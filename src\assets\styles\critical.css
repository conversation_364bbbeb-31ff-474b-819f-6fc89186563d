/* CRITICAL CSS - Загружается первым для мгновенного отображения */

*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: #2a2155;
  background-image: radial-gradient(circle at 30% 20%, rgba(85, 64, 174, 0.5) 0%, transparent 45%),
                    radial-gradient(circle at 80% 80%, rgba(123, 31, 162, 0.4) 0%, transparent 50%);
  color: #f8fafc;
  line-height: 1.6;
  overflow-x: hidden;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'kern' 1;
  transition: none;
  animation: none;
}

/* Определение класса gray-bg для предотвращения серого мигания */
body.gray-bg {
  background-color: #2a2155;
  background-image: radial-gradient(circle at 30% 20%, rgba(85, 64, 174, 0.5) 0%, transparent 45%),
                    radial-gradient(circle at 80% 80%, rgba(123, 31, 162, 0.4) 0%, transparent 50%);
}

/* Отключение переходов только для body и app-container */
body {
  transition: none;
  animation: none;
}

.app-container {
  transition: none;
  animation: none;
}

/* Принудительное включение анимаций только для уведомлений */
.notification-container .notification,
.notification-container {
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition: opacity 0.4s ease-out;
  animation: initial;
}

.notification-container .notification.swipe-out,
.notification-container .notification.fade-out {
  transition: transform 0.4s ease-in;
  transition: opacity 0.4s ease-in;
  animation: none;
}

/* КРИТИЧЕСКИЕ СТИЛИ - предотвращение серого мигания */
/* Все окна имеют одинаковый светлый многоцветный градиент */
body .settings-category,
body .action-card,
body .feature-card,
body .overview-hero {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.08) 0%,
    rgba(59, 130, 246, 0.06) 25%,
    rgba(139, 92, 246, 0.08) 50%,
    rgba(236, 72, 153, 0.06) 75%,
    rgba(245, 158, 11, 0.08) 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.08) !important;
  border-radius: 16px !important;
  backdrop-filter: blur(10px) !important;
  position: relative !important;
  overflow: hidden !important;
}



.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 250px;
  height: 100vh;
  background: linear-gradient(180deg, #16213e 0%, #0f172a 100%);
  z-index: 10;
  color: #cbd5e1;
  transform: translateZ(0);
  will-change: transform;
  contain: layout style paint;
}

.sidebar-menu-item {
  background: transparent;
  color: #cbd5e1;
  /* ИСПРАВЛЕНО: убираем transition для мгновенного переключения */
  border-radius: 8px;
}

.sidebar-menu-item.active {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
  color: #ffffff;
}

.hidden,
body .hidden,
.content-section.hidden,
#tank-list.hidden,
#tank-characteristics-container.hidden {
  display: none;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  position: absolute;
  left: -9999px;
  z-index: -1;
}

.section-visible {
  display: block;
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  position: relative;
  left: auto;
  z-index: auto;
}

.tank-list-visible {
  display: grid;
}

body:not([data-active-section="vehicles"]) #tank-list {
  display: none;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  position: absolute;
  left: -9999px;
  top: -9999px;
  z-index: -1000;
  width: 0;
  height: 0;
}

#overview-section:not(.hidden) ~ #tank-list,
#compare-section:not(.hidden) ~ #tank-list,
#settings-section:not(.hidden) ~ #tank-list {
  display: none;
  opacity: 0;
  visibility: hidden;
}

.loading {
  opacity: 0.5;
  pointer-events: none;
}

/* СТИЛИ СКРОЛЛБАРА УПРАВЛЯЮТСЯ В scrollbar-new.css */

/* Утилитарные классы управляются Tailwind CSS */

.error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.error-container {
  background: #1e293b;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  max-width: 400px;
  margin: 1rem;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-text {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: #f8fafc;
}

.error-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.error-button:hover {
  background: #1e40af;
}

#flag-section:not(.open) {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  visibility: hidden;
  pointer-events: none;
  display: block;
}

.flag-section,
.flag-list,
.type-list {
  background: transparent;
}

.transition-fast {
  transition: all 0.15s ease;
}

.transition-normal {
  transition: all 0.25s ease;
}

.transition-slow {
  transition: all 0.35s ease;
}

.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

#tank-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, 200px);
  gap: 0.5rem;
  padding: 1rem;
  margin: 0;
  width: 100%;
  justify-content: space-evenly;
}

/* Адаптивные размеры для 2K мониторов */
@media (min-width: 1920px) {
  #tank-list {
    grid-template-columns: repeat(auto-fill, 230px);
  }
}

.tank-item {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.6rem;
  display: flex;
  align-items: center;
  gap: 0.6rem;
  cursor: pointer;
  transition: transform 0.25s ease, box-shadow 0.25s ease;
  height: auto;
  min-height: 120px;
  max-height: 140px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.tank-item:hover {
  transform: translateY(-2px) translateZ(0);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.filter-item {
  transition: background-color 0.15s ease;
  cursor: pointer;
}

.filter-item:hover {
  background-color: rgba(139, 92, 246, 0.1);
}

.filter-item.active {
  background-color: rgba(139, 92, 246, 0.2);
}
