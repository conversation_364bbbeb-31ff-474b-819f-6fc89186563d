/**
 * УЛЬТРА-ОПТИМИЗИРОВАННЫЙ ПОИСКОВЫЙ ДВИЖОК
 * Максимальная производительность поиска и фильтрации
 */

class UltraSearchEngine {
  constructor() {
    this.searchIndex = new Map();
    this.filterIndex = {}; // Будет объектом с индексами
    this.sortedIndices = new Map();
    this.memoizedResults = new Map();
    this.maxCacheSize = 100;
    
    this.metrics = {
      searchTime: 0,
      filterTime: 0,
      cacheHits: 0,
      cacheMisses: 0,
      indexSize: 0
    };
  }

  /**
   * ПОСТРОЕНИЕ УЛЬТРА-БЫСТРОГО ИНДЕКСА
   */
  buildIndex(tanks) {
    const startTime = performance.now();

    // Очищаем старые индексы
    this.searchIndex.clear();
    // filterIndex будет перезаписан объектом, поэтому не очищаем его как Map
    this.sortedIndices.clear();
    this.memoizedResults.clear();
    
    // Строим поисковый индекс по именам
    this.buildNameIndex(tanks);
    
    // Строим индексы фильтров
    this.buildFilterIndices(tanks);
    
    // Строим отсортированные индексы
    this.buildSortedIndices(tanks);
    
    const buildTime = performance.now() - startTime;
    this.metrics.indexSize = tanks.length;
    
    console.log(`⚡ Search index built for ${tanks.length} tanks in ${buildTime.toFixed(2)}ms`);
  }

  /**
   * ПОСТРОЕНИЕ ИНДЕКСА ИМЕН (TRIE-СТРУКТУРА)
   */
  buildNameIndex(tanks) {
    const nameIndex = new Map();
    
    tanks.forEach(tank => {
      const name = tank.name.toLowerCase();
      
      // Индексируем каждую подстроку имени
      for (let i = 0; i < name.length; i++) {
        for (let j = i + 1; j <= name.length; j++) {
          const substring = name.slice(i, j);
          
          if (!nameIndex.has(substring)) {
            nameIndex.set(substring, new Set());
          }
          nameIndex.get(substring).add(tank);
        }
      }
      
      // Индексируем отдельные слова
      const words = name.split(/\s+/);
      words.forEach(word => {
        if (word.length > 0) {
          if (!nameIndex.has(word)) {
            nameIndex.set(word, new Set());
          }
          nameIndex.get(word).add(tank);
        }
      });
    });
    
    this.searchIndex = nameIndex;
  }

  /**
   * ПОСТРОЕНИЕ ИНДЕКСОВ ФИЛЬТРОВ
   */
  buildFilterIndices(tanks) {
    const countryIndex = new Map();
    const typeIndex = new Map();
    const levelIndex = new Map();
    const combinedIndex = new Map();
    
    tanks.forEach(tank => {
      // Индекс по странам
      if (!countryIndex.has(tank.country)) {
        countryIndex.set(tank.country, []);
      }
      countryIndex.get(tank.country).push(tank);
      
      // Индекс по типам
      if (!typeIndex.has(tank.type)) {
        typeIndex.set(tank.type, []);
      }
      typeIndex.get(tank.type).push(tank);
      
      // Индекс по уровням
      if (!levelIndex.has(tank.level)) {
        levelIndex.set(tank.level, []);
      }
      levelIndex.get(tank.level).push(tank);
      
      // Комбинированный индекс
      const key = `${tank.country}:${tank.type}:${tank.level}`;
      if (!combinedIndex.has(key)) {
        combinedIndex.set(key, []);
      }
      combinedIndex.get(key).push(tank);
    });
    
    this.filterIndex = {
      country: countryIndex,
      type: typeIndex,
      level: levelIndex,
      combined: combinedIndex
    };
  }

  /**
   * ПОСТРОЕНИЕ ОТСОРТИРОВАННЫХ ИНДЕКСОВ
   */
  buildSortedIndices(tanks) {
    // Сортировка по имени
    this.sortedIndices.set('name', [...tanks].sort((a, b) => a.name.localeCompare(b.name)));
    
    // Сортировка по уровню
    this.sortedIndices.set('level', [...tanks].sort((a, b) => b.level - a.level));
    
    // Сортировка по стране
    this.sortedIndices.set('country', [...tanks].sort((a, b) => a.country.localeCompare(b.country)));
    
    // Сортировка по типу
    this.sortedIndices.set('type', [...tanks].sort((a, b) => a.type.localeCompare(b.type)));
  }

  /**
   * УЛЬТРА-БЫСТРЫЙ ПОИСК
   */
  search(query, maxResults = 50) {
    if (!query || query.length === 0) return [];
    
    const startTime = performance.now();
    const normalizedQuery = query.toLowerCase().trim();
    
    // Проверяем кэш
    const cacheKey = `search:${normalizedQuery}:${maxResults}`;
    if (this.memoizedResults.has(cacheKey)) {
      this.metrics.cacheHits++;
      return this.memoizedResults.get(cacheKey);
    }
    
    let results = new Set();
    
    // Точное совпадение имеет наивысший приоритет
    if (this.searchIndex.has(normalizedQuery)) {
      this.searchIndex.get(normalizedQuery).forEach(tank => results.add(tank));
    }
    
    // Поиск по началу слов
    const words = normalizedQuery.split(/\s+/);
    words.forEach(word => {
      for (const [key, tanks] of this.searchIndex) {
        if (key.startsWith(word) && key.length > word.length) {
          tanks.forEach(tank => results.add(tank));
        }
      }
    });
    
    // Поиск по подстрокам (если результатов мало)
    if (results.size < maxResults) {
      for (const [key, tanks] of this.searchIndex) {
        if (key.includes(normalizedQuery)) {
          tanks.forEach(tank => results.add(tank));
          if (results.size >= maxResults) break;
        }
      }
    }
    
    const finalResults = Array.from(results).slice(0, maxResults);
    
    // Кэшируем результат
    this.cacheResult(cacheKey, finalResults);
    
    this.metrics.searchTime = performance.now() - startTime;
    this.metrics.cacheMisses++;
    
    return finalResults;
  }

  /**
   * УЛЬТРА-БЫСТРАЯ ФИЛЬТРАЦИЯ
   */
  filter(filters) {
    const startTime = performance.now();
    const { country, type, level, tiers } = filters;
    
    // Создаем ключ кэша
    const cacheKey = `filter:${country}:${type}:${level}:${tiers?.join(',') || 'all'}`;
    if (this.memoizedResults.has(cacheKey)) {
      this.metrics.cacheHits++;
      return this.memoizedResults.get(cacheKey);
    }
    
    let results;
    
    // Оптимизация: используем комбинированный индекс если возможно
    if (country !== 'all' && type !== 'all' && level !== 'all') {
      const combinedKey = `${country}:${type}:${level}`;
      results = this.filterIndex.combined.get(combinedKey) || [];
    } else {
      // Начинаем с наименьшего набора
      if (country !== 'all' && this.filterIndex.country.has(country)) {
        results = this.filterIndex.country.get(country);
      } else if (type !== 'all' && this.filterIndex.type.has(type)) {
        results = this.filterIndex.type.get(type);
      } else if (level !== 'all' && this.filterIndex.level.has(level)) {
        results = this.filterIndex.level.get(level);
      } else {
        // Возвращаем все танки из отсортированного индекса
        results = this.sortedIndices.get('name') || [];
      }
      
      // Применяем дополнительные фильтры
      if (country !== 'all') {
        results = results.filter(tank => tank.country === country);
      }
      if (type !== 'all') {
        results = results.filter(tank => tank.type === type);
      }
      if (level !== 'all') {
        results = results.filter(tank => tank.level === level);
      }
    }
    
    // Фильтрация по уровням (tiers)
    if (tiers && tiers.length > 0 && tiers.length < 5) {
      const tierLevels = tiers.map(tier => {
        switch (tier) {
          case 'VI': return 6;
          case 'VII': return 7;
          case 'VIII': return 8;
          case 'IX': return 9;
          case 'X': return 10;
          default: return 0;
        }
      });
      results = results.filter(tank => tierLevels.includes(tank.level));
    }
    
    // Кэшируем результат
    this.cacheResult(cacheKey, results);
    
    this.metrics.filterTime = performance.now() - startTime;
    this.metrics.cacheMisses++;
    
    return results;
  }

  /**
   * КОМБИНИРОВАННЫЙ ПОИСК И ФИЛЬТРАЦИЯ
   */
  searchAndFilter(query, filters, maxResults = 50) {
    if (query && query.length > 0) {
      const searchResults = this.search(query, maxResults * 2);
      return searchResults.filter(tank => this.matchesFilters(tank, filters)).slice(0, maxResults);
    } else {
      return this.filter(filters).slice(0, maxResults);
    }
  }

  /**
   * ПРОВЕРКА СООТВЕТСТВИЯ ФИЛЬТРАМ
   */
  matchesFilters(tank, filters) {
    const { country, type, level, tiers } = filters;
    
    if (country !== 'all' && tank.country !== country) return false;
    if (type !== 'all' && tank.type !== type) return false;
    if (level !== 'all' && tank.level !== level) return false;
    
    if (tiers && tiers.length > 0 && tiers.length < 5) {
      const tierLevels = tiers.map(tier => {
        switch (tier) {
          case 'VI': return 6;
          case 'VII': return 7;
          case 'VIII': return 8;
          case 'IX': return 9;
          case 'X': return 10;
          default: return 0;
        }
      });
      if (!tierLevels.includes(tank.level)) return false;
    }
    
    return true;
  }

  /**
   * КЭШИРОВАНИЕ РЕЗУЛЬТАТОВ
   */
  cacheResult(key, result) {
    if (this.memoizedResults.size >= this.maxCacheSize) {
      // Удаляем старые записи (LRU)
      const firstKey = this.memoizedResults.keys().next().value;
      this.memoizedResults.delete(firstKey);
    }
    
    this.memoizedResults.set(key, result);
  }

  /**
   * ОЧИСТКА КЭША
   */
  clearCache() {
    this.memoizedResults.clear();
  }

  /**
   * ПОЛУЧЕНИЕ МЕТРИК
   */
  getMetrics() {
    return {
      ...this.metrics,
      cacheSize: this.memoizedResults.size,
      hitRate: this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) || 0
    };
  }
}

// Создаем глобальный экземпляр
export const ultraSearchEngine = new UltraSearchEngine();

export default ultraSearchEngine;
