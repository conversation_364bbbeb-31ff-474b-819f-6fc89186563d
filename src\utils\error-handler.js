/**
 * HARVARD-LEVEL ERROR HANDLER
 * Профессиональная система обработки ошибок с IQ 300
 */

class UltraErrorHandler {
  constructor() {
    this.errorCount = 0;
    this.lastErrorTime = 0;
    this.errorThreshold = 5; // Максимум 5 ошибок в минуту
    this.isInitialized = false;
    
    this.init();
  }

  init() {
    if (this.isInitialized) return;
    
    // Глобальные обработчики ошибок
    window.addEventListener('error', this.handleError.bind(this));
    window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this));
    
    // Обработчик ошибок модулей
    window.addEventListener('vite:error', this.handleViteError.bind(this));
    
    this.isInitialized = true;
    console.log('🛡️ UltraErrorHandler initialized');
  }

  handleError(event) {
    const now = Date.now();
    
    // Фильтруем ошибки расширений браузера
    if (this.isBrowserExtensionError(event)) {
      return;
    }
    
    // Предотвращаем спам ошибок
    if (this.isErrorSpam(now)) {
      return;
    }
    
    const errorInfo = {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error,
      timestamp: now,
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    this.logError('JavaScript Error', errorInfo);
    this.showUserFriendlyError(errorInfo);
  }

  handlePromiseRejection(event) {
    const now = Date.now();
    
    if (this.isErrorSpam(now)) {
      return;
    }
    
    const errorInfo = {
      reason: event.reason,
      promise: event.promise,
      timestamp: now,
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    this.logError('Unhandled Promise Rejection', errorInfo);
    
    // Предотвращаем показ ошибки в консоли браузера
    event.preventDefault();
  }

  handleViteError(event) {
    console.error('🚨 Vite Error:', event.detail);
    
    // Попытка перезагрузки модуля
    if (event.detail && event.detail.id) {
      this.attemptModuleReload(event.detail.id);
    }
  }

  isBrowserExtensionError(event) {
    const filename = event.filename || '';
    return filename.includes('extension://') || 
           filename.includes('chrome-extension://') || 
           filename.includes('moz-extension://');
  }

  isErrorSpam(now) {
    if (now - this.lastErrorTime < 60000) { // В течение минуты
      this.errorCount++;
      if (this.errorCount > this.errorThreshold) {
        return true;
      }
    } else {
      this.errorCount = 1;
    }
    
    this.lastErrorTime = now;
    return false;
  }

  logError(type, errorInfo) {
    console.group(`🚨 ${type}`);
    console.error('Error Info:', errorInfo);
    console.error('Stack Trace:', errorInfo.error?.stack);
    console.groupEnd();
    
    // В production можно отправлять на сервер
    if (window.location.hostname !== 'localhost') {
      this.sendErrorToServer(type, errorInfo);
    }
  }

  showUserFriendlyError(errorInfo) {
    // Показываем пользователю дружелюбное сообщение
    const errorContainer = this.getOrCreateErrorContainer();
    
    const errorElement = document.createElement('div');
    errorElement.className = 'error-notification';
    errorElement.innerHTML = `
      <div class="error-content">
        <h4>Произошла ошибка</h4>
        <p>Мы работаем над исправлением. Попробуйте обновить страницу.</p>
        <button onclick="window.location.reload()" class="error-reload-btn">
          Обновить страницу
        </button>
        <button onclick="this.parentElement.parentElement.remove()" class="error-close-btn">
          ✕
        </button>
      </div>
    `;
    
    errorContainer.appendChild(errorElement);
    
    // Автоматически скрываем через 10 секунд
    setTimeout(() => {
      if (errorElement.parentElement) {
        errorElement.remove();
      }
    }, 10000);
  }

  getOrCreateErrorContainer() {
    let container = document.getElementById('error-notifications');
    
    if (!container) {
      container = document.createElement('div');
      container.id = 'error-notifications';
      container.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
      `;
      
      // Добавляем стили
      this.addErrorStyles();
      
      document.body.appendChild(container);
    }
    
    return container;
  }

  addErrorStyles() {
    if (document.getElementById('error-handler-styles')) return;
    
    const styles = document.createElement('style');
    styles.id = 'error-handler-styles';
    styles.textContent = `
      .error-notification {
        background: #ff6b6b;
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease-out;
      }
      
      .error-content h4 {
        margin: 0 0 0.5rem 0;
        font-size: 1.1rem;
      }
      
      .error-content p {
        margin: 0 0 1rem 0;
        opacity: 0.9;
      }
      
      .error-reload-btn, .error-close-btn {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 0.5rem;
        font-size: 0.9rem;
      }
      
      .error-reload-btn:hover, .error-close-btn:hover {
        background: rgba(255,255,255,0.3);
      }
      
      .error-close-btn {
        padding: 0.5rem;
        margin-right: 0;
        float: right;
      }
      
      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
    `;
    
    document.head.appendChild(styles);
  }

  attemptModuleReload(moduleId) {
    // Попытка перезагрузки через динамический импорт
    setTimeout(() => {
      /* @vite-ignore */
      import(moduleId + '?t=' + Date.now())
        .then(() => {
          // Module reloaded successfully
        })
        .catch(error => {
          console.error(`Failed to reload module: ${moduleId}`, error);
        });
    }, 1000);
  }

  sendErrorToServer(type, errorInfo) {
    // В production отправляем ошибки на сервер
    fetch('/api/errors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type,
        errorInfo,
        timestamp: Date.now()
      })
    }).catch(() => {
      // Тихо игнорируем ошибки отправки
    });
  }

  // Публичные методы для ручной обработки ошибок
  reportError(message, details = {}) {
    this.logError('Manual Report', {
      message,
      details,
      timestamp: Date.now(),
      url: window.location.href
    });
  }

  clearErrors() {
    const container = document.getElementById('error-notifications');
    if (container) {
      container.innerHTML = '';
    }
    this.errorCount = 0;
  }
}

// Экспортируем синглтон
export const ultraErrorHandler = new UltraErrorHandler();

// Утилиты для быстрого использования
export const reportError = (message, details) => ultraErrorHandler.reportError(message, details);
export const clearErrors = () => ultraErrorHandler.clearErrors();
