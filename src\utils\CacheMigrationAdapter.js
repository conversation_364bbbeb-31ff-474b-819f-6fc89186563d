/**
 * ОПТИМИЗИРОВАННАЯ СИСТЕМА КЭШИРОВАНИЯ
 * Унифицированная система с интеллектуальной очисткой и метриками производительности
 */

// Типы кэша
const CACHE_TYPES = {
  DOM_ELEMENTS: 'dom_elements',
  FILTER_RESULTS: 'filter_results',
  TANK_DATA: 'tank_data',
  COMPUTED: 'computed'
};

// Конфигурация кэша
const CACHE_CONFIG = {
  maxSize: 1000,
  maxAge: 600000, // 10 минут
  cleanupInterval: 300000 // 5 минут
};

/**
 * ИНТЕЛЛЕКТУАЛЬНАЯ СИСТЕМА КЭШИРОВАНИЯ
 */
class IntelligentCache {
  constructor() {
    this.cache = new Map();
    this.metadata = new Map();
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      cleanups: 0,
      lastCleanup: Date.now()
    };

    this.setupAutoCleanup();
  }

  get(type, key) {
    const cacheKey = `${type}:${key}`;
    const entry = this.cache.get(cacheKey);

    if (!entry) {
      this.metrics.misses++;
      return undefined;
    }

    const meta = this.metadata.get(cacheKey);
    if (meta && Date.now() - meta.timestamp > CACHE_CONFIG.maxAge) {
      this.delete(type, key);
      this.metrics.misses++;
      return undefined;
    }

    // Обновляем время последнего доступа
    if (meta) {
      meta.lastAccess = Date.now();
      meta.accessCount++;
    }

    this.metrics.hits++;
    return entry;
  }

  set(type, key, value) {
    const cacheKey = `${type}:${key}`;

    // Проверяем размер кэша
    if (this.cache.size >= CACHE_CONFIG.maxSize) {
      this.evictLeastUsed();
    }

    this.cache.set(cacheKey, value);
    this.metadata.set(cacheKey, {
      timestamp: Date.now(),
      lastAccess: Date.now(),
      accessCount: 1,
      type,
      size: this.estimateSize(value)
    });

    this.metrics.sets++;
  }

  has(type, key) {
    return this.cache.has(`${type}:${key}`);
  }

  delete(type, key) {
    const cacheKey = `${type}:${key}`;
    this.metadata.delete(cacheKey);
    return this.cache.delete(cacheKey);
  }

  clear(type) {
    if (type) {
      for (const key of this.cache.keys()) {
        if (key.startsWith(`${type}:`)) {
          this.cache.delete(key);
          this.metadata.delete(key);
        }
      }
    } else {
      this.cache.clear();
      this.metadata.clear();
    }
  }

  // Интеллектуальная очистка неиспользуемых элементов
  evictLeastUsed() {
    const entries = Array.from(this.metadata.entries())
      .sort((a, b) => {
        const scoreA = a[1].accessCount / (Date.now() - a[1].lastAccess);
        const scoreB = b[1].accessCount / (Date.now() - b[1].lastAccess);
        return scoreA - scoreB;
      });

    // Удаляем 20% наименее используемых элементов
    const toRemove = Math.ceil(entries.length * 0.2);
    for (let i = 0; i < toRemove; i++) {
      const [key] = entries[i];
      this.cache.delete(key);
      this.metadata.delete(key);
    }
  }

  // Автоматическая очистка устаревших элементов
  setupAutoCleanup() {
    setInterval(() => {
      this.cleanupExpired();
    }, CACHE_CONFIG.cleanupInterval);
  }

  cleanupExpired() {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, meta] of this.metadata.entries()) {
      if (now - meta.timestamp > CACHE_CONFIG.maxAge) {
        this.cache.delete(key);
        this.metadata.delete(key);
        cleaned++;
      }
    }

    this.metrics.cleanups++;
    this.metrics.lastCleanup = now;

    if (cleaned > 0) {
      console.log(`🧹 Cache cleanup: removed ${cleaned} expired entries`);
    }
  }

  // Оценка размера объекта
  estimateSize(obj) {
    if (typeof obj === 'string') return obj.length * 2;
    if (typeof obj === 'number') return 8;
    if (typeof obj === 'boolean') return 4;
    if (obj instanceof HTMLElement) return 100; // Примерная оценка
    if (Array.isArray(obj)) return obj.length * 50; // Примерная оценка
    return 100; // Дефолтная оценка для объектов
  }

  getMetrics() {
    const hitRate = this.metrics.hits / (this.metrics.hits + this.metrics.misses) || 0;
    return {
      ...this.metrics,
      hitRate: Math.round(hitRate * 100),
      cacheSize: this.cache.size,
      memoryUsage: Array.from(this.metadata.values())
        .reduce((sum, meta) => sum + meta.size, 0)
    };
  }
}

// Создаем единственный экземпляр
const intelligentCache = new IntelligentCache();

/**
 * ОПТИМИЗИРОВАННЫЙ АДАПТЕР ДЛЯ DOM КЭША
 */
export class OptimizedDOMCache {
  constructor() {
    this.cacheType = CACHE_TYPES.DOM_ELEMENTS;
  }

  // Оптимизированное получение элементов с проверкой валидности
  getCachedElement(selector) {
    let element = intelligentCache.get(this.cacheType, selector);

    // Проверяем, что элемент все еще в DOM
    if (element && document.contains(element)) {
      return element;
    }

    // Если элемент не найден или не в DOM, ищем заново
    element = document.querySelector(selector);
    if (element) {
      intelligentCache.set(this.cacheType, selector, element);
    }

    return element;
  }

  // Массовое кэширование элементов
  cacheElements(selectors) {
    const elements = {};
    selectors.forEach(selector => {
      const element = this.getCachedElement(selector);
      if (element) {
        elements[selector] = element;
      }
    });
    return elements;
  }

  // Очистка DOM кэша
  clearElementCache() {
    intelligentCache.clear(this.cacheType);
  }

  // Получение статистики
  getStats() {
    return intelligentCache.getMetrics();
  }
}

/**
 * ОПТИМИЗИРОВАННЫЙ АДАПТЕР ДЛЯ ФИЛЬТР КЭША
 */
export class OptimizedFilterCache {
  constructor() {
    this.cacheType = CACHE_TYPES.FILTER_RESULTS;
  }

  // Оптимизированное получение отфильтрованных танков
  getFilteredTanks(filterKey) {
    return intelligentCache.get(this.cacheType, filterKey);
  }

  setFilteredTanks(filterKey, tanks) {
    return intelligentCache.set(this.cacheType, filterKey, tanks);
  }

  // Совместимость с TankManager.js
  getCachedFilterResult(filterKey) {
    return this.getFilteredTanks(filterKey);
  }

  setCachedFilterResult(filterKey, result) {
    return this.setFilteredTanks(filterKey, result);
  }

  // Генерация ключа кэша на основе фильтров
  generateFilterKey(filters) {
    const { country, category, tiers, searchQuery } = filters;
    return `${country || 'all'}_${category || 'all'}_${(tiers || []).sort().join(',')}_${searchQuery || ''}`;
  }

  // Очистка фильтр кэша
  clearFilterCache() {
    intelligentCache.clear(this.cacheType);
  }

  // Получение статистики
  getFilterCacheStats() {
    const metrics = intelligentCache.getMetrics();
    return {
      size: metrics.cacheSize,
      hitRate: metrics.hitRate,
      hits: metrics.hits,
      misses: metrics.misses
    };
  }
}

/**
 * УНИВЕРСАЛЬНЫЙ АДАПТЕР ДЛЯ ДАННЫХ
 */
export class UniversalDataCache {
  constructor() {
    this.tankDataType = CACHE_TYPES.TANK_DATA;
    this.computedType = CACHE_TYPES.COMPUTED;
  }

  // Кэширование данных танка
  cacheTankData(tankId, data) {
    return intelligentCache.set(this.tankDataType, tankId, data);
  }

  // Получение данных танка
  getTankData(tankId) {
    return intelligentCache.get(this.tankDataType, tankId);
  }

  // Кэширование вычисляемых значений
  cacheComputed(key, value) {
    return intelligentCache.set(this.computedType, key, value);
  }

  // Получение вычисляемого значения
  getComputed(key) {
    return intelligentCache.get(this.computedType, key);
  }

  // Кэширование с функцией вычисления
  getOrCompute(key, computeFn) {
    let value = this.getComputed(key);

    if (value === undefined) {
      value = computeFn();
      this.cacheComputed(key, value);
    }

    return value;
  }

  // Очистка всех данных
  clearAll() {
    intelligentCache.clear(this.tankDataType);
    intelligentCache.clear(this.computedType);
  }
}

// Создаем экземпляры оптимизированных адаптеров
export const domCache = new OptimizedDOMCache();
export const filterCache = new OptimizedFilterCache();
export const dataCache = new UniversalDataCache();

// Главный объект для управления всеми кэшами
export const cacheManager = {
  dom: domCache,
  filter: filterCache,
  data: dataCache,

  // Получение общей статистики
  getStats() {
    return intelligentCache.getMetrics();
  },

  // Полная очистка всех кэшей
  clearAll() {
    intelligentCache.clear();
  },

  // Принудительная очистка устаревших элементов
  cleanup() {
    intelligentCache.cleanupExpired();
  }
};

// Экспортируем для обратной совместимости
export const cacheAdapter = cacheManager;

// Глобальный доступ для отладки
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.cacheManager = cacheManager;
  window.intelligentCache = intelligentCache;
}
