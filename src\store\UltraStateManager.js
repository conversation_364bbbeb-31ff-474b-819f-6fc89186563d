/**
 * УНИФИЦИРОВАННЫЙ МЕНЕДЖЕР СОСТОЯНИЯ
 * Объединяет функциональность state.js и UltraStateManager для устранения дублирования
 * Оптимизирован для максимальной производительности и минимального потребления памяти
 */

import { state as baseState } from './state.js';

class UnifiedStateManager {
  constructor() {
    // Используем существующий state как основу для устранения дублирования
    this.state = baseState;

    // Оптимизированная система подписок
    this.subscribers = new Map();
    this.computedCache = new Map();
    this.batchedUpdates = new Set();
    this.updateScheduled = false;

    // Упрощенные метрики для мониторинга производительности
    this.metrics = {
      stateUpdates: 0,
      subscriberNotifications: 0,
      batchedUpdates: 0,
      cacheHits: 0,
      cacheMisses: 0,
      lastCleanup: Date.now()
    };

    // Настройка автоматической очистки кэша
    this.setupCacheCleanup();
  }

  /**
   * НАСТРОЙКА АВТОМАТИЧЕСКОЙ ОЧИСТКИ КЭША
   * Используем глобальный менеджер очистки для избежания дублирования
   */
  setupCacheCleanup() {
    // Регистрируем функцию очистки в глобальном менеджере
    if (window.globalCleanupManager) {
      window.globalCleanupManager.registerCleanupFunction('stateManager', () => {
        this.cleanupCache();
      });
    } else {
      // Fallback если глобальный менеджер не доступен
      setInterval(() => {
        this.cleanupCache();
      }, 300000);
    }

    // Очистка при скрытии страницы
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.cleanupCache();
      }
    });
  }

  /**
   * ИНТЕЛЛЕКТУАЛЬНАЯ ОЧИСТКА КЭША
   */
  cleanupCache() {
    const now = Date.now();
    const maxAge = 600000; // 10 минут

    // Очищаем устаревшие computed значения
    for (const [key, entry] of this.computedCache.entries()) {
      if (now - entry.timestamp > maxAge) {
        this.computedCache.delete(key);
      }
    }

    this.metrics.lastCleanup = now;
    console.log('🧹 Cache cleanup completed');
  }

  /**
   * ПОЛУЧЕНИЕ ЗНАЧЕНИЯ ИЗ СОСТОЯНИЯ (делегируется к базовому state)
   */
  get(property) {
    return this.state[property];
  }

  /**
   * УСТАНОВКА ЗНАЧЕНИЯ В СОСТОЯНИЕ (оптимизированная версия)
   */
  set(property, value) {
    const oldValue = this.state[property];

    // Быстрая проверка равенства для примитивов
    if (oldValue === value) {
      return;
    }

    // Для объектов используем поверхностное сравнение (быстрее глубокого)
    if (typeof oldValue === 'object' && typeof value === 'object' &&
        oldValue !== null && value !== null) {
      if (this.shallowEqual(oldValue, value)) {
        return;
      }
    }

    this.state[property] = value;
    this.metrics.stateUpdates++;

    // Очищаем связанные computed значения
    this.invalidateComputedCache(property);
  }

  /**
   * БЫСТРОЕ ПОВЕРХНОСТНОЕ СРАВНЕНИЕ (оптимизировано для производительности)
   */
  shallowEqual(a, b) {
    if (Array.isArray(a) && Array.isArray(b)) {
      return a.length === b.length && a.every((item, index) => item === b[index]);
    }

    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    return keysA.length === keysB.length && keysA.every(key => a[key] === b[key]);
  }

  /**
   * ПОДПИСКА НА ИЗМЕНЕНИЯ СОСТОЯНИЯ
   */
  subscribe(property, callback, options = {}) {
    if (!this.subscribers.has(property)) {
      this.subscribers.set(property, new Set());
    }
    
    const subscription = {
      callback,
      immediate: options.immediate || false,
      debounce: options.debounce || 0,
      lastCall: 0
    };
    
    this.subscribers.get(property).add(subscription);
    
    // Немедленный вызов если запрошен
    if (subscription.immediate) {
      callback(this.state[property], undefined);
    }
    
    // Возвращаем функцию отписки
    return () => {
      this.subscribers.get(property)?.delete(subscription);
    };
  }

  /**
   * УВЕДОМЛЕНИЕ ПОДПИСЧИКОВ
   */
  notifySubscribers(property, newValue, oldValue) {
    const propertySubscribers = this.subscribers.get(property);
    if (!propertySubscribers) return;
    
    const now = performance.now();
    
    propertySubscribers.forEach(subscription => {
      // Проверяем debounce
      if (subscription.debounce > 0) {
        if (now - subscription.lastCall < subscription.debounce) {
          return;
        }
        subscription.lastCall = now;
      }
      
      try {
        subscription.callback(newValue, oldValue);
        this.metrics.subscriberNotifications++;
      } catch (error) {
        console.error(`Error in state subscriber for ${property}:`, error);
      }
    });
  }

  /**
   * БАТЧИРОВАННОЕ ОБНОВЛЕНИЕ СОСТОЯНИЯ (оптимизированная версия)
   */
  batchUpdate(updates) {
    // Используем встроенный batchUpdate из базового state для избежания дублирования
    if (this.state.batchUpdate) {
      this.state.batchUpdate(updates);
      this.metrics.batchedUpdates++;
    } else {
      // Fallback: прямое обновление без дополнительного батчинга
      Object.entries(updates).forEach(([key, value]) => {
        this.set(key, value);
      });
      this.metrics.batchedUpdates++;
    }
  }

  /**
   * ОЧИСТКА COMPUTED КЭША ПРИ ИЗМЕНЕНИИ ЗАВИСИМОСТЕЙ
   */
  invalidateComputedCache(property) {
    // Удаляем все computed значения, которые зависят от изменившегося свойства
    for (const [key, entry] of this.computedCache.entries()) {
      if (entry.dependencies && entry.dependencies.includes(property)) {
        this.computedCache.delete(key);
      }
    }
  }

  /**
   * ОПРЕДЕЛЕНИЕ ГРУППЫ ОБНОВЛЕНИЙ
   */
  getUpdateGroup(property) {
    if (['selectedCountry', 'selectedCategory', 'selectedTiers', 'searchQuery'].includes(property)) {
      return 'filters';
    }
    if (['theme', 'language', 'fontSize'].includes(property)) {
      return 'settings';
    }
    if (['comparedTanks', 'isCompareWindowVisible'].includes(property)) {
      return 'compare';
    }
    return 'general';
  }

  /**
   * ВЫЧИСЛЯЕМЫЕ СВОЙСТВА С КЭШИРОВАНИЕМ (оптимизированная версия)
   */
  computed(name, computeFn, dependencies = []) {
    const cacheKey = `${name}:${dependencies.map(dep => this.state[dep]).join(':')}`;

    // Проверяем кэш
    const cached = this.computedCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp < 300000)) { // 5 минут TTL
      this.metrics.cacheHits++;
      return cached.value;
    }

    // Вычисляем новое значение
    const result = computeFn(this.state);
    this.computedCache.set(cacheKey, {
      value: result,
      timestamp: Date.now(),
      dependencies
    });
    this.metrics.cacheMisses++;

    return result;
  }

  /**
   * ОБНОВЛЕНИЕ СОСТОЯНИЯ
   */
  update(property, updater) {
    const currentValue = this.state[property];
    const newValue = typeof updater === 'function' ? updater(currentValue) : updater;
    this.set(property, newValue);
  }

  /**
   * СБРОС СОСТОЯНИЯ (использует метод базового state)
   */
  reset(properties = null) {
    if (this.state.reset && !properties) {
      // Используем встроенный reset для полного сброса
      this.state.reset();
    } else if (properties) {
      // Частичный сброс
      properties.forEach(prop => {
        delete this.state[prop];
      });
    } else {
      // Fallback для полного сброса
      Object.keys(this.state).forEach(key => {
        if (!key.startsWith('_')) { // Сохраняем служебные свойства
          delete this.state[key];
        }
      });
    }

    // Очищаем кэши
    this.computedCache.clear();
    this.metrics.stateUpdates++;
  }

  /**
   * СЕРИАЛИЗАЦИЯ СОСТОЯНИЯ
   */
  serialize(properties = null) {
    const stateToSerialize = properties 
      ? Object.fromEntries(properties.map(prop => [prop, this.state[prop]]))
      : this.state;
      
    return JSON.stringify(stateToSerialize);
  }

  /**
   * ДЕСЕРИАЛИЗАЦИЯ СОСТОЯНИЯ
   */
  deserialize(serializedState) {
    try {
      const parsedState = JSON.parse(serializedState);
      Object.entries(parsedState).forEach(([key, value]) => {
        this.state[key] = value;
      });
    } catch (error) {
      console.error('Failed to deserialize state:', error);
    }
  }

  /**
   * СОХРАНЕНИЕ В ЛОКАЛЬНОЕ ХРАНИЛИЩЕ
   */
  persist(key = 'app-state', properties = null) {
    try {
      const serialized = this.serialize(properties);
      localStorage.setItem(key, serialized);
    } catch (error) {
      console.error('Failed to persist state:', error);
    }
  }

  /**
   * ЗАГРУЗКА ИЗ ЛОКАЛЬНОГО ХРАНИЛИЩА
   */
  restore(key = 'app-state') {
    try {
      const serialized = localStorage.getItem(key);
      if (serialized) {
        this.deserialize(serialized);
      }
    } catch (error) {
      console.error('Failed to restore state:', error);
    }
  }

  /**
   * ОТЛАДОЧНАЯ ИНФОРМАЦИЯ
   */
  debug() {
    return {
      state: { ...this.state },
      subscribers: Object.fromEntries(
        Array.from(this.subscribers.entries()).map(([key, subs]) => [key, subs.size])
      ),
      computedCacheSize: this.computedCache.size,
      metrics: { ...this.metrics }
    };
  }

  /**
   * ОЧИСТКА РЕСУРСОВ
   */
  cleanup() {
    this.subscribers.clear();
    this.computedCache.clear();
    this.batchedUpdates.clear();
  }
}

// Создаем единственный экземпляр унифицированного менеджера состояния
export const unifiedStateManager = new UnifiedStateManager();

// Экспортируем для обратной совместимости
export const ultraStateManager = unifiedStateManager;

// Оптимизированные хуки для работы с состоянием
export const useState = (property, defaultValue) => {
  if (unifiedStateManager.get(property) === undefined && defaultValue !== undefined) {
    unifiedStateManager.set(property, defaultValue);
  }
  return [
    unifiedStateManager.get(property),
    (value) => unifiedStateManager.set(property, value)
  ];
};

export const useComputed = (name, computeFn, dependencies) => {
  return unifiedStateManager.computed(name, computeFn, dependencies);
};

export const useSubscribe = (property, callback, options) => {
  return unifiedStateManager.subscribe(property, callback, options);
};

// Экспортируем базовое состояние для прямого доступа
export const state = unifiedStateManager.state;

export default unifiedStateManager;

// Глобальный доступ для отладки (только в development)
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.unifiedStateManager = unifiedStateManager;
  window.ultraStateManager = unifiedStateManager; // Для совместимости
}
