/* ======================================== 
   TANK LIST & CARDS
   ======================================== */

/* Tank list container - ОПТИМИЗИРОВАННЫЙ GRID LAYOUT */
#tank-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, 200px);
  gap: 0.5rem;
  padding: 1rem;
  margin: 0;
  width: 100%;
  justify-content: space-evenly;

  /* Стабильность отображения */
  opacity: 1;
  visibility: visible;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  min-height: 200px;
  height: auto;

  /* Производительность */
  contain: layout style;
  content-visibility: auto;
  contain-intrinsic-size: auto 1000px;

  /* GPU ускорение */
  transform: translateZ(0);
  backface-visibility: hidden;

  /* Позиционирование */
  position: relative;
  z-index: 1;
}

/* СОСТОЯНИЯ СПИСКА ТАНКОВ - ОПТИМИЗИРОВАННЫЕ */
#tank-list.tank-list-loading,
#tank-list.tank-list-visible,
#tank-list.force-visible {
  opacity: 1;
  visibility: visible;
  display: grid;
  height: auto;
  min-height: 200px;

  /* Восстановление grid layout */
  grid-template-columns: repeat(auto-fill, 200px);
  gap: 0.5rem;
  padding: 1rem;
  justify-content: space-evenly;

  /* Производительность */
  contain: layout style;
  content-visibility: auto;
}

/* СОСТОЯНИЕ РЕНДЕРИНГА - ОПТИМИЗИРОВАННОЕ */
#tank-list.rendering {
  contain: layout style;
  will-change: contents;
  transition: none;
  animation: none;

  /* Сохраняем grid layout во время рендеринга */
  display: grid;
  grid-template-columns: repeat(auto-fill, 200px);
  gap: 0.5rem;
}

/* РЕЗЕРВНЫЕ ПРАВИЛА ДЛЯ СТАБИЛЬНОСТИ */
.main-content-container #tank-list,
.content-section#tank-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, 200px);
  gap: 0.5rem;
  padding: 1rem;
  width: 100%;
  justify-content: space-evenly;
}

/* ДОПОЛНИТЕЛЬНАЯ ЗАЩИТА ОТ ПОЛОМКИ GRID LAYOUT */
#tank-list:not(.hidden) {
  display: grid;
  grid-template-columns: repeat(auto-fill, 200px);
  gap: 0.5rem;
  padding: 1rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  justify-content: space-evenly;
}

/* ЗАЩИТА ОТ КОНФЛИКТОВ С ДРУГИМИ СТИЛЯМИ */
#tank-list.tank-list-visible {
  display: grid;
  grid-template-columns: repeat(auto-fill, 200px);
  width: 100%;
  justify-content: space-evenly;
}

/* ACCESSIBILITY: Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ACCESSIBILITY: Focus indicators */
.tank-item:focus {
  outline: 2px solid #007acc;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(0, 122, 204, 0.2);
}

/* ACCESSIBILITY: High contrast mode support */
@media (prefers-contrast: high) {
  .tank-item {
    border: 2px solid;
  }

  .tank-name {
    font-weight: bold;
  }
}



/* HARVARD-LEVEL СТАБИЛИЗАЦИЯ ДОЧЕРНИХ ЭЛЕМЕНТОВ ВО ВРЕМЯ РЕНДЕРИНГА */
#tank-list.rendering .tank-item {
  transition: none;
  animation: none;
  will-change: auto;
}

/* МГНОВЕННОЕ скрытие - ТОЛЬКО ДЛЯ ДРУГИХ ВКЛАДОК */
body #tank-list.tank-list-hidden {
  opacity: 0;
  visibility: hidden;
  display: none;
}

/* Убираем скрытие скроллбара для стабилизации интерфейса */
/* #tank-list::-webkit-scrollbar {
  display: none;
} */

/* Individual tank card - MICRO-OPTIMIZED */
.tank-item {
  background: transparent;
  border: none;
  border-radius: 12px;
  position: relative;
  overflow: hidden; /* Возвращаем для предотвращения слияния карточек */
  padding: 0.6rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.6rem;
  cursor: pointer;
  /* УВЕЛИЧЕННАЯ ВЫСОТА ДЛЯ ПОЛНЫХ НАЗВАНИЙ */
  min-height: 120px;
  height: auto; /* Автоматическая высота */
  max-height: 140px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  /* MICRO-OPTIMIZED APPEARANCE */
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
  /* PERFORMANCE OPTIMIZATIONS */
  contain: layout style;
  will-change: auto;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  /* MICRO-OPTIMIZATIONS: Reduce paint complexity */
  isolation: isolate;
  /* MICRO-OPTIMIZATIONS: GPU acceleration hints */
  transform: translateZ(0);
}

/* СТАБИЛИЗИРОВАННЫЕ КАРТОЧКИ - УБИРАЕМ АГРЕССИВНУЮ ИЗОЛЯЦИЮ */
.tank-item-stabilized {
  /* Минимальная дополнительная стабилизация */
  transform: translateZ(0); /* Только GPU слой */
}

/* УБИРАЕМ АНИМАЦИЮ ВХОДА */

.tank-item:hover {
  /* HARVARD-LEVEL СТАБИЛЬНЫЙ HOVER БЕЗ LAYOUT SHIFTS */
  transform: translateY(-2px) translateZ(0);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease, background 0.2s ease;
  /* HARVARD-LEVEL ОПТИМИЗАЦИЯ HOVER */
  will-change: transform, box-shadow, background;
}

/* СТАБИЛИЗАЦИЯ HOVER ДЛЯ СТАБИЛИЗИРОВАННЫХ ЭЛЕМЕНТОВ */
.tank-item-stabilized:hover {
  /* Минимальная стабилизация hover эффектов */
  contain: layout style;
}

.tank-item:hover .tank-flag-background {
  filter: brightness(1.1) saturate(1.4);
  transition: filter 0.2s ease;
}

/* Selected state - без контуров */
.tank-item.selected {
  background: rgba(139, 92, 246, 0.2);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

/* Category-specific selected highlight - без контуров */
.tank-item.selected-lt {
  background: rgba(124, 58, 237, 0.2);
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.3);
}
.tank-item.selected-st {
  background: rgba(16, 185, 129, 0.2);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}
.tank-item.selected-tt {
  background: rgba(245, 158, 11, 0.2);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}
.tank-item.selected-td {
  background: rgba(239, 68, 68, 0.2);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}
.tank-item.selected-spg {
  background: rgba(168, 85, 247, 0.2);
  box-shadow: 0 8px 25px rgba(168, 85, 247, 0.3);
}

/* Container for tank type badge + small icon */
.tank-type-container {
  display: flex;
  align-items: center;
  gap: 0;
  z-index: 2;
  width: 100%;
  min-width: 0;
}

/* Compact badge with tank type abbreviation */
.tank-type-indicator {
  font-size: 0.85rem;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 6px;
  line-height: 1;
  color: #ffffff;
  text-transform: uppercase;
  white-space: nowrap;
  flex-shrink: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  box-shadow:
    0 3px 6px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

/* Шершавые soft-touch бейджики с оригинальными цветами */
.tank-type-indicator.lt {
  background:
    radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.1) 0.8px, transparent 0.8px),
    radial-gradient(circle at 70% 60%, rgba(255, 255, 255, 0.08) 0.6px, transparent 0.6px),
    radial-gradient(circle at 20% 80%, rgba(0, 0, 0, 0.06) 0.7px, transparent 0.7px),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.06) 1px, transparent 1px),
    radial-gradient(circle at 10% 60%, rgba(0, 0, 0, 0.08) 0.5px, transparent 0.5px),
    linear-gradient(135deg, #7c3aed, #8b5cf6, #6d28d9);
  background-size: 3px 3px, 2.5px 2.5px, 2px 2px, 3.5px 3.5px, 2.2px 2.2px, 100% 100%;
  border: 1px solid rgba(124, 58, 237, 0.5);
}

.tank-type-indicator.st {
  background:
    radial-gradient(circle at 25% 35%, rgba(255, 255, 255, 0.1) 0.8px, transparent 0.8px),
    radial-gradient(circle at 75% 65%, rgba(255, 255, 255, 0.08) 0.6px, transparent 0.6px),
    radial-gradient(circle at 40% 90%, rgba(0, 0, 0, 0.06) 0.7px, transparent 0.7px),
    radial-gradient(circle at 85% 25%, rgba(255, 255, 255, 0.06) 1px, transparent 1px),
    radial-gradient(circle at 15% 70%, rgba(0, 0, 0, 0.08) 0.5px, transparent 0.5px),
    linear-gradient(135deg, #db2777, #ec4899, #be185d);
  background-size: 3px 3px, 2.5px 2.5px, 2px 2px, 3.5px 3.5px, 2.2px 2.2px, 100% 100%;
  border: 1px solid rgba(219, 39, 119, 0.5);
}

.tank-type-indicator.tt {
  background:
    radial-gradient(circle at 35% 30%, rgba(255, 255, 255, 0.1) 0.8px, transparent 0.8px),
    radial-gradient(circle at 65% 70%, rgba(255, 255, 255, 0.08) 0.6px, transparent 0.6px),
    radial-gradient(circle at 15% 85%, rgba(0, 0, 0, 0.06) 0.7px, transparent 0.7px),
    radial-gradient(circle at 90% 15%, rgba(255, 255, 255, 0.06) 1px, transparent 1px),
    radial-gradient(circle at 5% 50%, rgba(0, 0, 0, 0.08) 0.5px, transparent 0.5px),
    linear-gradient(135deg, #2563eb, #3b82f6, #1d4ed8);
  background-size: 3px 3px, 2.5px 2.5px, 2px 2px, 3.5px 3.5px, 2.2px 2.2px, 100% 100%;
  border: 1px solid rgba(37, 99, 235, 0.5);
}

.tank-type-indicator.td {
  background:
    radial-gradient(circle at 40% 25%, rgba(255, 255, 255, 0.1) 0.8px, transparent 0.8px),
    radial-gradient(circle at 60% 75%, rgba(255, 255, 255, 0.08) 0.6px, transparent 0.6px),
    radial-gradient(circle at 25% 90%, rgba(0, 0, 0, 0.06) 0.7px, transparent 0.7px),
    radial-gradient(circle at 95% 35%, rgba(255, 255, 255, 0.06) 1px, transparent 1px),
    radial-gradient(circle at 8% 45%, rgba(0, 0, 0, 0.08) 0.5px, transparent 0.5px),
    linear-gradient(135deg, #059669, #10b981, #047857);
  background-size: 3px 3px, 2.5px 2.5px, 2px 2px, 3.5px 3.5px, 2.2px 2.2px, 100% 100%;
  border: 1px solid rgba(5, 150, 105, 0.5);
}

.tank-type-indicator.spg {
  background:
    radial-gradient(circle at 20% 45%, rgba(255, 255, 255, 0.1) 0.8px, transparent 0.8px),
    radial-gradient(circle at 80% 55%, rgba(255, 255, 255, 0.08) 0.6px, transparent 0.6px),
    radial-gradient(circle at 45% 85%, rgba(0, 0, 0, 0.06) 0.7px, transparent 0.7px),
    radial-gradient(circle at 75% 10%, rgba(255, 255, 255, 0.06) 1px, transparent 1px),
    radial-gradient(circle at 12% 75%, rgba(0, 0, 0, 0.08) 0.5px, transparent 0.5px),
    linear-gradient(135deg, #dc2626, #ef4444, #b91c1c);
  background-size: 3px 3px, 2.5px 2.5px, 2px 2px, 3.5px 3.5px, 2.2px 2.2px, 100% 100%;
  border: 1px solid rgba(220, 38, 38, 0.5);
}

/* Hover эффекты для шершавых бейджиков - УБИРАЕМ УВЕЛИЧЕНИЕ */
.tank-type-indicator:hover {
  transform: translateY(-1px);
  box-shadow:
    0 5px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  filter: brightness(1.1) saturate(1.1);
}

/* Усиленная шершавая текстура при hover */
.tank-type-indicator:hover::before {
  content: '';
  position: absolute;
  inset: 0;
  background:
    radial-gradient(circle at 35% 45%, rgba(255, 255, 255, 0.15) 0.8px, transparent 0.8px),
    radial-gradient(circle at 65% 55%, rgba(255, 255, 255, 0.12) 0.6px, transparent 0.6px),
    radial-gradient(circle at 25% 75%, rgba(0, 0, 0, 0.1) 0.7px, transparent 0.7px),
    radial-gradient(circle at 85% 25%, rgba(255, 255, 255, 0.08) 1px, transparent 1px);
  background-size: 3px 3px, 2.5px 2.5px, 2px 2px, 3.5px 3.5px;
  border-radius: inherit;
  pointer-events: none;
}

/* Small icon immediately following the badge */
.tank-type-icon {
  width: 30px;
  height: 30px;
  object-fit: contain;
  flex-shrink: 0;
  order: 1;
  margin-left: -6px;
}

/* Бейджик уровня танка в правом верхнем углу */
.tank-level-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.12) 1px, transparent 1px),
    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.08) 1px, transparent 1px),
    radial-gradient(circle at 40% 80%, rgba(0, 0, 0, 0.06) 1px, transparent 1px),
    radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.1) 0.8px, transparent 0.8px),
    radial-gradient(circle at 10% 90%, rgba(0, 0, 0, 0.08) 0.6px, transparent 0.6px),
    radial-gradient(circle at 90% 40%, rgba(255, 255, 255, 0.06) 1.2px, transparent 1.2px),
    linear-gradient(135deg, #fbbf24, #f59e0b, #d97706, #b45309);
  background-size: 4px 4px, 3px 3px, 2px 2px, 3.5px 3.5px, 2.5px 2.5px, 4.5px 4.5px, 100% 100%;
  color: #ffffff;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 6px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 24px;
  text-align: center;
  line-height: 1;
  border: 1px solid rgba(251, 191, 36, 0.4);
  transition: all 0.2s ease;
}

/* Убираем увеличение при наведении на бейджик уровня */
.tank-level-badge:hover {
  transform: translateY(-1px);
  box-shadow:
    0 3px 8px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  filter: brightness(1.1) saturate(1.1);
}

/* Blurred country flag background */
.tank-flag-background {
  position: absolute;
  inset: -1.7rem;
  margin-left: -0rem;
  background-size: cover;
  background-position: center;
  filter: brightness(1) saturate(1.3);
  transition:
    filter 0.25s ease,
    transform 0.25s ease;
  z-index: 0;
  border-radius: inherit;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

/* Ensure main tank icon uses background image correctly */
.tank-icon {
  width: 75px; /* Уменьшаем размер иконки для большего места под текст */
  height: 48px; /* Уменьшаем высоту */
  background-size: contain; /* Показываем полную иконку */
  background-position: center;
  background-repeat: no-repeat;
  z-index: 2;
  margin-left: auto;
  flex: 0 0 75px; /* Соответствует ширине иконки */
  order: 2;
  transform: none; /* Убираем смещение */
}

/* Vertical information container inside the card */
.tank-info-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 0.3rem;
  z-index: 3;
  flex: 1 1 auto;
  order: 1;
  min-width: 0;
  max-width: calc(100% - 85px); /* Больше места для текста - иконка 75px + отступы */
  height: 100%;
}

/* Optional: name row wrapper */
.tank-name-row {
  display: flex;
  justify-content: flex-start;
  width: auto;
  margin-top: auto;
  align-self: flex-start;
}

.tank-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.tank-name {
  font-size: clamp(0.8rem, 1.4vw, 0.9rem); /* Возвращаем нормальный размер */
  font-weight: 700; /* Возвращаем жирность */
  text-align: left;
  color: #ffffff;
  line-height: 1.25;
  font-stretch: condensed;
  letter-spacing: -0.01em;
  width: 100%; /* Используем всю доступную ширину */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;

  /* Убираем обрезание - показываем полное название */
  white-space: normal; /* Разрешаем перенос строк */
  overflow: visible; /* Показываем весь текст */
  word-wrap: break-word; /* Переносим длинные слова */
  display: block;
}

/* Простой hover эффект без изменения размера */
.tank-item:hover .tank-name {
  color: #f8fafc;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.tank-category,
.tank-role {
  font-size: 0.75rem;
  color: var(--gm-dark-text-secondary);
  letter-spacing: 0.03em;
}

/* Адаптивные размеры для больших мониторов */
@media (min-width: 1920px) {
  #tank-list,
  .visible-items-container {
    grid-template-columns: repeat(auto-fill, 230px);
  }
}

/* Мобильные устройства */
@media (max-width: 768px) {
  .tank-name {
    font-size: clamp(0.75rem, 1.6vw, 0.85rem);
  }
}

@media (max-width: 480px) {
  .tank-name {
    font-size: clamp(0.7rem, 1.8vw, 0.8rem);
  }
}

/* ========================================
   ПРЕДОТВРАЩЕНИЕ МИГАНИЯ НА СТРАНИЦАХ ТАНКОВ
   ======================================== */

/* Скрываем список танков на страницах конкретных танков для предотвращения мигания */
body[data-tank-page="true"] #tank-list,
body[data-tank-page="true"] .main-content-container #tank-list {
  display: none;
  opacity: 0;
  visibility: hidden;
}

/* Скрываем tank-list когда показаны характеристики танка */
.tank-characteristics:not(.hidden) ~ #tank-list,
#tank-characteristics-container:not(.hidden) ~ #tank-list,
body .tank-characteristics:not(.hidden) ~ #tank-list,
body #tank-characteristics-container:not(.hidden) ~ #tank-list {
  display: none;
  opacity: 0;
  visibility: hidden;
}

/* ========================================
   ULTRA PERFORMANCE RENDERER STYLES
   ======================================== */

/* Виртуальный контейнер */
.virtual-scroll-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* Видимые элементы */
.visible-items-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, 210px);
  gap: 0.5rem;
  width: 100%;
  justify-content: space-evenly;
}

/* Ленивая загрузка изображений */
[data-lazy-bg] {
  background-color: rgba(255, 255, 255, 0.1);
  background-image: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

/* Плавное появление после загрузки изображения */
.tank-item [data-lazy-bg] {
  transition: background-color 0.3s ease;
}

.tank-item [data-lazy-bg]:not([data-lazy-bg]) {
  background-color: transparent;
}


