/**
 * УЛЬТРА-ОПТИМИЗИРОВАННЫЙ МЕНЕДЖЕР РЕСУРСОВ
 * Максимальная производительность загрузки и кэширования
 */

class UltraResourceManager {
  constructor() {
    this.resourceCache = new Map();
    this.loadingPromises = new Map();
    this.preloadQueue = [];
    this.criticalResources = new Set();
    this.metrics = {
      cacheHits: 0,
      cacheMisses: 0,
      loadTime: 0,
      preloadedResources: 0
    };
  }

  /**
   * КРИТИЧЕСКАЯ ПРЕДЗАГРУЗКА РЕСУРСОВ
   */
  async preloadCriticalResources() {
    // Отключаем предзагрузку модулей, так как они уже загружаются в main.js
    console.log('⚡ Critical resources preloading skipped (handled by main.js)');
    this.metrics.loadTime = 0;
    this.metrics.preloadedResources = 0;
    return;

  }

  /**
   * ОПТИМИЗИРОВАННАЯ ЗАГРУЗКА МОДУЛЯ
   */
  async loadModule(modulePath) {
    // Проверяем кэш
    if (this.resourceCache.has(modulePath)) {
      this.metrics.cacheHits++;
      return this.resourceCache.get(modulePath);
    }

    // Проверяем, не загружается ли уже
    if (this.loadingPromises.has(modulePath)) {
      return this.loadingPromises.get(modulePath);
    }

    // Создаем промис загрузки
    const loadPromise = this._loadModuleInternal(modulePath);
    this.loadingPromises.set(modulePath, loadPromise);

    try {
      const moduleData = await loadPromise;
      this.resourceCache.set(modulePath, moduleData);
      this.metrics.cacheMisses++;
      return moduleData;
    } finally {
      this.loadingPromises.delete(modulePath);
    }
  }

  /**
   * ВНУТРЕННЯЯ ЗАГРУЗКА МОДУЛЯ
   */
  async _loadModuleInternal(modulePath) {
    const startTime = performance.now();
    
    try {
      const moduleData = await import(modulePath);
      const loadTime = performance.now() - startTime;
      
      if (loadTime > 50) {
        console.warn(`Slow module load: ${modulePath} took ${loadTime.toFixed(2)}ms`);
      }
      
      return moduleData;
    } catch (error) {
      console.error(`Failed to load module ${modulePath}:`, error);
      throw error;
    }
  }

  /**
   * ЛЕНИВАЯ ЗАГРУЗКА С ПРИОРИТЕТОМ
   */
  async lazyLoad(modulePath, priority = 'normal') {
    if (priority === 'high') {
      return this.loadModule(modulePath);
    }

    // Для обычного приоритета используем requestIdleCallback
    return new Promise((resolve, reject) => {
      const loadFn = async () => {
        try {
          const result = await this.loadModule(modulePath);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };

      if (window.requestIdleCallback) {
        window.requestIdleCallback(loadFn, { timeout: 1000 });
      } else {
        setTimeout(loadFn, 0);
      }
    });
  }

  /**
   * ПРЕДЗАГРУЗКА ИЗОБРАЖЕНИЙ
   */
  async preloadImages(imagePaths) {
    const imagePromises = imagePaths.map(path => {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve({ path, success: true });
        img.onerror = () => resolve({ path, success: false });
        img.src = path;
      });
    });

    const results = await Promise.allSettled(imagePromises);
    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    
    console.log(`🖼️ Preloaded ${successful}/${imagePaths.length} images`);
    return results;
  }

  /**
   * ОЧИСТКА КЭША
   */
  clearCache(keepCritical = true) {
    if (keepCritical) {
      // Удаляем только некритические ресурсы
      for (const [key] of this.resourceCache) {
        if (!this.criticalResources.has(key)) {
          this.resourceCache.delete(key);
        }
      }
    } else {
      this.resourceCache.clear();
      this.criticalResources.clear();
    }
    
    this.loadingPromises.clear();
  }

  /**
   * ПОЛУЧЕНИЕ МЕТРИК
   */
  getMetrics() {
    return {
      ...this.metrics,
      cacheSize: this.resourceCache.size,
      hitRate: this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) || 0
    };
  }

  /**
   * ОПТИМИЗАЦИЯ ПАМЯТИ
   */
  optimizeMemory() {
    const maxCacheSize = 50;
    
    if (this.resourceCache.size > maxCacheSize) {
      // Удаляем старые некритические ресурсы
      let deleted = 0;
      for (const [key] of this.resourceCache) {
        if (!this.criticalResources.has(key) && deleted < 10) {
          this.resourceCache.delete(key);
          deleted++;
        }
      }
      
      console.log(`🧹 Cleaned ${deleted} cached resources`);
    }
  }
}

// Создаем глобальный экземпляр
export const ultraResourceManager = new UltraResourceManager();

/**
 * ИНИЦИАЛИЗАЦИЯ УЛЬТРА-МЕНЕДЖЕРА РЕСУРСОВ
 */
export async function initializeUltraResourceManager() {
  await ultraResourceManager.preloadCriticalResources();
  
  // Настраиваем автоочистку памяти
  setInterval(() => {
    ultraResourceManager.optimizeMemory();
  }, 60000); // Каждую минуту
  
  return ultraResourceManager;
}

export default ultraResourceManager;
