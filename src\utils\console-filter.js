/**
 * CONSOLE FILTER - Фильтрация нежелательных сообщений браузерных расширений
 */

const EXTENSION_ERROR_PATTERNS = [
  'runtime.lastError',
  'message port closed',
  'Extension context invalidated',
  'Could not establish connection',
  'chrome-extension://',
  'moz-extension://',
  'safari-extension://',
  'edge-extension://',
  'Receiving end does not exist',
  'The message port closed before a response was received',
  'Unchecked runtime.lastError',
];

function isExtensionError(message) {
  if (typeof message !== 'string') return false;

  return EXTENSION_ERROR_PATTERNS.some(pattern =>
    message.toLowerCase().includes(pattern.toLowerCase())
  );
}

export function initConsoleFilter() {
  // Применяем фильтр только в режиме разработки
  if (import.meta.env?.MODE !== 'development') {
    return;
  }

  // Фильтрация console.error
  const originalError = console.error;
  console.error = (...args) => {
    if (args[0] && isExtensionError(args[0])) {
      return;
    }
    originalError.apply(console, args);
  };

  // Фильтрация console.warn
  const originalWarn = console.warn;
  console.warn = (...args) => {
    if (args[0] && isExtensionError(args[0])) {
      return;
    }
    originalWarn.apply(console, args);
  };

  // Глобальная обработка необработанных ошибок
  const originalWindowError = window.onerror;
  window.onerror = (message, source, lineno, colno, error) => {
    if (isExtensionError(message)) {
      return true;
    }

    if (originalWindowError) {
      return originalWindowError(message, source, lineno, colno, error);
    }
    return false;
  };

  // Обработка необработанных промисов
  const originalUnhandledRejection = window.onunhandledrejection;
  window.onunhandledrejection = (event) => {
    if (event.reason && isExtensionError(event.reason.toString())) {
      event.preventDefault();
      return;
    }

    if (originalUnhandledRejection) {
      originalUnhandledRejection(event);
    }
  };
}

// Автоматическая инициализация при импорте (только в dev режиме)
if (import.meta.env?.MODE === 'development') {
  setTimeout(initConsoleFilter, 100);
}
