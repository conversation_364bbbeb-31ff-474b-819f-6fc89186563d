/* ========================================
   АДАПТИВНАЯ ТАБЛИЦА СРАВНЕНИЯ ДЛЯ 8+ ТАНКОВ
   ======================================== */

/* ОСНОВНОЙ КОНТЕЙНЕР */
.adaptive-compare-table {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(30, 39, 59, 0.95) 0%,
    rgba(45, 55, 75, 0.9) 100%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  overflow: hidden;
  position: relative;
}

/* КОНТЕЙНЕР СКРОЛЛА */
.table-scroll-container {
  width: 100%;
  height: 100%;
  overflow-x: auto;
  overflow-y: auto;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(114, 84, 228, 0.5) rgba(255, 255, 255, 0.1);
}

.table-scroll-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.table-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.table-scroll-container::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, rgba(114, 84, 228, 0.6), rgba(161, 123, 248, 0.6));
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.table-scroll-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, rgba(114, 84, 228, 0.8), rgba(161, 123, 248, 0.8));
}

/* ЗАГОЛОВОК ТАБЛИЦЫ */
.adaptive-table-header {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 100;
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.15), rgba(161, 123, 248, 0.1));
  border-bottom: 2px solid rgba(114, 84, 228, 0.3);
  backdrop-filter: blur(15px);
}

.characteristics-column {
  width: 250px;
  min-width: 250px;
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.25), rgba(161, 123, 248, 0.2));
  border-right: 2px solid rgba(114, 84, 228, 0.4);
  position: sticky;
  left: 0;
  z-index: 101;
}

.characteristics-column.fixed {
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
}

.column-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem 1rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.header-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 0 10px rgba(114, 84, 228, 0.8));
}

.header-text {
  font-size: 1.1rem;
}

/* КОНТЕЙНЕР КОЛОНОК ТАНКОВ */
.tanks-columns-container {
  display: flex;
  min-width: fit-content;
}

.tank-column {
  min-width: 140px;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.tank-column:last-child {
  border-right: none;
}

.tank-column-content {
  padding: 1rem 0.75rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tank-column-content:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.tank-column-content.filled {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.1), rgba(161, 123, 248, 0.08));
  border: 1px solid rgba(114, 84, 228, 0.2);
  border-radius: 12px;
  margin: 0.5rem;
  cursor: default;
}

.tank-column-content.filled:hover {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.15), rgba(161, 123, 248, 0.12));
  border-color: rgba(114, 84, 228, 0.4);
  transform: translateY(-1px);
}

/* АВАТАР ТАНКА */
.tank-avatar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tank-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.tank-column-content:hover .tank-icon {
  transform: scale(1.05);
  border-color: rgba(114, 84, 228, 0.5);
}

.remove-tank-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 18px;
  height: 18px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
  opacity: 0;
}

.tank-column-content.filled:hover .remove-tank-btn {
  opacity: 1;
}

.remove-tank-btn:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* ИНФОРМАЦИЯ О ТАНКЕ */
.tank-info {
  text-align: center;
  width: 100%;
}

.tank-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.tank-meta {
  font-size: 0.7rem;
  color: #9ca3af;
  font-weight: 500;
}

/* ПУСТОЙ СЛОТ */
.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  transition: all 0.3s ease;
  padding: 1rem;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  width: 100%;
}

.tank-column-content.empty:hover .empty-slot {
  color: #9ca3af;
  border-color: rgba(114, 84, 228, 0.5);
  background: rgba(114, 84, 228, 0.1);
}

.add-icon {
  font-size: 2rem;
  font-weight: bold;
  color: #7c3aed;
  filter: drop-shadow(0 0 10px rgba(124, 58, 237, 0.5));
  transition: all 0.3s ease;
}

.tank-column-content.empty:hover .add-icon {
  color: #8b5cf6;
  transform: scale(1.1);
}

.add-text {
  font-size: 0.75rem;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

/* ТЕЛО ТАБЛИЦЫ */
.adaptive-table-body {
  background: rgba(255, 255, 255, 0.01);
}

.virtual-rows-container {
  width: 100%;
}

/* СЕКЦИИ КАТЕГОРИЙ */
.category-section {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.category-section:last-child {
  border-bottom: none;
}

.category-header-row {
  display: flex;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  left: 0;
  z-index: 50;
}

.category-cell {
  width: 250px;
  min-width: 250px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
  border-right: 2px solid rgba(114, 84, 228, 0.2);
  position: sticky;
  left: 0;
  z-index: 51;
}

.category-cell.fixed {
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.category-icon {
  font-size: 1.25rem;
  filter: drop-shadow(0 0 10px currentColor);
}

.category-name {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

.tank-cells-container {
  display: flex;
  min-width: fit-content;
}

.tank-cell {
  min-width: 140px;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.tank-cell:last-child {
  border-right: none;
}

.category-spacer {
  background: rgba(255, 255, 255, 0.02);
}

/* СТРОКИ ХАРАКТЕРИСТИК */
.characteristic-row {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.03);
  transition: background-color 0.2s ease;
}

.characteristic-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.characteristic-cell {
  width: 250px;
  min-width: 250px;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.02);
  font-size: 0.85rem;
  color: #d1d5db;
  border-right: 2px solid rgba(114, 84, 228, 0.1);
  display: flex;
  align-items: center;
  font-weight: 500;
  position: sticky;
  left: 0;
  z-index: 10;
}

.characteristic-cell.fixed {
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.values-container {
  display: flex;
  min-width: fit-content;
  flex: 1;
}

/* ЯЧЕЙКИ ЗНАЧЕНИЙ */
.value-cell {
  min-width: 140px;
  padding: 0.75rem 0.5rem;
  background: rgba(255, 255, 255, 0.01);
  font-size: 0.8rem;
  color: #f3f4f6;
  text-align: center;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.2rem;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
}

.value-cell:last-child {
  border-right: none;
}

.value-cell:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: scale(1.02);
}

.value-cell.empty {
  background: rgba(255, 255, 255, 0.005);
}

.value {
  font-weight: 600;
  font-size: 0.85rem;
}

.value.optimal {
  color: #10b981;
  text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
  font-weight: 700;
}

.unit {
  font-size: 0.7rem;
  color: #9ca3af;
  font-weight: 400;
}

.empty-value {
  color: #6b7280;
  font-style: italic;
}

.value-cell.optimal {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.1));
  border: 1px solid rgba(16, 185, 129, 0.3);
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.2);
}

/* ИНДИКАТОР СКРОЛЛА */
.scroll-indicator {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  z-index: 1000;
}

.scroll-track {
  width: 100px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  position: relative;
}

.scroll-thumb {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 20px;
  background: linear-gradient(90deg, #7c3aed, #a855f7);
  border-radius: 2px;
  transition: left 0.2s ease;
}

.scroll-info {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: #ffffff;
  font-weight: 500;
}

.current-position {
  color: #7c3aed;
  font-weight: 700;
}

.separator {
  color: #9ca3af;
}

.total-columns {
  color: #9ca3af;
}

/* АДАПТИВНОСТЬ */
@media (max-width: 1400px) {
  .tank-column,
  .tank-cell,
  .value-cell {
    min-width: 120px;
  }
  
  .tank-name {
    font-size: 0.75rem;
    max-width: 100px;
  }
}

@media (max-width: 1200px) {
  .characteristics-column,
  .category-cell,
  .characteristic-cell {
    width: 200px;
    min-width: 200px;
  }
  
  .tank-column,
  .tank-cell,
  .value-cell {
    min-width: 100px;
  }
  
  .tank-icon {
    width: 32px;
    height: 32px;
  }
  
  .tank-name {
    font-size: 0.7rem;
    max-width: 80px;
  }
}

@media (max-width: 768px) {
  .adaptive-compare-table {
    border-radius: 12px;
  }
  
  .characteristics-column,
  .category-cell,
  .characteristic-cell {
    width: 150px;
    min-width: 150px;
  }
  
  .tank-column,
  .tank-cell,
  .value-cell {
    min-width: 80px;
  }
  
  .column-header {
    padding: 1rem 0.75rem;
  }
  
  .header-text {
    font-size: 0.9rem;
  }
  
  .tank-icon {
    width: 28px;
    height: 28px;
  }
  
  .tank-name {
    font-size: 0.65rem;
    max-width: 60px;
  }
  
  .scroll-indicator {
    bottom: 0.5rem;
    right: 0.5rem;
    padding: 0.5rem 0.75rem;
  }
  
  .scroll-track {
    width: 60px;
  }
}
