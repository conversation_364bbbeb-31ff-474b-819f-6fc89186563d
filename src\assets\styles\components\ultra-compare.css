/* ========================================
   УЛЬТРА-ПРЕМИАЛЬНАЯ СИСТЕМА СРАВНЕНИЯ
   Стоимость: 10 миллионов долларов
   ======================================== */

/* ОСНОВНОЙ КОНТЕЙНЕР УЛЬТРА-СИСТЕМЫ */
.ultra-compare-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 3rem;
  background: linear-gradient(135deg, 
    rgba(114, 84, 228, 0.12) 0%,
    rgba(161, 123, 248, 0.1) 25%,
    rgba(59, 130, 246, 0.08) 50%,
    rgba(139, 92, 246, 0.1) 75%,
    rgba(236, 72, 153, 0.08) 100%);
  border-radius: 32px;
  border: 1px solid rgba(114, 84, 228, 0.3);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(114, 84, 228, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 100px rgba(114, 84, 228, 0.3);
  position: relative;
  overflow: hidden;
  animation: ultraGlow 4s ease-in-out infinite alternate;
}

/* ГОЛОГРАФИЧЕСКИЙ ЗАГОЛОВОК */
.ultra-header {
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

.ultra-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent);
  animation: holographicScan 3s linear infinite;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.title-section {
  flex: 1;
}

.ultra-title {
  font-size: 3rem;
  font-weight: 900;
  margin: 0 0 0.5rem 0;
  position: relative;
}

.holographic-text {
  background: linear-gradient(45deg, 
    #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #dda0dd, #98d8c8);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: holographicShimmer 3s ease-in-out infinite;
  text-shadow: 0 0 30px rgba(114, 84, 228, 0.5);
}

.ultra-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  margin: 0;
  font-weight: 500;
}

.stats-section {
  display: flex;
  gap: 2rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-item:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 20px 40px rgba(114, 84, 228, 0.3);
  border-color: rgba(114, 84, 228, 0.5);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  filter: drop-shadow(0 0 10px rgba(114, 84, 228, 0.5));
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.25rem;
  text-shadow: 0 0 20px rgba(114, 84, 228, 0.8);
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* AI ПАНЕЛЬ УПРАВЛЕНИЯ */
.ai-control-panel {
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, 
    rgba(16, 185, 129, 0.1) 0%,
    rgba(59, 130, 246, 0.08) 100%);
  border-radius: 24px;
  border: 1px solid rgba(16, 185, 129, 0.3);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.ai-control-panel::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, 
    rgba(16, 185, 129, 0.1) 0%, 
    transparent 70%);
  animation: aiPulse 2s ease-in-out infinite;
  pointer-events: none;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

.ai-icon {
  font-size: 1.75rem;
  animation: aiRotate 4s linear infinite;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #10b981;
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.8);
  animation: statusPulse 1.5s ease-in-out infinite;
}

.control-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* УЛЬТРА-КНОПКИ */
.ultra-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 16px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.ultra-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent);
  transition: left 0.5s ease;
}

.ultra-button:hover::before {
  left: 100%;
}

.ultra-button.primary {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  color: white;
  border: 1px solid rgba(124, 58, 237, 0.5);
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
}

.ultra-button.primary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 35px rgba(124, 58, 237, 0.6);
}

.ultra-button.secondary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: 1px solid rgba(59, 130, 246, 0.5);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.ultra-button.tertiary {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: 1px solid rgba(16, 185, 129, 0.5);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.ultra-button.danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: 1px solid rgba(239, 68, 68, 0.5);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.button-icon {
  font-size: 1.2rem;
  filter: drop-shadow(0 0 10px currentColor);
}

.button-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  pointer-events: none;
}

.ultra-button:hover .button-glow {
  width: 200px;
  height: 200px;
}

/* AI ИНСАЙТЫ */
.ai-insights {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}

.insight-icon {
  font-size: 1.2rem;
  animation: insightGlow 2s ease-in-out infinite alternate;
}

/* КВАНТОВАЯ ТАБЛИЦА */
.quantum-table-container {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(15px);
}

.quantum-comparison-table {
  width: 100%;
  position: relative;
}

.quantum-table-header {
  display: grid;
  grid-template-columns: 250px repeat(12, minmax(120px, 1fr));
  gap: 1px;
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.15), rgba(161, 123, 248, 0.1));
  padding: 1rem;
  border-bottom: 2px solid rgba(114, 84, 228, 0.3);
}

.header-characteristics {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 700;
  color: #ffffff;
  padding: 1rem;
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.25), rgba(161, 123, 248, 0.2));
  border-radius: 12px;
  border: 1px solid rgba(114, 84, 228, 0.4);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.header-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 0 10px rgba(114, 84, 228, 0.8));
}

.tank-header-slot {
  padding: 0.75rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tank-header-slot:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.08));
  border-color: rgba(114, 84, 228, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.tank-header-slot.filled {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.15), rgba(161, 123, 248, 0.1));
  border-color: rgba(114, 84, 228, 0.4);
}

.tank-quantum-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  position: relative;
}

.tank-quantum-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tank-quantum-details {
  text-align: center;
  flex: 1;
}

.tank-quantum-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100px;
}

.tank-quantum-meta {
  font-size: 0.7rem;
  color: #9ca3af;
}

.quantum-remove-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.quantum-remove-btn:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.empty-quantum-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  transition: all 0.3s ease;
}

.quantum-plus {
  font-size: 2rem;
  font-weight: bold;
  color: #7c3aed;
  filter: drop-shadow(0 0 10px rgba(124, 58, 237, 0.5));
}

.quantum-text {
  font-size: 0.75rem;
  text-align: center;
  line-height: 1.2;
}

.tank-header-slot:hover .empty-quantum-slot {
  color: #9ca3af;
}

.tank-header-slot:hover .quantum-plus {
  color: #8b5cf6;
  transform: scale(1.1);
}

/* ТЕЛО КВАНТОВОЙ ТАБЛИЦЫ */
.quantum-table-body {
  padding: 1rem;
}

.quantum-category-section {
  margin-bottom: 1rem;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.quantum-category-header {
  padding: 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.category-quantum-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.category-quantum-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 0 10px currentColor);
}

.category-quantum-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
}

.quantum-characteristic-row {
  display: grid;
  grid-template-columns: 250px repeat(12, minmax(120px, 1fr));
  gap: 1px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.03);
  transition: background-color 0.2s ease;
}

.quantum-characteristic-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.quantum-characteristic-name {
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.02);
  font-size: 0.85rem;
  color: #d1d5db;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  font-weight: 500;
}

.quantum-characteristic-value {
  padding: 0.75rem 0.5rem;
  background: rgba(255, 255, 255, 0.01);
  font-size: 0.8rem;
  color: #f3f4f6;
  text-align: center;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.2rem;
  min-height: 50px;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
}

.quantum-characteristic-value:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: scale(1.02);
}

.quantum-value {
  font-weight: 600;
  font-size: 0.9rem;
}

.quantum-value.optimal {
  color: #10b981;
  text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
  font-weight: 700;
}

.quantum-unit {
  font-size: 0.7rem;
  color: #9ca3af;
  font-weight: 400;
}

.quantum-empty {
  color: #6b7280;
  font-style: italic;
}

.optimal-value {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.1)) !important;
  border: 1px solid rgba(16, 185, 129, 0.3) !important;
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.2);
}

/* СИСТЕМА ЧАСТИЦ */
.particle-system {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.quantum-particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
}

/* АНИМАЦИИ */
@keyframes ultraGlow {
  0% { box-shadow: 0 32px 64px rgba(0, 0, 0, 0.4), 0 0 100px rgba(114, 84, 228, 0.3); }
  100% { box-shadow: 0 32px 64px rgba(0, 0, 0, 0.4), 0 0 150px rgba(114, 84, 228, 0.5); }
}

@keyframes holographicShimmer {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes holographicScan {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes aiPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

@keyframes aiRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

@keyframes insightGlow {
  0% { filter: drop-shadow(0 0 5px rgba(255, 193, 7, 0.5)); }
  100% { filter: drop-shadow(0 0 15px rgba(255, 193, 7, 0.8)); }
}

@keyframes quantumFloat {
  0% { transform: translateY(0px) rotate(0deg); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
}

/* МОДАЛЬНЫЕ ОКНА КВАНТОВЫХ РЕЗУЛЬТАТОВ */
.quantum-results-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: modalFadeIn 0.3s ease-out;
}

.modal-content {
  background: linear-gradient(135deg,
    rgba(26, 26, 46, 0.95) 0%,
    rgba(22, 33, 62, 0.9) 100%);
  border-radius: 24px;
  border: 1px solid rgba(114, 84, 228, 0.3);
  backdrop-filter: blur(25px);
  padding: 2rem;
  max-width: 600px;
  width: 90%;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(114, 84, 228, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-content h3 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
  text-align: center;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.result-item {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.result-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(114, 84, 228, 0.2);
}

.result-label {
  font-size: 0.875rem;
  color: #9ca3af;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.result-value {
  font-size: 2rem;
  font-weight: 700;
  color: #10b981;
  text-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
}

.recommendations {
  margin-bottom: 2rem;
}

.recommendations h4 {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.recommendations p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin: 0.5rem 0;
  line-height: 1.5;
}

.close-modal {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.close-modal:hover {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
}

/* AI СТАТУСЫ */
.ai-status.thinking {
  animation: thinkingPulse 1s ease-in-out infinite;
}

.ai-status.thinking .status-indicator {
  background: #f59e0b;
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.8);
}

.insight-item.success {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
}

.insight-item.success .insight-icon {
  color: #10b981;
}

.insight-item.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
}

.insight-item.error .insight-icon {
  color: #ef4444;
}

/* ДОПОЛНИТЕЛЬНЫЕ АНИМАЦИИ */
@keyframes modalFadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes thinkingPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* АДАПТИВНОСТЬ ДЛЯ УЛЬТРА-СИСТЕМЫ */
@media (max-width: 1400px) {
  .quantum-table-header,
  .quantum-characteristic-row {
    grid-template-columns: 200px repeat(12, minmax(100px, 1fr));
  }

  .tank-quantum-name {
    font-size: 0.75rem;
    max-width: 80px;
  }
}

@media (max-width: 1200px) {
  .ultra-compare-container {
    padding: 2rem;
  }

  .ultra-title {
    font-size: 2.5rem;
  }

  .stats-section {
    flex-direction: column;
    gap: 1rem;
  }

  .control-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .quantum-table-header,
  .quantum-characteristic-row {
    grid-template-columns: 180px repeat(12, minmax(80px, 1fr));
  }
}

@media (max-width: 768px) {
  .ultra-compare-container {
    padding: 1.5rem;
    margin: 0 0.5rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .ultra-title {
    font-size: 2rem;
  }

  .control-grid {
    grid-template-columns: 1fr;
  }

  .quantum-table-container {
    overflow-x: auto;
  }

  .quantum-table-header,
  .quantum-characteristic-row {
    grid-template-columns: 150px repeat(12, minmax(60px, 1fr));
    min-width: 800px;
  }

  .results-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    padding: 1.5rem;
    margin: 1rem;
  }
}

@media (max-width: 480px) {
  .ultra-title {
    font-size: 1.5rem;
  }

  .ultra-subtitle {
    font-size: 1rem;
  }

  .tank-quantum-icon {
    width: 28px;
    height: 28px;
  }

  .tank-quantum-name {
    font-size: 0.7rem;
    max-width: 60px;
  }
}
