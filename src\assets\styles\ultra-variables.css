/**
 * УЛЬТРА-ОПТИМИЗИРОВАННЫЕ CSS ПЕРЕМЕННЫЕ
 * Централизованная система дизайна для максимальной производительности
 */

:root {
  /* ЦВЕТОВАЯ ПАЛИТРА */
  --primary-blue: #3b82f6;
  --primary-purple: #8b5cf6;
  --primary-gradient: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
  
  --success-green: #22c55e;
  --warning-orange: #f59e0b;
  --error-red: #ef4444;
  --info-cyan: #06b6d4;
  
  /* НЕЙТРАЛЬНЫЕ ЦВЕТА */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* ПРОЗРАЧНОСТИ */
  --alpha-02: rgba(255, 255, 255, 0.02);
  --alpha-03: rgba(255, 255, 255, 0.03);
  --alpha-05: rgba(255, 255, 255, 0.05);
  --alpha-10: rgba(255, 255, 255, 0.1);
  --alpha-20: rgba(255, 255, 255, 0.2);
  --alpha-30: rgba(255, 255, 255, 0.3);
  --alpha-50: rgba(255, 255, 255, 0.5);
  
  /* РАЗМЕРЫ И ОТСТУПЫ */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.5rem;
  --spacing-2xl: 2rem;
  --spacing-3xl: 3rem;
  
  /* РАДИУСЫ СКРУГЛЕНИЯ */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-3xl: 20px;
  
  /* ШРИФТЫ */
  --font-xs: 0.625rem;
  --font-sm: 0.75rem;
  --font-md: 0.875rem;
  --font-lg: 1rem;
  --font-xl: 1.25rem;
  --font-2xl: 1.5rem;
  --font-3xl: 2rem;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* ТЕНИ */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* ПЕРЕХОДЫ */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
  --transition-bounce: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-INDEX СЛОИ */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* РАЗМЕРЫ КОМПОНЕНТОВ */
  --tank-icon-xs: 24px;
  --tank-icon-sm: 32px;
  --tank-icon-md: 48px;
  --tank-icon-lg: 64px;
  --tank-icon-xl: 96px;
  
  --flag-width: 16px;
  --flag-height: 12px;
  
  /* СЕТКИ И МАКЕТЫ */
  --grid-gap-sm: 0.5rem;
  --grid-gap-md: 1rem;
  --grid-gap-lg: 1.5rem;
  
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  
  /* ПРОИЗВОДИТЕЛЬНОСТЬ */
  --gpu-acceleration: translateZ(0);
  --will-change-transform: transform;
  --will-change-opacity: opacity;
  --will-change-auto: auto;
  
  /* ФИЛЬТРЫ И ЭФФЕКТЫ */
  --blur-sm: blur(4px);
  --blur-md: blur(8px);
  --blur-lg: blur(16px);
  --blur-xl: blur(24px);
  
  --backdrop-blur: blur(20px);
  --backdrop-saturate: saturate(180%);
  
  /* АНИМАЦИИ */
  --animate-spin: spin 1s linear infinite;
  --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-bounce: bounce 1s infinite;
  
  /* КАСТОМНЫЕ СВОЙСТВА ДЛЯ ТАНКОВ */
  --tank-card-width: 210px;
  --tank-card-height: 320px;
  --tank-card-padding: var(--spacing-lg);
  --tank-card-radius: var(--radius-xl);
  --tank-card-shadow: var(--shadow-lg);
  
  /* СРАВНЕНИЕ ТАНКОВ */
  --compare-header-height: 80px;
  --compare-row-height: 48px;
  --compare-category-width: 160px;
  --compare-tank-min-width: 140px;
  --compare-scroll-height: 8px;
  
  /* АДАПТИВНЫЕ БРЕЙКПОИНТЫ */
  --bp-sm: 640px;
  --bp-md: 768px;
  --bp-lg: 1024px;
  --bp-xl: 1280px;
  --bp-2xl: 1536px;
  --bp-3xl: 1920px;
}

/* ТЕМНАЯ ТЕМА (по умолчанию) */
:root {
  --bg-primary: #2a2155;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;

  --text-primary: var(--white);
  --text-secondary: var(--gray-200);
  --text-tertiary: var(--gray-400);

  --border-primary: var(--alpha-10);
  --border-secondary: var(--alpha-05);

  --surface-primary: var(--alpha-02);
  --surface-secondary: var(--alpha-03);
  --surface-tertiary: var(--alpha-05);
}

/* СВЕТЛАЯ ТЕМА */
[data-theme="light"] {
  --bg-primary: var(--white);
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  
  --border-primary: rgba(0, 0, 0, 0.1);
  --border-secondary: rgba(0, 0, 0, 0.05);
  
  --surface-primary: rgba(0, 0, 0, 0.02);
  --surface-secondary: rgba(0, 0, 0, 0.03);
  --surface-tertiary: rgba(0, 0, 0, 0.05);
}

/* УТИЛИТАРНЫЕ КЛАССЫ ДЛЯ ПРОИЗВОДИТЕЛЬНОСТИ */
.gpu-accelerated {
  transform: var(--gpu-acceleration);
  will-change: var(--will-change-transform);
}

.smooth-transition {
  transition: all var(--transition-normal);
}

.fast-transition {
  transition: all var(--transition-fast);
}

.no-transition {
  transition: none;
}

.backdrop-blur {
  backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
}

/* ОПТИМИЗИРОВАННЫЕ СЕЛЕКТОРЫ */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--tank-card-width), 1fr));
  gap: var(--grid-gap-md);
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ПРОИЗВОДИТЕЛЬНЫЕ АНИМАЦИИ */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.95);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.animate-slide-up {
  animation: slideUp var(--transition-normal) ease-out;
}

.animate-scale-in {
  animation: scaleIn var(--transition-normal) ease-out;
}
