/**
 * ПРОСТОЙ И РАБОЧИЙ ФИЛЬТР ПО TIER УРОВНЯМ ТАНКОВ
 */

export class TierFilter {
  constructor() {
    this.selectedTiers = new Set(['X']); // По умолчанию только X уровень
    this.container = null;
    this.onFilterChange = null;
  }

  /**
   * СОЗДАНИЕ ЧИСТЫХ КНОПОК УРОВНЕЙ
   */
  createHTML() {
    return `${this.createTierButtons()}`;
  }

  /**
   * СОЗДАНИЕ МИНИМАЛИСТИЧНЫХ КНОПОК TIER УРОВНЕЙ
   */
  createTierButtons() {
    const tiers = ['X', 'IX', 'VIII', 'VII', 'VI']; // Слева направо: большие -> меньшие
    const tierColors = {
      'VI': '#10b981',   // Изумрудный (начальный уровень)
      'VII': '#3b82f6',  // Синий (средний уровень)
      'VIII': '#8b5cf6', // Фиолетовый (высокий уровень)
      'IX': '#ec4899',   // Розовый (элитный уровень)
      'X': '#f59e0b'     // Золотой как в бейджиках танков (топ уровень)
    };

    return tiers.map(tier => {
      const isSelected = this.selectedTiers.has(tier);
      const color = tierColors[tier];

      return `
        <button
          class="tier-btn-minimal ${isSelected ? 'active' : ''}"
          data-tier="${tier}"
          style="--tier-color: ${color};"
          title="Уровень ${tier}"
        >
          ${tier}
        </button>
      `;
    }).join('');
  }



  /**
   * ИНИЦИАЛИЗАЦИЯ ФИЛЬТРА
   */
  init(containerSelector, onFilterChange) {
    this.container = document.querySelector(containerSelector);
    this.onFilterChange = onFilterChange;

    if (!this.container) {
      console.error('TierFilter: Container not found:', containerSelector);
      return;
    }

    // Вставляем HTML
    this.container.innerHTML = this.createHTML();

    // Добавляем стили
    this.addStyles();

    // Инициализируем обработчики событий
    this.initEventListeners();

    console.log('🏆 TierFilter initialized with tiers:', Array.from(this.selectedTiers));
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ ОБРАБОТЧИКОВ СОБЫТИЙ
   */
  initEventListeners() {
    // Обработчики минималистичных кнопок tier
    this.container.querySelectorAll('.tier-btn-minimal').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tier = e.currentTarget.dataset.tier;
        this.toggleTier(tier);
      });
    });

    // Минималистичная кнопка сброса
    const resetBtn = this.container.querySelector('.tier-reset-minimal');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => {
        this.resetFilter();
      });
    }
  }

  /**
   * ПЕРЕКЛЮЧЕНИЕ TIER УРОВНЯ
   */
  toggleTier(tier) {
    if (this.selectedTiers.has(tier)) {
      // Не позволяем убрать все уровни
      if (this.selectedTiers.size > 1) {
        this.selectedTiers.delete(tier);
      }
    } else {
      this.selectedTiers.add(tier);
    }

    this.updateUI();
    this.notifyFilterChange();
  }

  /**
   * СБРОС ФИЛЬТРА
   */
  resetFilter() {
    this.selectedTiers = new Set(['X']); // Сбрасываем к X уровню
    this.updateUI();
    this.notifyFilterChange();
  }

  /**
   * ОБНОВЛЕНИЕ UI
   */
  updateUI() {
    // Пересоздаем весь HTML для простоты
    this.container.innerHTML = this.createHTML();

    // Переинициализируем обработчики
    this.initEventListeners();
  }

  /**
   * УВЕДОМЛЕНИЕ О ИЗМЕНЕНИИ ФИЛЬТРА
   */
  notifyFilterChange() {
    if (this.onFilterChange) {
      this.onFilterChange(Array.from(this.selectedTiers));
    }
  }

  /**
   * ПОЛУЧЕНИЕ ВЫБРАННЫХ TIER
   */
  getSelectedTiers() {
    return Array.from(this.selectedTiers);
  }

  /**
   * УСТАНОВКА ВЫБРАННЫХ TIER
   */
  setSelectedTiers(tiers) {
    this.selectedTiers = new Set(tiers);
    this.updateUI();
  }

  /**
   * ДОБАВЛЕНИЕ СТИЛЕЙ
   */
  addStyles() {
    if (document.getElementById('tier-filter-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'tier-filter-styles';
    styles.textContent = `
      /* СИММЕТРИЧНЫЙ КОНТЕЙНЕР TIER ФИЛЬТРА */
      .tier-filter-minimal {
        background: rgba(42, 33, 85, 0.9);
        border: 1px solid rgba(139, 92, 246, 0.3);
        border-radius: 10px;
        padding: 0.6rem 0.8rem;
        margin-bottom: 1rem;
        backdrop-filter: blur(12px);
        box-shadow:
          0 3px 12px rgba(0, 0, 0, 0.2),
          0 0 0 1px rgba(255, 255, 255, 0.05);
        display: flex;
        align-items: center;
        gap: 0.8rem;
        flex-wrap: wrap;
        max-width: fit-content;
      }

      /* ПРОПОРЦИОНАЛЬНЫЙ ЛЕЙБЛ */
      .tier-label-minimal {
        display: flex;
        align-items: center;
        gap: 0.4rem;
        white-space: nowrap;
      }

      .tier-icon {
        font-size: 0.85rem;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
      }

      .tier-text {
        font-size: 0.8rem;
        font-weight: 700;
        background: linear-gradient(135deg, #ffffff, #f1f5f9, #e2e8f0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        filter: contrast(1.2) brightness(1.1);
        letter-spacing: 0.2px;
      }

      /* МИНИАТЮРНЫЕ КНОПКИ С ТЕКСТУРОЙ */
      .tier-buttons-minimal {
        display: flex;
        gap: 0.4rem;
        align-items: center;
        flex-wrap: wrap;
      }

      .tier-btn-minimal {
        background:
          radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.08) 0.8px, transparent 0.8px),
          radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.06) 0.6px, transparent 0.6px),
          radial-gradient(circle at 40% 80%, rgba(0, 0, 0, 0.04) 0.7px, transparent 0.7px),
          radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
          radial-gradient(circle at 10% 90%, rgba(0, 0, 0, 0.06) 0.5px, transparent 0.5px),
          rgba(15, 23, 42, 0.8);
        background-size: 2.5px 2.5px, 2px 2px, 1.8px 1.8px, 3px 3px, 2.2px 2.2px, 100% 100%;
        border: 1px solid var(--tier-color);
        border-radius: 6px;
        color: #ffffff;
        cursor: pointer;
        padding: 0.3rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 700;
        font-family: inherit;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        min-width: 28px;
        text-align: center;
        backdrop-filter: blur(8px);
        position: relative;
        overflow: hidden;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
        box-shadow:
          0 2px 4px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1),
          inset 0 -1px 0 rgba(0, 0, 0, 0.1);
      }

      .tier-btn-minimal:hover {
        transform: translateY(-1px);
        border-color: var(--tier-color);
        box-shadow:
          0 3px 8px rgba(0, 0, 0, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.15),
          inset 0 -1px 0 rgba(0, 0, 0, 0.15);
        filter: brightness(1.1) saturate(1.1);
      }

      .tier-btn-minimal.active {
        border-color: var(--tier-color);
        color: var(--tier-color);
        box-shadow:
          0 2px 6px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          inset 0 -1px 0 rgba(0, 0, 0, 0.1);
        background:
          radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.12) 0.8px, transparent 0.8px),
          radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.08) 0.6px, transparent 0.6px),
          radial-gradient(circle at 40% 80%, rgba(0, 0, 0, 0.06) 0.7px, transparent 0.7px),
          radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
          radial-gradient(circle at 10% 90%, rgba(0, 0, 0, 0.08) 0.5px, transparent 0.5px),
          rgba(15, 23, 42, 0.9);
        background-size: 2.5px 2.5px, 2px 2px, 1.8px 1.8px, 3px 3px, 2.2px 2.2px, 100% 100%;
      }

      .tier-btn-minimal.active:hover {
        box-shadow:
          0 3px 8px rgba(0, 0, 0, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          inset 0 -1px 0 rgba(0, 0, 0, 0.15);
        filter: brightness(1.1) saturate(1.1);
      }

      /* МИНИАТЮРНАЯ КНОПКА СБРОСА */
      .tier-reset-minimal {
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.3);
        border-radius: 6px;
        color: #ef4444;
        cursor: pointer;
        padding: 0.3rem;
        font-family: inherit;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(8px);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: auto;
        position: relative;
        overflow: hidden;
      }

      .tier-reset-minimal svg {
        width: 12px;
        height: 12px;
      }

      .tier-reset-minimal::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #ef4444;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 0;
      }

      .tier-reset-minimal:hover::before {
        opacity: 0.1;
      }

      .tier-reset-minimal:hover {
        transform: translateY(-2px);
        border-color: #ef4444;
        box-shadow:
          0 8px 25px rgba(0, 0, 0, 0.3),
          0 0 20px rgba(239, 68, 68, 0.3);
      }

      .tier-reset-minimal svg {
        position: relative;
        z-index: 1;
      }

      /* МОБИЛЬНАЯ АДАПТАЦИЯ */
      @media (max-width: 768px) {
        .tier-filter-minimal {
          padding: 1rem;
          gap: 1rem;
        }

        .tier-buttons-minimal {
          gap: 0.5rem;
        }

        .tier-btn-minimal {
          padding: 0.6rem 0.8rem;
          font-size: 0.9rem;
          min-width: 42px;
        }

        .tier-reset-minimal {
          padding: 0.6rem;
          margin-left: 0;
        }
      }

      @media (max-width: 480px) {
        .tier-filter-minimal {
          flex-direction: column;
          align-items: stretch;
          gap: 0.75rem;
        }

        .tier-label-minimal {
          justify-content: center;
        }

        .tier-buttons-minimal {
          justify-content: center;
        }

        .tier-reset-minimal {
          align-self: center;
          margin-left: 0;
        }
      }





      /* Мобильная адаптация */
      @media (max-width: 768px) {
        .tier-filter-horizontal {
          padding: 0.75rem;
          gap: 0.75rem;
        }

        .tier-label {
          font-size: 0.9rem;
        }

        .tier-buttons-row {
          gap: 0.4rem;
        }

        .tier-btn {
          padding: 0.4rem 0.6rem;
          font-size: 0.9rem;
          min-width: 40px;
        }

        .tier-reset-btn {
          padding: 0.4rem;
        }
      }
    `;

    document.head.appendChild(styles);
  }
}

// Создаем глобальный экземпляр
export const tierFilter = new TierFilter();

export default TierFilter;
