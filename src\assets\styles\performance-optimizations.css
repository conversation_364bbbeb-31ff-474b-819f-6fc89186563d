/* УПРОЩЕННЫЕ ОПТИМИЗАЦИИ ПРОИЗВОДИТЕЛЬНОСТИ CSS */

/* GPU ускорение для критических элементов */
.tank-item,
.tank-icon {
  transform: translateZ(0);
  will-change: transform;
}

/* SIDEBAR БЕЗ will-change - для стабильности при масштабировании */
.sidebar {
  transform: translateZ(0);
  will-change: auto;
}

/* Оптимизация контейнеров - используется в tank-list */
.performance-optimized {
  contain: layout style;
  content-visibility: auto;
}

/* Ленивая загрузка изображений - используется для иконок танков */
.lazy-image {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-image.loaded {
  opacity: 1;
}

/* Уменьшение анимаций для слабых устройств */
@media (prefers-reduced-motion: reduce) {
  html * {
    animation-duration: 0.01ms;
    transition-duration: 0.01ms;
    scroll-behavior: auto;
  }
}

/* Оптимизация для мобильных устройств */
@media (max-width: 768px) {
  .tank-item {
    transform: none; /* Отключаем GPU ускорение на мобильных */
  }

  /* Убираем конфликтующие стили для tank-list - используем основные стили из tank-list.css */

  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }
}

/* Оптимизация для планшетов - ОТКЛЮЧЕНА, используем основные стили */
/* @media (min-width: 769px) and (max-width: 1024px) {
  #tank-list {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }
} */

/* Оптимизация для больших экранов - ОТКЛЮЧЕНА, используем основные стили */
/* @media (min-width: 1440px) {
  #tank-list {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
  }
} */

/* Принт стили */
@media print {
  body .sidebar,
  body .modal,
  body .loading-overlay {
    display: none;
  }
  
  .tank-item {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}

/* Оптимизация фокуса */
.focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  border-radius: var(--border-radius);
}

/* Скрытие элементов для screen readers */
body .visually-hidden,
html .visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Оптимизация загрузки шрифтов удалена - используем только Inter */

/* Критические стили для предотвращения FOUC */
.critical-content {
  visibility: visible;
  opacity: 1;
}

/* Оптимизация для низкой пропускной способности */
@media (prefers-reduced-data: reduce) {
  .tank-icon {
    background-image: none;
  }
  
  .glassmorphism {
    backdrop-filter: none;
    background: var(--surface-color);
  }
}

/* Стили для состояния загрузки */
.loading-state {
  pointer-events: none;
  opacity: 0.7;
  cursor: wait;
}

.loading-state * {
  pointer-events: none;
}

/* Оптимизация для touch устройств */
@media (pointer: coarse) {
  .tank-item {
    min-height: 44px; /* Минимальный размер для touch */
    padding: 0.75rem;
  }
  
  .sidebar-menu-item {
    min-height: 44px;
    padding: 0.75rem 1rem;
  }
}

/* Оптимизация для устройств с высоким DPI */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .tank-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Удалены неиспользуемые экспериментальные стили */

/* Дублирующиеся стили удалены - используются из base/animations.css */
