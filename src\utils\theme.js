// src/utils/theme.js
// Theme initialization and related helpers

export function initTheme() {
  const savedTheme = localStorage.getItem('theme') || 'dark';
  document.documentElement.setAttribute('data-theme', savedTheme);
  const body = document.body;

  // Мгновенно применяем правильный фон без серого мигания
  if (savedTheme === 'dark') {
    body.classList.add('gray-bg');
    // Устанавливаем правильный фон с !important
    body.style.setProperty('background-color', '#2a2155', 'important');
    body.style.setProperty('background-image', 'radial-gradient(circle at 30% 20%, rgba(85, 64, 174, 0.5) 0%, transparent 45%), radial-gradient(circle at 80% 80%, rgba(123, 31, 162, 0.4) 0%, transparent 50%)', 'important');
    body.style.setProperty('transition', 'none', 'important');
    body.style.setProperty('animation', 'none', 'important');

    // Также применяем к app-container для гарантии
    setTimeout(() => {
      const appContainer = document.querySelector('.app-container');
      if (appContainer) {
        appContainer.style.setProperty('transition', 'none', 'important');
        appContainer.style.setProperty('animation', 'none', 'important');
      }
    }, 0);
  } else {
    body.classList.remove('gray-bg');
    // Сбрасываем inline стили для светлой темы
    body.style.backgroundColor = '';
    body.style.backgroundImage = '';
  }
}
