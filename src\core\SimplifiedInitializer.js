/**
 * УПРОЩЕННЫЙ ИНИЦИАЛИЗАТОР ПРИЛОЖЕНИЯ
 * Заменяет избыточный UltraAppInitializer простыми утилитами для main.js
 * Устраняет дублирование кода и упрощает архитектуру
 */

import { domCache } from '../utils/CacheMigrationAdapter.js';

/**
 * УПРОЩЕННЫЙ КЛАСС ИНИЦИАЛИЗАТОРА
 * Предоставляет только необходимые утилиты для main.js
 */
class SimplifiedInitializer {
  constructor() {
    this.currentProgress = 0;
    this.startTime = performance.now();
    this.isInitialized = false;
    
    // Элементы прогресса
    this.progressElement = null;
    this.statusElement = null;
  }

  /**
   * НАСТРОЙКА ОТСЛЕЖИВАНИЯ ПРОГРЕССА
   */
  setupProgressTracking() {
    this.progressElement = domCache.getCachedElement('#loading-progress') || 
                          domCache.getCachedElement('#ultra-instant-progress-fill');
    this.statusElement = domCache.getCachedElement('#loading-status') || 
                        domCache.getCachedElement('#ultra-instant-status');
    
    if (!this.progressElement || !this.statusElement) {
      console.warn('Progress elements not found, progress tracking disabled');
      return false;
    }
    
    return true;
  }

  /**
   * ОБНОВЛЕНИЕ ПРОГРЕССА
   */
  updateProgress(progress, status) {
    this.currentProgress = Math.min(100, Math.max(0, progress));
    
    if (this.progressElement) {
      this.progressElement.style.width = `${this.currentProgress}%`;
    }
    
    if (this.statusElement && status) {
      this.statusElement.textContent = status;
    }
    
    // Глобальная функция для совместимости
    if (window.updateProgress) {
      window.updateProgress(this.currentProgress, status);
    }
  }

  /**
   * СКРЫТИЕ ЭКРАНА ЗАГРУЗКИ
   */
  async hideLoadingScreen() {
    const loaders = [
      domCache.getCachedElement('#modern-loading'),
      domCache.getCachedElement('#ultra-instant-loading'),
      domCache.getCachedElement('.loading-overlay')
    ].filter(Boolean);
    
    for (const loader of loaders) {
      loader.style.opacity = '0';
      loader.style.pointerEvents = 'none';
      
      setTimeout(() => {
        loader.style.display = 'none';
      }, 300);
    }
    
    this.isInitialized = true;
  }

  /**
   * ОБРАБОТКА ОШИБОК
   */
  showError(error) {
    console.error('Initialization error:', error);
    
    const errorHTML = `
      <div id="app-error-overlay" class="error-overlay">
        <div class="error-container">
          <div class="error-icon">⚠️</div>
          <div class="error-text">Ошибка инициализации: ${error.message}</div>
          <button onclick="location.reload()" class="error-button">Обновить страницу</button>
        </div>
      </div>
    `;
    document.body.insertAdjacentHTML('beforeend', errorHTML);
  }

  /**
   * УТИЛИТА ДЛЯ ЗАДЕРЖКИ
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * ПОЛУЧЕНИЕ МЕТРИК ПРОИЗВОДИТЕЛЬНОСТИ
   */
  getMetrics() {
    return {
      initTime: performance.now() - this.startTime,
      isInitialized: this.isInitialized,
      currentProgress: this.currentProgress
    };
  }
}

// Создаем единственный экземпляр
export const simplifiedInitializer = new SimplifiedInitializer();

// Экспортируем для обратной совместимости
export const appInitializer = simplifiedInitializer;
export const UltraAppInitializer = SimplifiedInitializer;
export const ultraAppInitializer = simplifiedInitializer;

export default simplifiedInitializer;
