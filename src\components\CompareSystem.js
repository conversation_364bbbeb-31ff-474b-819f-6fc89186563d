/**
 * УНИФИЦИРОВАННАЯ СИСТЕМА СРАВНЕНИЯ ТАНКОВ
 * Объединяет функциональность старой и новой систем сравнения
 */

import { getTankTypeClass, getFlagPath, getTankIconPath } from '../utils/constants.js';
import { cacheManager } from '../utils/CacheMigrationAdapter.js';
import { BaseCompareSystem } from './base/BaseCompareSystem.js';

export class UnifiedCompareSystem extends BaseCompareSystem {
  constructor() {
    super('compareSystemState', 8);

    this.minTanksForComparison = 1;
    this.isFloatingWindowVisible = false;
    this.debouncedUpdate = this.debounce(this.updateFloatingWindow.bind(this), 50);
    this.domCache = new Map();
    this.isUpdating = false;

    this.initializeSystem();
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ СИСТЕМЫ
   */
  initializeSystem() {
    this.restoreState();
    this.createFloatingWindow();
    this.setupEventListeners();
    this.addBadgesToExistingCards();
    console.log('✅ Unified Compare System initialized');
  }

  /**
   * ПРОСТАЯ ФУНКЦИЯ DEBOUNCE
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * ВОССТАНОВЛЕНИЕ СОСТОЯНИЯ ИЗ LOCALSTORAGE
   */
  restoreState() {
    try {
      const savedState = localStorage.getItem(this.storageKey);
      if (!savedState) return;

      const state = JSON.parse(savedState);

      // Проверяем, не слишком ли старое состояние (24 часа)
      const maxAge = 24 * 60 * 60 * 1000; // 24 часа
      if (Date.now() - state.timestamp > maxAge) {
        localStorage.removeItem(this.storageKey);
        return;
      }

      // Восстанавливаем танки
      if (state.tanks && Array.isArray(state.tanks)) {
        this.tanks = state.tanks.map(tankData => {
          if (!tankData) return null;

          // Пытаемся найти полные данные танка
          const fullTankData = this.getTankDataByName(tankData.name);
          if (fullTankData) {
            return { ...fullTankData, id: tankData.id };
          }

          // Если не нашли полные данные, используем сохраненные
          return tankData;
        });

        if (import.meta.env?.MODE === 'development') {
          const restoredCount = this.tanks.filter(tank => tank !== null).length;
          console.log(`🔄 Compare system state restored: ${restoredCount} tanks`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to restore compare system state:', error);
      // Очищаем поврежденное состояние
      localStorage.removeItem(this.storageKey);
    }
  }

  /**
   * КЭШИРОВАНИЕ DOM ЭЛЕМЕНТОВ ДЛЯ ПРОИЗВОДИТЕЛЬНОСТИ
   */
  getCachedElement(selector) {
    if (!this.domCache.has(selector)) {
      const element = document.querySelector(selector);
      if (element) {
        this.domCache.set(selector, element);
      }
    }
    return this.domCache.get(selector);
  }

  /**
   * ОЧИСТКА КЭША DOM ЭЛЕМЕНТОВ
   */
  clearDOMCache() {
    this.domCache.clear();
  }

  /**
   * ОПТИМИЗИРОВАННОЕ ДОБАВЛЕНИЕ ТАНКА
   */
  addTank(tank, slotIndex = null) {
    if (!tank) return false;

    // Найти свободный слот
    if (slotIndex === null) {
      slotIndex = this.tanks.findIndex(slot => slot === null);
      if (slotIndex === -1) return false;
    }

    if (slotIndex < 0 || slotIndex >= this.maxTanks) return false;

    // Добавляем танк
    this.tanks[slotIndex] = { ...tank, id: tank.id || this.createTankId(tank.name) };

    // Обновляем UI
    this.updateTankCardBadge(tank.name, true);
    this.debouncedUpdate();

    // Сохраняем состояние
    this.saveState();

    // Логирование только в режиме разработки
    if (import.meta.env?.MODE === 'development') {
      console.log(`✅ Tank added: ${tank.name}`);
    }

    return true;
  }

  /**
   * УДАЛИТЬ ТАНК ИЗ СРАВНЕНИЯ
   */
  removeTank(slotIndex) {
    if (slotIndex < 0 || slotIndex >= this.maxTanks) return false;

    const removedTank = this.tanks[slotIndex];

    // Логирование только в режиме разработки
    if (import.meta.env?.MODE === 'development') {
      console.log('🗑️ Removing tank from slot', slotIndex, ':', removedTank?.name || 'null');
    }

    if (removedTank) {
      this.updateTankCardBadge(removedTank.name, false);
    }

    this.tanks[slotIndex] = null;

    // Логирование только в режиме разработки
    if (import.meta.env?.MODE === 'development') {
      console.log('🔍 Tanks after removal:', this.tanks.map((t, i) => `${i}: ${t?.name || 'null'}`));
    }

    this.updateFloatingWindow();
    this.updateCompareButton();

    // Сохраняем состояние
    this.saveState();

    return true;
  }

  /**
   * УДАЛИТЬ ТАНК ПО ИМЕНИ
   */
  removeTankByName(tankName) {
    const index = this.tanks.findIndex(tank => tank && tank.name === tankName);
    if (index !== -1) {
      return this.removeTank(index);
    }
    return false;
  }

  /**
   * ДУБЛИРОВАТЬ ТАНК (добавить копию)
   */
  duplicateTank(sourceIndex) {
    console.log('🔄 Duplicating tank from slot:', sourceIndex);

    if (sourceIndex < 0 || sourceIndex >= this.maxTanks) {
      console.log('❌ Invalid source index:', sourceIndex);
      return false;
    }

    const sourceTank = this.tanks[sourceIndex];
    if (!sourceTank) {
      console.log('❌ No tank in source slot:', sourceIndex);
      return false;
    }

    // Найти первый свободный слот
    const freeSlotIndex = this.tanks.findIndex(slot => slot === null);
    if (freeSlotIndex === -1) {
      console.log('⚠️ No free slots available for duplication');
      return false;
    }

    // Логирование только в режиме разработки
    if (import.meta.env?.MODE === 'development') {
      console.log('🎯 Duplicating tank:', sourceTank.name, 'to slot:', freeSlotIndex);
    }

    // Создаем копию танка (без изменения ID - просто дублируем)
    const duplicatedTank = { ...sourceTank };

    this.tanks[freeSlotIndex] = duplicatedTank;

    // Логирование только в режиме разработки
    if (import.meta.env?.MODE === 'development') {
      console.log('✅ Tank duplicated successfully');
    }

    this.updateFloatingWindow();
    this.updateCompareButton();

    return true;
  }

  /**
   * ОЧИСТИТЬ ВСЕ ТАНКИ
   */
  clearAll() {
    // Убираем бейджики со всех танков
    this.tanks.forEach(tank => {
      if (tank) {
        this.updateTankCardBadge(tank.name, false);
      }
    });

    this.tanks = new Array(8).fill(null);
    this.updateFloatingWindow();
    this.updateCompareButton();
    this.hideResults();

    // Сохраняем состояние
    this.saveState();
  }

  /**
   * СОЗДАНИЕ ВСПЛЫВАЮЩЕГО ОКНА
   */
  createFloatingWindow() {
    // Удаляем существующее окно если есть
    const existingWindow = document.getElementById('compare-floating-window');
    if (existingWindow) {
      existingWindow.remove();
    }

    const floatingWindow = document.createElement('div');
    floatingWindow.id = 'compare-floating-window';
    floatingWindow.className = 'compare-floating-window hidden';

    floatingWindow.innerHTML = `
      <div class="floating-window-header">
        <div class="floating-window-title">
          <svg class="floating-window-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 13a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 9a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 20h14" />
          </svg>
          <span>Сравнение</span>
          <span class="tank-count">0/8</span>
        </div>
        <button class="floating-window-close">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      <div class="floating-window-content">
        <div class="floating-tanks-list" id="floating-tanks-list">
          <!-- Танки будут добавлены динамически -->
        </div>
        <div class="floating-window-actions">
          <button class="floating-action-btn compare-btn" disabled>
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
            Сравнить
          </button>
          <button class="floating-action-btn clear-btn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
            Очистить
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(floatingWindow);

    // Добавляем event listeners для кнопок в окне
    this.setupFloatingWindowEventListeners(floatingWindow);
  }

  /**
   * НАСТРОЙКА EVENT LISTENERS ДЛЯ ВСПЛЫВАЮЩЕГО ОКНА
   */
  setupFloatingWindowEventListeners(floatingWindow) {
    // Удаляем старые listeners если есть
    const oldListener = floatingWindow._compareSystemListener;
    if (oldListener) {
      floatingWindow.removeEventListener('click', oldListener);
    }

    // Event delegation для кнопок танков
    const newListener = (event) => {
      const button = event.target.closest('button[data-action]');
      if (!button) return;

      event.stopPropagation();
      event.preventDefault();

      const action = button.dataset.action;
      const index = parseInt(button.dataset.index);

      console.log(`🎯 Floating window button clicked: ${action} for index ${index}`);

      if (action === 'duplicate') {
        this.duplicateTank(index);
      } else if (action === 'remove') {
        this.removeTank(index);
      }
    };

    floatingWindow.addEventListener('click', newListener);
    floatingWindow._compareSystemListener = newListener;

    // Исправляем кнопку закрытия
    const closeBtn = floatingWindow.querySelector('.floating-window-close');
    if (closeBtn) {
      closeBtn.onclick = null; // Убираем старый onclick
      closeBtn.addEventListener('click', (event) => {
        event.stopPropagation();
        this.hideFloatingWindow();
      });
    }

    // Исправляем кнопку сравнения
    const compareBtn = floatingWindow.querySelector('.compare-btn');
    if (compareBtn) {
      compareBtn.onclick = null;
      compareBtn.addEventListener('click', (event) => {
        event.stopPropagation();
        if (!compareBtn.disabled) {
          // Переключаемся на вкладку сравнения
          if (window.onMenuSelected) {
            window.onMenuSelected('compare');
          }
        }
      });
    }

    // Исправляем кнопку очистки
    const clearBtn = floatingWindow.querySelector('.clear-btn');
    if (clearBtn) {
      clearBtn.onclick = null;
      clearBtn.addEventListener('click', (event) => {
        event.stopPropagation();
        this.clearAll();
      });
    }
  }

  /**
   * Обновить отображение слота
   */
  updateSlotDisplay(slotIndex) {
    const slot = document.querySelector(`[data-slot="${slotIndex + 1}"]`);
    if (!slot) return;

    const content = slot.querySelector('.tank-slot-content');
    const tank = this.tanks[slotIndex];

    if (tank) {
      const typeClass = getTankTypeClass(tank.type);
      const flagPath = getFlagPath(tank.country);

      content.innerHTML = `
        <div class="modern-tank-card">
          <div class="tank-card-background"></div>
          <div class="tank-card-content">
            <div class="tank-card-top">
              <div class="tank-icon-area">
                <img src="${this.getTankIconPath(tank.name)}" alt="${tank.name}" class="tank-card-icon" onerror="this.style.opacity='0.5'; this.style.background='#374151'; this.onerror=null;">
              </div>
              <div class="tank-flag-area">
                <img src="${flagPath}" alt="${tank.country}" class="tank-card-flag">
              </div>
            </div>
            <div class="tank-card-bottom">
              <div class="tank-card-name">${tank.name}</div>
              <div class="tank-card-badge ${typeClass}">${tank.type}</div>
            </div>
          </div>
          <button class="tank-card-remove" onclick="compareSystem.removeTank(${slotIndex})" title="Удалить танк">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      `;
      slot.classList.add('has-tank');
    } else {
      content.innerHTML = `
        <div class="tank-placeholder">
          <svg class="placeholder-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          <span class="placeholder-text">Выберите танк</span>
        </div>
      `;
      slot.classList.remove('has-tank');
    }
  }

  /**
   * ОБНОВЛЕНИЕ БЕЙДЖИКА СРАВНЕНИЯ НА КАРТОЧКЕ ТАНКА
   */
  updateTankCardBadge(tankName, isInComparison) {
    const tankCards = document.querySelectorAll('.tank-item');
    tankCards.forEach(card => {
      if (card.dataset.tankName === tankName) {
        let compareBadge = card.querySelector('.tank-compare-badge');

        // Создаем бейджик если его нет
        if (!compareBadge) {
          compareBadge = document.createElement('div');
          compareBadge.className = 'tank-compare-badge';
          compareBadge.innerHTML = `
            <svg class="compare-plus-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            <svg class="compare-check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
          `;
          card.appendChild(compareBadge);
        }

        // Обновляем состояние бейджика
        if (isInComparison) {
          compareBadge.classList.add('active');
        } else {
          compareBadge.classList.remove('active');
        }
      }
    });
  }

  /**
   * Обновить кнопку сравнения
   */
  updateCompareButton() {
    const compareBtn = document.getElementById('compare-btn');
    const floatingCompareBtn = document.querySelector('.floating-action-btn.compare-btn');

    const activeTanks = this.tanks.filter(tank => tank !== null);
    const canCompare = activeTanks.length >= this.minTanksForComparison;

    [compareBtn, floatingCompareBtn].forEach(btn => {
      if (btn) {
        btn.disabled = !canCompare;
        if (canCompare) {
          const tankWord = activeTanks.length === 1 ? 'танк' : 'танков';
          btn.textContent = btn.classList.contains('floating-action-btn') ? 'Сравнить' : `Сравнить ${activeTanks.length} ${tankWord}`;
        } else {
          btn.textContent = btn.classList.contains('floating-action-btn') ? 'Сравнить' : 'Выберите минимум 1 танк';
        }
      }
    });
  }

  /**
   * Показать результаты сравнения
   */
  showResults() {
    const activeTanks = this.tanks.filter(t => t !== null);

    // Проверяем минимальное количество танков
    if (activeTanks.length < this.minTanksForComparison) {
      if (import.meta.env?.MODE === 'development') {
        console.warn('❌ Not enough tanks for comparison:', activeTanks.length);
      }
      return;
    }

    // Логирование только в режиме разработки
    if (import.meta.env?.MODE === 'development') {
      console.log('🎯 Comparing', activeTanks.length, 'tanks');
    }

    const resultsArea = document.getElementById('compare-results');
    const advancedResultsArea = document.getElementById('advanced-results-content');

    if (resultsArea) {
      resultsArea.classList.remove('hidden');
      resultsArea.style.display = 'block';
      resultsArea.style.visibility = 'visible';
      resultsArea.style.opacity = '1';
    }

    // Используем новую систему если доступна
    if (advancedResultsArea && activeTanks.length >= 2) {
      this.generateAdvancedComparisonTable();

      // Прокручиваем к результатам
      setTimeout(() => {
        resultsArea?.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }, 300);
    } else {
      this.generateComparisonTable();
    }
  }

  /**
   * Скрыть результаты сравнения
   */
  hideResults() {
    const resultsArea = document.getElementById('compare-results');
    if (!resultsArea) return;

    resultsArea.classList.add('hidden');
  }

  /**
   * ОПТИМИЗИРОВАННОЕ ОБНОВЛЕНИЕ ВСПЛЫВАЮЩЕГО ОКНА
   */
  updateFloatingWindow() {
    // Предотвращаем множественные обновления
    if (this.isUpdating) return;
    this.isUpdating = true;

    const floatingWindow = this.getCachedElement('#compare-floating-window');
    if (!floatingWindow) {
      this.isUpdating = false;
      return;
    }

    const tanksList = this.getCachedElement('#floating-tanks-list');
    const tankCount = this.tanks.filter(tank => tank !== null).length;

    // Обновляем счетчик
    const countElement = floatingWindow.querySelector('.tank-count');
    if (countElement) {
      countElement.textContent = `${tankCount}/8`;
    }

    // Показываем окно когда добавляется первый танк
    const isCurrentlyVisible = !floatingWindow.classList.contains('hidden');

    if (tankCount > 0 && !isCurrentlyVisible) {
      this.showFloatingWindow();
    }
    // Не скрываем окно автоматически - пользователь сам закроет кнопкой X

    // Обновляем список танков
    if (tanksList) {
      tanksList.innerHTML = this.tanks
        .map((tank, index) => {
          if (!tank) return '';

          return `
            <div class="floating-tank-item" data-index="${index}">
              <div class="floating-tank-info">
                <img src="${this.getTankIconPath(tank.name)}" alt="${tank.name}" class="floating-tank-icon" onerror="this.style.opacity='0.5'; this.style.background='#374151'; this.onerror=null;">
                <div class="floating-tank-details">
                  <span class="floating-tank-name">${tank.name}</span>
                  <span class="floating-tank-type">${tank.type} • Ур. ${tank.level}</span>
                </div>
              </div>
              <div class="floating-tank-controls">
                <button class="floating-tank-duplicate" data-action="duplicate" data-index="${index}" title="Добавить копию">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                  </svg>
                </button>
                <button class="floating-tank-remove" data-action="remove" data-index="${index}" title="Удалить">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                  </svg>
                </button>
              </div>
            </div>
          `;
        })
        .filter(html => html !== '')
        .join('');
    }

    this.updateCompareButton();

    // Переустанавливаем event listeners после обновления HTML
    this.setupFloatingWindowEventListeners(floatingWindow);

    // Сбрасываем флаг обновления
    this.isUpdating = false;
  }

  /**
   * ПОКАЗАТЬ ВСПЛЫВАЮЩЕЕ ОКНО
   */
  showFloatingWindow() {
    const floatingWindow = document.getElementById('compare-floating-window');
    if (floatingWindow) {
      floatingWindow.classList.remove('hidden');
      this.isFloatingWindowVisible = true;
    }
  }

  /**
   * СКРЫТЬ ВСПЛЫВАЮЩЕЕ ОКНО
   */
  hideFloatingWindow() {
    const floatingWindow = document.getElementById('compare-floating-window');
    if (floatingWindow) {
      floatingWindow.classList.add('hidden');
      this.isFloatingWindowVisible = false;
    }
  }

  /**
   * Генерировать таблицу сравнения
   */
  generateComparisonTable() {
    const resultsContent = document.querySelector('.results-content');
    if (!resultsContent || !this.tanks[0] || !this.tanks[1]) return;

    const tank1 = this.tanks[0];
    const tank2 = this.tanks[1];

    resultsContent.innerHTML = `
      <div class="comparison-table">
        <div class="comparison-header">
          <div class="comparison-category">Характеристика</div>
          <div class="comparison-tank">
            <div class="comparison-tank-icon-wrapper">
              <img src="${this.getTankIconPath(tank1.name)}" alt="${tank1.name}" class="comparison-tank-icon" onerror="this.style.opacity='0.5'; this.style.background='#374151'; this.onerror=null;">
            </div>
            <div class="comparison-tank-info">
              <div class="comparison-tank-header">
                <img src="${getFlagPath(tank1.country)}" alt="${tank1.country}" class="comparison-flag">
                <div class="comparison-tank-badge ${getTankTypeClass(tank1.type)}">${tank1.type}</div>
              </div>
              <span class="comparison-tank-name">${tank1.name}</span>
            </div>
          </div>
          <div class="comparison-tank">
            <div class="comparison-tank-icon-wrapper">
              <img src="${this.getTankIconPath(tank2.name)}" alt="${tank2.name}" class="comparison-tank-icon" onerror="this.style.opacity='0.5'; this.style.background='#374151'; this.onerror=null;">
            </div>
            <div class="comparison-tank-info">
              <div class="comparison-tank-header">
                <img src="${getFlagPath(tank2.country)}" alt="${tank2.country}" class="comparison-flag">
                <div class="comparison-tank-badge ${getTankTypeClass(tank2.type)}">${tank2.type}</div>
              </div>
              <span class="comparison-tank-name">${tank2.name}</span>
            </div>
          </div>
        </div>

        ${this.generateComparisonRow('Урон', tank1, tank2, 'damage', '')}
        ${this.generateComparisonRow('Бронепробитие', tank1, tank2, 'penetration', ' мм')}
        ${this.generateComparisonRow('Прочность', tank1, tank2, 'hitPoints', ' HP')}
        ${this.generateComparisonRow('Максимальная скорость', tank1, tank2, 'speed', ' км/ч')}
        ${this.generateComparisonRow('Обзор', tank1, tank2, 'viewRange', ' м')}
        ${this.generateComparisonRow('Время перезарядки', tank1, tank2, 'reloadTime', ' сек', true)}
        ${this.generateComparisonRow('Время сведения', tank1, tank2, 'aimTime', ' сек', true)}
        ${this.generateComparisonRow('Разброс', tank1, tank2, 'dispersion', ' м', true)}
        ${this.generateComparisonRow('Мощность двигателя', tank1, tank2, 'enginePower', ' л.с.')}
        ${this.generateComparisonRow('Скорость поворота', tank1, tank2, 'traverse', ' °/сек')}
      </div>
    `;
  }

  /**
   * Генерировать строку сравнения
   */
  generateComparisonRow(label, tank1, tank2, stat, unit = '', isLowerBetter = false) {
    const getStatValue = (tank, stat, defaultValue = 'Н/Д') => {
      if (
        tank.characteristics &&
        tank.characteristics[stat] !== undefined &&
        tank.characteristics[stat] !== null
      ) {
        return tank.characteristics[stat];
      }
      if (tank[stat] !== undefined && tank[stat] !== null) {
        return tank[stat];
      }
      return defaultValue;
    };

    const val1 = getStatValue(tank1, stat);
    const val2 = getStatValue(tank2, stat);

    let class1 = 'equal',
      class2 = 'equal';

    if (val1 !== 'Н/Д' && val2 !== 'Н/Д') {
      const num1 = parseFloat(val1);
      const num2 = parseFloat(val2);

      if (!isNaN(num1) && !isNaN(num2)) {
        if (isLowerBetter) {
          class1 = num1 < num2 ? 'better' : num1 > num2 ? 'worse' : 'equal';
          class2 = num2 < num1 ? 'better' : num2 > num1 ? 'worse' : 'equal';
        } else {
          class1 = num1 > num2 ? 'better' : num1 < num2 ? 'worse' : 'equal';
          class2 = num2 > num1 ? 'better' : num2 < num1 ? 'worse' : 'equal';
        }
      }
    }

    const displayVal1 = val1 === 'Н/Д' ? val1 : val1 + unit;
    const displayVal2 = val2 === 'Н/Д' ? val2 : val2 + unit;

    return `
      <div class="comparison-row">
        <div class="comparison-category">${label}</div>
        <div class="comparison-value ${class1}">${displayVal1}</div>
        <div class="comparison-value ${class2}">${displayVal2}</div>
      </div>
    `;
  }

  /**
   * УВЕДОМЛЕНИЯ
   */
  showMaxTanksNotification() {
    this.showNotification('Достигнуто максимальное количество танков для сравнения (8)', 'warning');
  }

  showDuplicateNotification(tankName) {
    this.showNotification(`Танк "${tankName}" уже добавлен в сравнение`, 'info');
  }

  showNotification(message, type = 'info') {
    // Используем существующую систему уведомлений если есть
    if (window.notificationManager) {
      window.notificationManager.show(message, type);
    } else {
      // Простое уведомление
      console.log(`[${type.toUpperCase()}] ${message}`);
    }
  }

  /**
   * НАСТРОЙКА ОБРАБОТЧИКОВ СОБЫТИЙ
   */
  setupEventListeners() {
    // Глобальный обработчик кликов по бейджикам сравнения
    document.addEventListener('click', (event) => {
      const compareBadge = event.target.closest('.tank-compare-badge');
      if (compareBadge) {
        // Логирование только в режиме разработки
        if (import.meta.env?.MODE === 'development') {
          console.log('🎯 Global click handler triggered');
        }

        event.stopPropagation();
        event.preventDefault();

        // Защита от множественных кликов
        if (compareBadge.dataset.processing === 'true') {
          if (import.meta.env?.MODE === 'development') {
            console.log('⏳ Badge is already being processed, ignoring click');
          }
          return;
        }

        compareBadge.dataset.processing = 'true';

        // Добавляем визуальную обратную связь
        compareBadge.style.transform = 'scale(0.9)';
        setTimeout(() => {
          compareBadge.style.transform = '';
          compareBadge.dataset.processing = 'false';
        }, 300);

        const tankCard = compareBadge.closest('.tank-item');
        const tankName = tankCard?.dataset.tankName;

        if (tankName) {
          console.log('🎯 Compare badge clicked for:', tankName);

          // Проверяем текущее состояние бейджика
          const isCurrentlyActive = compareBadge.classList.contains('active');

          if (isCurrentlyActive) {
            // Бейджик активен - удаляем танк
            console.log('🗑️ Removing tank from comparison:', tankName);
            const existingIndex = this.tanks.findIndex(t => t && t.name === tankName);
            if (existingIndex !== -1) {
              this.removeTank(existingIndex);
            }
          } else {
            // Бейджик неактивен - добавляем танк
            console.log('➕ Adding tank to comparison:', tankName);
            const tankData = this.getTankDataByName(tankName);
            if (tankData) {
              const success = this.addTank(tankData);
              if (!success) {
                console.log('❌ Failed to add tank:', tankName);
              }
            }
          }
        }
      }
    });

    // Обработчик для динамического добавления бейджиков к новым карточкам
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Проверяем, добавлены ли новые карточки танков
            let newTankCards = [];

            if (node.querySelectorAll) {
              newTankCards = Array.from(node.querySelectorAll('.tank-item'));
            }

            if (node.classList && node.classList.contains('tank-item')) {
              newTankCards.push(node);
            }

            newTankCards.forEach(card => {
              const tankName = card.dataset.tankName;
              if (tankName && !card.querySelector('.tank-compare-badge')) {
                this.addCompareBadgeToCard(card, tankName);
              }
            });
          }
        });
      });
    });

    // Наблюдаем за изменениями в контейнере списка танков
    const tankListContainer = document.getElementById('tank-list');
    if (tankListContainer) {
      observer.observe(tankListContainer, {
        childList: true,
        subtree: true
      });
    }

    console.log('🔍 Event listeners and mutation observer set up');
  }

  /**
   * ДОБАВИТЬ БЕЙДЖИКИ КО ВСЕМ СУЩЕСТВУЮЩИМ КАРТОЧКАМ
   */
  addBadgesToExistingCards() {
    const tankCards = document.querySelectorAll('.tank-item');
    console.log(`🏷️ Processing ${tankCards.length} tank cards for compare badges`);

    tankCards.forEach(card => {
      const tankName = card.dataset.tankName;
      if (!tankName) return;

      let compareBadge = card.querySelector('.tank-compare-badge');

      if (!compareBadge) {
        // Создаем новый бейджик если его нет
        this.addCompareBadgeToCard(card, tankName);
      }
    });
  }

  /**
   * ПРИКРЕПИТЬ ОБРАБОТЧИК СОБЫТИЙ К БЕЙДЖИКУ
   */
  attachBadgeEventListener(compareBadge, tankName) {
    // Проверяем, есть ли уже обработчик
    if (compareBadge.hasAttribute('data-listener-attached')) {
      return compareBadge;
    }

    // Добавляем обработчик
    const clickHandler = (event) => {
      console.log('🎯 Compare badge clicked for:', tankName);
      event.stopPropagation();
      event.preventDefault();

      if (compareBadge.classList.contains('active')) {
        // Удаляем танк из сравнения
        this.removeTankByName(tankName);
      } else {
        // Добавляем танк в сравнение
        const tankData = this.getTankDataByName(tankName);
        if (tankData) {
          this.addTank(tankData);
        }
      }
    };

    compareBadge.addEventListener('click', clickHandler);
    compareBadge.setAttribute('data-listener-attached', 'true');

    return compareBadge;
  }

  /**
   * ДОБАВИТЬ БЕЙДЖИК СРАВНЕНИЯ К КАРТОЧКЕ
   */
  addCompareBadgeToCard(tankCard, tankName) {
    if (tankCard.querySelector('.tank-compare-badge')) return;

    // Убеждаемся, что состояние восстановлено
    if (this.tanks.every(t => t === null)) {
      this.restoreState();
    }

    // Проверяем, находится ли танк в сравнении
    const isInComparison = this.tanks.some(tank => tank && tank.name === tankName);

    const compareBadge = document.createElement('div');
    compareBadge.className = isInComparison ? 'tank-compare-badge active' : 'tank-compare-badge';
    compareBadge.innerHTML = `
      <svg class="compare-plus-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
      </svg>
      <svg class="compare-check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
      </svg>
    `;

    tankCard.appendChild(compareBadge);
    console.log('🏷️ Compare badge added to:', tankName, 'Active:', isInComparison);
  }

  /**
   * ПОЛУЧИТЬ ДАННЫЕ ТАНКА ПО ИМЕНИ
   */
  getTankDataByName(tankName) {
    // Пробуем разные источники данных
    let allTanks = null;

    if (window.tankManager?.allTanks) {
      allTanks = window.tankManager.allTanks;
    } else if (window.state?.allTanks) {
      allTanks = window.state.allTanks;
    } else if (window.allTanks) {
      allTanks = window.allTanks;
    }

    if (!allTanks) {
      console.error('❌ No tank data source available');
      console.log('Available sources:', {
        tankManager: !!window.tankManager,
        state: !!window.state,
        allTanks: !!window.allTanks
      });
      return null;
    }

    // Ищем танк
    const tank = allTanks.find(tank => tank.name === tankName);

    if (!tank) {
      // Логирование только в режиме разработки
      if (import.meta.env?.MODE === 'development') {
        console.error('❌ Tank not found:', tankName);
        console.log('Available tanks count:', allTanks.length);
      }
    } else {
      // Логирование только в режиме разработки
      if (import.meta.env?.MODE === 'development') {
        console.log('✅ Tank found:', tankName);
      }
    }

    return tank;
  }

  /**
   * ГЕНЕРАЦИЯ РАСШИРЕННОЙ ТАБЛИЦЫ СРАВНЕНИЯ
   */
  generateAdvancedComparisonTable() {
    const resultsContent = document.getElementById('advanced-results-content');
    if (!resultsContent) return;

    const activeTanks = this.tanks.filter(tank => tank !== null);
    if (activeTanks.length < 1) return;

    // Обновляем счетчик танков
    const tanksCounter = document.getElementById('tanks-in-comparison');
    if (tanksCounter) {
      tanksCounter.textContent = activeTanks.length;
    }

    resultsContent.innerHTML = `
      <div class="ultra-comparison-container">
        <div class="ultra-comparison-scroll">
          <div class="advanced-comparison-table">
            <div class="comparison-table-header">
              <div class="comparison-category-header">Характеристика</div>
              ${activeTanks.map((tank, index) => `
                <div class="comparison-tank-header">
                  <div class="tank-header-content">
                    <img src="${this.getTankIconPath(tank.name)}" alt="${tank.name}" class="tank-header-icon" onerror="this.style.opacity='0.5'; this.style.background='#374151'; this.onerror=null;">
                    <div class="tank-header-info">
                      <div class="tank-header-name" title="${tank.name}">${tank.name}</div>
                      <div class="tank-header-details">
                        <img src="${getFlagPath(tank.country)}" alt="${tank.country}" class="tank-header-flag">
                        <span class="tank-header-type ${getTankTypeClass(tank.type)}">${tank.type}</span>
                        <span class="tank-header-level">Ур. ${tank.level}</span>
                      </div>
                    </div>
                  </div>
                </div>
              `).join('')}
            </div>
            <div class="comparison-table-body">
              ${this.generateAdvancedComparisonRows(activeTanks)}
            </div>
          </div>
        </div>
      </div>
    `;

    console.log('✅ Comparison table generated');

    // Прокручиваем к результатам
    setTimeout(() => {
      const resultsArea = document.getElementById('compare-results');
      if (resultsArea) {
        resultsArea.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }

  /**
   * ГЕНЕРАЦИЯ СТРОК РАСШИРЕННОГО СРАВНЕНИЯ
   */
  generateAdvancedComparisonRows(tanks) {
    const characteristics = [
      { label: 'Прочность', key: 'hp', unit: ' HP', isLowerBetter: false },
      { label: 'Урон', key: 'damage', unit: ' HP', isLowerBetter: false },
      { label: 'Пробитие', key: 'penetration', unit: ' мм', isLowerBetter: false },
      { label: 'Скорострельность', key: 'rateOfFire', unit: ' выстр/мин', isLowerBetter: false },
      { label: 'Время сведения', key: 'aimTime', unit: ' с', isLowerBetter: true },
      { label: 'Разброс', key: 'accuracy', unit: ' м', isLowerBetter: true },
      { label: 'Максимальная скорость', key: 'maxSpeed', unit: ' км/ч', isLowerBetter: false },
      { label: 'Мощность двигателя', key: 'enginePower', unit: ' л.с.', isLowerBetter: false },
      { label: 'Удельная мощность', key: 'powerToWeight', unit: ' л.с./т', isLowerBetter: false },
      { label: 'Скорость поворота', key: 'traverse', unit: ' °/сек', isLowerBetter: false }
    ];

    return characteristics.map(char =>
      this.generateAdvancedComparisonRow(char.label, tanks, char.key, char.unit, char.isLowerBetter)
    ).join('');
  }

  /**
   * ГЕНЕРАЦИЯ СТРОКИ РАСШИРЕННОГО СРАВНЕНИЯ
   */
  generateAdvancedComparisonRow(label, tanks, stat, unit = '', isLowerBetter = false) {
    const values = tanks.map(tank => {
      const value = this.getStatValue(tank, stat);
      return { tank, value, numValue: parseFloat(value) };
    });

    // Определяем лучшие и худшие значения
    const numericValues = values.filter(v => !isNaN(v.numValue)).map(v => v.numValue);
    const bestValue = numericValues.length > 0 ? (isLowerBetter ? Math.min(...numericValues) : Math.max(...numericValues)) : null;
    const worstValue = numericValues.length > 0 ? (isLowerBetter ? Math.max(...numericValues) : Math.min(...numericValues)) : null;

    return `
      <div class="comparison-row">
        <div class="comparison-category">${label}</div>
        ${values.map(({ tank, value, numValue }) => {
          let className = 'equal';
          if (!isNaN(numValue) && bestValue !== null) {
            if (numValue === bestValue) className = 'best';
            else if (numValue === worstValue && bestValue !== worstValue) className = 'worst';
          }

          const displayValue = value === 'Н/Д' ? value : value + unit;
          return `<div class="comparison-value ${className}">${displayValue}</div>`;
        }).join('')}
      </div>
    `;
  }

  /**
   * ПОЛУЧИТЬ ЗНАЧЕНИЕ ХАРАКТЕРИСТИКИ
   */
  getStatValue(tank, stat, defaultValue = 'Н/Д') {
    if (tank.characteristics && tank.characteristics[stat] !== undefined && tank.characteristics[stat] !== null) {
      return tank.characteristics[stat];
    }
    if (tank[stat] !== undefined && tank[stat] !== null) {
      return tank[stat];
    }
    return defaultValue;
  }

  /**
   * МЕТОДЫ ДЛЯ ОБРАТНОЙ СОВМЕСТИМОСТИ СО СТАРОЙ СИСТЕМОЙ
   */
  updateLegacySlotDisplay(slotIndex) {
    // Совместимость со старой системой - обновляем слоты если они есть
    const slot = document.querySelector(`[data-slot="${slotIndex + 1}"]`);
    if (!slot) return;

    const content = slot.querySelector('.tank-slot-content');
    if (!content) return;

    const tank = this.tanks[slotIndex];

    if (!tank) {
      content.innerHTML = `
        <div class="tank-placeholder">
          <svg class="placeholder-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          <span class="placeholder-text">Выберите танк</span>
        </div>
      `;
      slot.classList.remove('has-tank');
      return;
    }

    const flagPath = getFlagPath(tank.country);
    const typeClass = getTankTypeClass(tank.type);

    content.innerHTML = `
      <div class="modern-tank-card">
        <div class="tank-card-background"></div>
        <div class="tank-card-content">
          <div class="tank-card-top">
            <div class="tank-icon-area">
              <img src="${this.getTankIconPath(tank.name)}" alt="${tank.name}" class="tank-card-icon" onerror="this.style.opacity='0.5'; this.style.background='#374151'; this.onerror=null;">
            </div>
            <div class="tank-flag-area">
              <img src="${flagPath}" alt="${tank.country}" class="tank-card-flag">
            </div>
          </div>
          <div class="tank-card-bottom">
            <div class="tank-card-name">${tank.name}</div>
            <div class="tank-card-badge ${typeClass}">${tank.type}</div>
          </div>
        </div>
        <button class="tank-card-remove" onclick="advancedCompareSystem.removeTank(${slotIndex})" title="Удалить танк">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
    `;
    slot.classList.add('has-tank');
  }

  /* Дублирующийся метод удален - используется основной generateComparisonTable выше */

  /**
   * Создать ID танка
   */
  createTankId(tankName) {
    return tankName
      .toLowerCase()
      .replace(/[^a-z0-9а-я]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }

  /**
   * Получить путь к иконке танка
   */
  getTankIconPath(tankName) {
    return getTankIconPath(tankName);
  }

  /**
   * СИНХРОНИЗАЦИЯ ВСЕХ БЕЙДЖИКОВ С ТЕКУЩИМ СОСТОЯНИЕМ
   */
  syncAllBadges() {
    // Убеждаемся, что состояние восстановлено
    if (this.tanks.every(t => t === null)) {
      this.restoreState();
    }

    const tankCards = document.querySelectorAll('.tank-item');
    tankCards.forEach(card => {
      const tankName = card.dataset.tankName;
      if (tankName) {
        const isInComparison = this.tanks.some(tank => tank && tank.name === tankName);
        this.updateTankCardBadge(tankName, isInComparison);
      }
    });

    // Обновляем UI элементы
    this.updateFloatingWindow();
    this.updateCompareButton();

    if (import.meta.env?.MODE === 'development') {
      const activeTanks = this.tanks.filter(tank => tank !== null);
      console.log(`🔄 Synced ${activeTanks.length} tanks in comparison`);
    }
  }
}

// Создаем алиас для обратной совместимости
export const OptimizedCompareSystem = UnifiedCompareSystem;
export const CompareSystem = UnifiedCompareSystem;
export const AdvancedCompareSystem = UnifiedCompareSystem;

// Создаем единственный экземпляр системы сравнения
export const unifiedCompareSystem = new UnifiedCompareSystem();

// Для обратной совместимости
export const compareSystem = unifiedCompareSystem;
export const advancedCompareSystem = unifiedCompareSystem;

/**
 * Инициализация системы сравнения
 */
export function initializeCompareSystem() {
  const compareBtn = document.getElementById('compare-btn');
  const clearBtn = document.getElementById('clear-all-compare');

  // Обработчики для основных кнопок
  if (compareBtn) {
    compareBtn.addEventListener('click', () => {
      if (!compareBtn.disabled) {
        // Показываем расширенное сравнение
        if (window.newCompareSystem && window.newCompareSystem.generateCompactTable) {
          window.newCompareSystem.generateCompactTable();
        }
      }
    });
  }

  if (clearBtn) {
    clearBtn.addEventListener('click', () => {
      // Очищаем все системы сравнения
      if (window.newCompareSystem) {
        window.newCompareSystem.clearAll();
      }
      if (window.advancedCompareSystem) {
        window.advancedCompareSystem.clearAll();
      }
    });
  }

  // Обработчики для старых слотов (обратная совместимость)
  const tankSlots = document.querySelectorAll('.compare-tank-slot');
  tankSlots.forEach((slot, index) => {
    slot.addEventListener('click', e => {
      if (e.target.closest('.tank-card-remove')) return;
      openTankSelector(index);
    });
  });

  // Инициализируем обе системы
  compareSystem.updateCompareButton();
  if (window.advancedCompareSystem) {
    advancedCompareSystem.updateCompareButton();
  }

  console.log('✅ Compare system initialized');
}

/**
 * Открыть селектор танков
 */
function openTankSelector(slotIndex) {
  // Реализация селектора танков будет добавлена позже
  console.log(`Opening tank selector for slot ${slotIndex + 1}`);
}

// Делаем системы доступными глобально
window.compareSystem = compareSystem; // Старая система для совместимости
window.advancedCompareSystem = advancedCompareSystem; // Новая система

