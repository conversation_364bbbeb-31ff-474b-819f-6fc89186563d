/**
 * ОПТИМИЗИРОВАННЫЙ РЕНДЕРЕР
 * Эффективный батчинг DOM операций с минимальными reflow/repaint
 */

import { PERFORMANCE_THRESHOLDS } from './performance-constants.js';

class OptimizedRenderer {
  constructor() {
    this.renderQueue = [];
    this.isRendering = false;
    this.frameId = null;
    this.batchedUpdates = new Map();
    this.intersectionObserver = null;

    // Упрощенные метрики
    this.metrics = {
      renderCount: 0,
      averageRenderTime: 0,
      batchedOperations: 0,
      lastRenderTime: 0
    };

    this.setupIntersectionObserver();
  }

  /**
   * НАСТРОЙКА INTERSECTION OBSERVER ДЛЯ ВИРТУАЛИЗАЦИИ
   */
  setupIntersectionObserver() {
    if (!window.IntersectionObserver) return;

    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const element = entry.target;
        if (entry.isIntersecting) {
          element.classList.add('visible');
          element.classList.remove('hidden');
        } else {
          element.classList.add('hidden');
          element.classList.remove('visible');
        }
      });
    }, {
      rootMargin: '50px',
      threshold: 0.1
    });
  }

  /**
   * ОПТИМИЗИРОВАННЫЙ БАТЧИНГ DOM ОПЕРАЦИЙ
   */
  batchDOMUpdate(element, updates) {
    if (!element || !updates) return;

    const elementKey = element.id || element.dataset?.key || 'anonymous';

    if (!this.batchedUpdates.has(elementKey)) {
      this.batchedUpdates.set(elementKey, {
        element,
        updates: []
      });
    }

    this.batchedUpdates.get(elementKey).updates.push(updates);
    this.scheduleRender();
  }

  /**
   * ПЛАНИРОВАНИЕ РЕНДЕРА С THROTTLING
   */
  scheduleRender() {
    if (this.isRendering || this.frameId) return;

    this.frameId = requestAnimationFrame(() => {
      this.frameId = null;
      this.executeBatchedUpdates();
    });
  }

  /**
   * ОПТИМИЗИРОВАННОЕ ВЫПОЛНЕНИЕ БАТЧИРОВАННЫХ ОБНОВЛЕНИЙ
   */
  executeBatchedUpdates() {
    if (this.batchedUpdates.size === 0) {
      this.isRendering = false;
      return;
    }

    const startTime = performance.now();
    this.isRendering = true;

    // Группируем операции по типу для минимизации reflow
    const operations = {
      styles: [],
      content: [],
      attributes: [],
      classes: []
    };

    // Собираем все операции
    for (const [key, batch] of this.batchedUpdates) {
      for (const update of batch.updates) {
        const baseUpdate = { element: batch.element, ...update };

        if (update.styles) operations.styles.push(baseUpdate);
        if (update.content) operations.content.push(baseUpdate);
        if (update.attributes) operations.attributes.push(baseUpdate);
        if (update.classes) operations.classes.push(baseUpdate);
      }
    }

    // Выполняем операции в оптимальном порядке (стили -> атрибуты -> классы -> контент)
    this.applyStyleUpdates(operations.styles);
    this.applyAttributeUpdates(operations.attributes);
    this.applyClassUpdates(operations.classes);
    this.applyContentUpdates(operations.content);

    // Очищаем и завершаем
    this.batchedUpdates.clear();
    this.isRendering = false;

    const renderTime = performance.now() - startTime;
    this.updateMetrics(renderTime);
  }

  /**
   * ОПТИМИЗИРОВАННОЕ ПРИМЕНЕНИЕ СТИЛЕВЫХ ОБНОВЛЕНИЙ
   */
  applyStyleUpdates(updates) {
    if (updates.length === 0) return;

    // Группируем по элементам для минимизации layout thrashing
    const elementGroups = new Map();

    updates.forEach(update => {
      if (!update.element || !update.styles) return;

      if (!elementGroups.has(update.element)) {
        elementGroups.set(update.element, {});
      }
      Object.assign(elementGroups.get(update.element), update.styles);
    });

    // Применяем все стили для каждого элемента за один раз
    elementGroups.forEach((styles, element) => {
      try {
        Object.assign(element.style, styles);
      } catch (error) {
        console.warn('Failed to apply styles:', error);
      }
    });
  }

  /**
   * ПРИМЕНЕНИЕ ОБНОВЛЕНИЙ КЛАССОВ
   */
  applyClassUpdates(updates) {
    if (updates.length === 0) return;

    updates.forEach(update => {
      if (!update.element || !update.classes) return;

      try {
        const { add = [], remove = [], toggle = [] } = update.classes;

        if (remove.length > 0) update.element.classList.remove(...remove);
        if (add.length > 0) update.element.classList.add(...add);
        toggle.forEach(className => update.element.classList.toggle(className));
      } catch (error) {
        console.warn('Failed to apply class updates:', error);
      }
    });
  }

  /**
   * ОПТИМИЗИРОВАННОЕ ПРИМЕНЕНИЕ ОБНОВЛЕНИЙ АТРИБУТОВ
   */
  applyAttributeUpdates(updates) {
    if (updates.length === 0) return;

    updates.forEach(update => {
      if (!update.element || !update.attributes) return;

      try {
        Object.entries(update.attributes).forEach(([attr, value]) => {
          if (value === null || value === undefined) {
            update.element.removeAttribute(attr);
          } else {
            update.element.setAttribute(attr, String(value));
          }
        });
      } catch (error) {
        console.warn('Failed to apply attribute updates:', error);
      }
    });
  }

  /**
   * ОПТИМИЗИРОВАННОЕ ПРИМЕНЕНИЕ ОБНОВЛЕНИЙ КОНТЕНТА
   */
  applyContentUpdates(updates) {
    if (updates.length === 0) return;

    updates.forEach(update => {
      if (!update.element || !update.content) return;

      try {
        if (update.content.html !== undefined) {
          update.element.innerHTML = update.content.html;
        } else if (update.content.text !== undefined) {
          update.element.textContent = update.content.text;
        }
      } catch (error) {
        console.warn('Failed to apply content updates:', error);
      }
    });
  }

  /**
   * УПРОЩЕННАЯ ВИРТУАЛИЗАЦИЯ СПИСКОВ
   */
  virtualizeList(container, items, renderItem, itemHeight = 120) {
    if (!container || !items || items.length === 0) return;

    const containerHeight = container.clientHeight;
    const visibleCount = Math.ceil(containerHeight / itemHeight) + 4; // +4 для буфера
    const scrollTop = container.scrollTop;
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - 2);
    const endIndex = Math.min(startIndex + visibleCount, items.length);

    // Используем Intersection Observer для более эффективной виртуализации
    const visibleItems = items.slice(startIndex, endIndex);

    // Рендерим только видимые элементы
    const fragment = document.createDocumentFragment();
    visibleItems.forEach((item, index) => {
      const element = renderItem(item, startIndex + index);

      // Добавляем в Intersection Observer для отслеживания видимости
      if (this.intersectionObserver) {
        this.intersectionObserver.observe(element);
      }

      fragment.appendChild(element);
    });

    // Обновляем контейнер
    const viewport = container.querySelector('.virtual-viewport') || container;
    viewport.innerHTML = '';
    viewport.appendChild(fragment);
  }

  /**
   * ОБНОВЛЕНИЕ МЕТРИК ПРОИЗВОДИТЕЛЬНОСТИ
   */
  updateMetrics(renderTime) {
    this.metrics.renderCount++;
    this.metrics.lastRenderTime = renderTime;
    this.metrics.averageRenderTime =
      (this.metrics.averageRenderTime * 0.9) + (renderTime * 0.1); // Экспоненциальное сглаживание
  }

  /**
   * ПОЛУЧЕНИЕ МЕТРИК ПРОИЗВОДИТЕЛЬНОСТИ
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * ОЧИСТКА РЕСУРСОВ
   */
  cleanup() {
    if (this.frameId) {
      cancelAnimationFrame(this.frameId);
      this.frameId = null;
    }

    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }

    this.batchedUpdates.clear();
    this.renderQueue.length = 0;
  }
}

// Создаем глобальный экземпляр оптимизированного рендерера
export const optimizedRenderer = new OptimizedRenderer();

// Экспортируем для обратной совместимости
export const ultraRenderer = optimizedRenderer;

export default optimizedRenderer;
