{"name": "wot-tank-comparator", "version": "2.1.0", "description": "Advanced World of Tanks tank comparison tool with AI-powered analysis and modern UI", "type": "module", "homepage": "https://github.com/username/wot-tank-comparator", "repository": {"type": "git", "url": "https://github.com/username/wot-tank-comparator.git"}, "bugs": {"url": "https://github.com/username/wot-tank-comparator/issues"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "vite --host --port 3000", "dev:turbo": "vite --host --port 3000 --force", "build": "vite build", "build:analyze": "vite build --mode analyze", "build:optimized": "set NODE_ENV=production && vite build --mode production", "build:ultra": "npm run clean:cache && npm run build:optimized", "preview": "vite preview --host --port 3000", "lint": "eslint . --ext .js --fix --cache", "lint:check": "eslint . --ext .js --cache", "format": "prettier --write . --cache", "test:performance": "node -e \"import('./src/tests/TestRunner.js').then(m => new m.TestRunner().runAllTests())\"", "test:system": "node -e \"import('./src/tests/UltraPerformanceTests.js').then(m => m.ultraPerformanceTests.runAllTests())\"", "analyze:bundle": "npx vite-bundle-analyzer dist", "analyze:deps": "npx depcheck --ignore-bin-package=false", "clean": "rimraf dist node_modules/.vite .eslintcache", "clean:full": "npm run clean && rimraf node_modules package-lock.json && npm install", "clean:cache": "rimraf node_modules/.vite node_modules/.cache .eslintcache", "clean:all": "npm run clean && npm run clean:cache", "format:check": "prettier --check . --cache", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "analyze": "npm run build:analyze", "optimize": "npm run clean:all && npm run build:ultra", "validate": "npm run lint:check && npm run format:check && npm run test", "performance": "npm run build:ultra && npm run analyze", "start": "npm run dev", "start:fast": "npm run dev:turbo"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@vitest/ui": "^3.1.4", "autoprefixer": "^10.4.21", "cssnano": "^7.0.7", "daisyui": "^5.0.43", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "jsdom": "^26.1.0", "postcss": "^8.5.3", "prettier": "^3.2.5", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^6.0.1", "tailwindcss": "^4.1.10", "vite": "^5.1.0", "vitest": "^3.1.4"}, "main": "src/main.js", "keywords": ["world-of-tanks", "tank-comparison", "gaming", "wot", "tank-analyzer", "javascript", "vite"], "author": {"name": "Your Name", "email": "<EMAIL>"}, "license": "MIT", "browserslist": ["> 1%", "last 2 versions", "not dead"]}