/**
 * PREMIUM UI CONTROLLER
 * Управление интерактивностью премиального интерфейса
 */

export class PremiumUI {
  constructor() {
    this.isInitialized = false;
    this.dropdowns = new Map();
    this.settings = this.loadSettings();
  }

  /**
   * Инициализация премиального UI
   */
  initialize() {
    if (this.isInitialized) return;

    this.setupOverviewInteractions();
    this.setupSettingsInteractions();
    // ПОЛНОСТЬЮ ОТКЛЮЧЕНО - SettingsManager управляет dropdown
    // this.setupDropdowns();
    this.setupToggles();
    this.setupButtons();

    this.isInitialized = true;
    console.log('✅ Premium UI initialized');
  }

  /**
   * Настройка взаимодействий Overview
   */
  setupOverviewInteractions() {
    // Quick Actions
    const actionCards = document.querySelectorAll('.action-card:not(.coming-soon)');
    actionCards.forEach(card => {
      card.addEventListener('click', (e) => {
        const action = card.dataset.action;
        this.handleQuickAction(action);
      });

      // Hover effects
      card.addEventListener('mouseenter', () => {
        this.animateCard(card, 'enter');
      });

      card.addEventListener('mouseleave', () => {
        this.animateCard(card, 'leave');
      });
    });

    // Coming Soon cards
    const comingSoonCards = document.querySelectorAll('.action-card.coming-soon, .feature-card.coming-soon');
    comingSoonCards.forEach(card => {
      card.addEventListener('click', (e) => {
        e.preventDefault();
        this.showComingSoonMessage();
      });
    });
  }

  /**
   * Настройка взаимодействий Settings
   * ПРИМЕЧАНИЕ: Основные обработчики настроек теперь в SettingsManager.js
   */
  setupSettingsInteractions() {
    // Этот метод оставлен для совместимости, но основная логика в SettingsManager
    console.log('🔧 PremiumUI: Settings interactions setup (delegated to SettingsManager)');
  }

  /**
   * Настройка выпадающих списков
   */
  setupDropdowns() {
    const dropdowns = document.querySelectorAll('.custom-dropdown');
    
    dropdowns.forEach(dropdown => {
      const selected = dropdown.querySelector('.dropdown-selected');
      const options = dropdown.querySelector('.dropdown-options');
      const icon = dropdown.querySelector('.dropdown-icon');
      
      if (!selected || !options) return;

      // Клик по выбранному элементу
      selected.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleDropdown(dropdown);
      });

      // Клик по опции
      const optionElements = options.querySelectorAll('.dropdown-option');
      optionElements.forEach(option => {
        option.addEventListener('click', (e) => {
          e.stopPropagation();
          this.selectDropdownOption(dropdown, option);
        });
      });

      this.dropdowns.set(dropdown, { selected, options, icon });

      // Выделяем текущий активный элемент галочкой
      this.highlightActiveOption(dropdown);
    });

    // Закрытие при клике вне
    document.addEventListener('click', (e) => {
      // Проверяем, что клик не по dropdown элементам
      if (!e.target.closest('.custom-dropdown')) {
        this.closeAllDropdowns();
      }
    });

    // Закрытие при изменении размера окна
    window.addEventListener('resize', () => {
      this.closeAllDropdowns();
    });


  }

  /**
   * Настройка переключателей
   */
  setupToggles() {
    const toggles = document.querySelectorAll('.modern-toggle input');
    const toggleContainers = document.querySelectorAll('.modern-toggle');

    toggles.forEach(toggle => {
      toggle.addEventListener('change', (e) => {
        e.stopPropagation();
        this.handleToggleChange(toggle.id, e.target.checked);
      });
    });

    // Предотвращаем всплытие для контейнера toggle
    toggleContainers.forEach(container => {
      container.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    });
  }

  /**
   * Настройка кнопок
   */
  setupButtons() {
    const buttons = document.querySelectorAll('.modern-button, .settings-button');

    buttons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        this.animateButton(button);
      });
    });
  }

  /**
   * Обработка быстрых действий
   */
  handleQuickAction(action) {
    switch (action) {
      case 'vehicles':
        this.navigateToSection('vehicles');
        break;
      case 'compare':
        this.navigateToSection('compare');
        break;
      case 'tier-list':
        this.showComingSoonMessage();
        break;
      default:
        console.warn('Unknown action:', action);
    }
  }

  /**
   * Переключение выпадающего списка
   */
  toggleDropdown(dropdown) {
    const data = this.dropdowns.get(dropdown);
    if (!data) return;

    const isOpen = data.options.classList.contains('open');

    if (isOpen) {
      // Если уже открыт, закрываем
      this.closeAllDropdowns();
    } else {
      // Закрываем все остальные
      this.closeAllDropdowns();

      // Открываем текущий
      data.options.classList.add('open');
      data.icon.style.transform = 'rotate(180deg)';
      dropdown.classList.add('dropdown-active');

      // ОТКЛЮЧАЕМ анимацию для мгновенного появления
      data.options.style.animation = 'none';
      data.options.style.opacity = '1';
      data.options.style.transform = 'translateY(0)';
    }
  }



  /**
   * Выбор опции в выпадающем списке
   */
  selectDropdownOption(dropdown, option) {
    const data = this.dropdowns.get(dropdown);
    if (!data) return;

    const value = option.dataset.value;
    const text = option.textContent;

    // Убираем выделение с других опций
    const allOptions = data.options.querySelectorAll('.dropdown-option');
    allOptions.forEach(opt => opt.classList.remove('selected'));

    // Выделяем выбранную опцию галочкой
    option.classList.add('selected');

    // Обновляем отображение
    data.selected.dataset.value = value;
    data.selected.querySelector('.dropdown-text').textContent = text;

    // Закрываем список
    data.options.classList.remove('open');
    data.icon.style.transform = 'rotate(0deg)';
    dropdown.classList.remove('dropdown-active');

    // Сохраняем настройку
    const dropdownType = dropdown.dataset.dropdown;
    this.updateSetting(dropdownType, value);
  }

  /**
   * Закрытие всех выпадающих списков
   */
  closeAllDropdowns() {
    this.dropdowns.forEach((data, dropdown) => {
      data.options.classList.remove('open');
      data.icon.style.transform = 'rotate(0deg)';
      dropdown.classList.remove('dropdown-active');
      data.options.style.animation = '';
    });
  }

  /**
   * Выделение активной опции галочкой
   */
  highlightActiveOption(dropdown) {
    const data = this.dropdowns.get(dropdown);
    if (!data) return;

    const currentValue = data.selected.dataset.value;
    const options = data.options.querySelectorAll('.dropdown-option');

    options.forEach(option => {
      if (option.dataset.value === currentValue) {
        option.classList.add('selected');
      } else {
        option.classList.remove('selected');
      }
    });
  }

  /**
   * Обработка изменения переключателя
   */
  handleToggleChange(toggleId, checked) {
    switch (toggleId) {
      case 'cache-toggle':
        this.updateSetting('cache', checked);
        break;
      case 'animations-toggle':
        this.updateSetting('animations', checked);
        this.toggleAnimations(checked);
        break;
      case 'auto-optimize-toggle':
        this.updateSetting('autoOptimize', checked);
        break;
      case 'debug-toggle':
        this.updateSetting('debug', checked);
        window.DEBUG_MODE = checked;
        break;
      case 'experimental-toggle':
        this.updateSetting('experimental', checked);
        break;
    }
  }

  /**
   * Анимация карточки
   */
  animateCard(card, type) {
    if (type === 'enter') {
      card.style.transform = 'translateY(-4px) scale(1.02)';
      card.style.boxShadow = '0 20px 40px rgba(139, 92, 246, 0.15)';
    } else {
      card.style.transform = 'translateY(0) scale(1)';
      card.style.boxShadow = '';
    }
  }

  /**
   * Добавление эффекта пульсации
   */
  addPulseEffect(element) {
    element.style.animation = 'pulse 0.6s ease-in-out';
    setTimeout(() => {
      element.style.animation = '';
    }, 600);
  }

  /**
   * Добавление эффекта встряхивания
   */
  addShakeEffect(element) {
    element.style.animation = 'shake 0.5s ease-in-out';
    setTimeout(() => {
      element.style.animation = '';
    }, 500);
  }

  /**
   * Плавное появление элементов
   */
  fadeInElements(selector, delay = 100) {
    const elements = document.querySelectorAll(selector);
    elements.forEach((element, index) => {
      element.style.opacity = '0';
      element.style.transform = 'translateY(20px)';

      setTimeout(() => {
        element.style.transition = 'all 0.6s ease-out';
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
      }, index * delay);
    });
  }

  /**
   * Анимация кнопки
   */
  animateButton(button) {
    // Добавляем эффект пульсации
    button.style.transform = 'scale(0.95)';
    button.style.boxShadow = '0 0 20px rgba(139, 92, 246, 0.4)';

    setTimeout(() => {
      button.style.transform = 'scale(1)';
      button.style.boxShadow = '';
    }, 150);

    // Добавляем ripple эффект
    this.createRippleEffect(button);
  }

  /**
   * Создание ripple эффекта
   */
  createRippleEffect(element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);

    ripple.style.cssText = `
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: scale(0);
      animation: ripple 0.6s linear;
      left: 50%;
      top: 50%;
      width: ${size}px;
      height: ${size}px;
      margin-left: ${-size / 2}px;
      margin-top: ${-size / 2}px;
      pointer-events: none;
    `;

    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);

    setTimeout(() => {
      ripple.remove();
    }, 600);
  }

  /**
   * Показ сообщения "Coming Soon"
   */
  showComingSoonMessage() {
    import('../../utils/NotificationManager.js').then(module => {
      module.notificationManager.info('Эта функция находится в разработке', {
        title: 'Скоро будет доступно!',
        icon: '🚀',
        duration: 3000
      });
    });
  }

  /**
   * Навигация к секции
   */
  navigateToSection(section) {
    if (window.onMenuSelected) {
      window.onMenuSelected(section);
    }
  }

  /**
   * Переключение анимаций
   */
  toggleAnimations(enabled) {
    if (enabled) {
      document.body.classList.add('animations-enabled');
    } else {
      document.body.classList.remove('animations-enabled');
    }
  }

  /**
   * Загрузка настроек
   */
  loadSettings() {
    try {
      const saved = localStorage.getItem('premiumUISettings');
      return saved ? JSON.parse(saved) : this.getDefaultSettings();
    } catch (error) {
      console.warn('Failed to load settings:', error);
      return this.getDefaultSettings();
    }
  }

  /**
   * Настройки по умолчанию
   */
  getDefaultSettings() {
    return {
      theme: 'dark',
      language: 'ru',
      fontSize: 'medium',
      region: 'ru',
      cache: true,
      animations: true,
      autoOptimize: true,
      debug: false,
      experimental: false
    };
  }

  /**
   * Обновление настройки
   */
  updateSetting(key, value) {
    this.settings[key] = value;
    this.saveSettingsToStorage();
  }

  /**
   * Сохранение настроек
   */
  saveSettings() {
    this.saveSettingsToStorage();
    this.showSuccessMessage('Настройки сохранены');
  }

  /**
   * Сброс настроек
   */
  resetSettings() {
    this.settings = this.getDefaultSettings();
    this.saveSettingsToStorage();
    this.applySettings();
    this.showSuccessMessage('Настройки сброшены');
  }

  /**
   * Очистка кэша
   */
  clearCache() {
    try {
      // Очищаем localStorage (кроме настроек)
      const settingsBackup = localStorage.getItem('premiumUISettings');
      localStorage.clear();
      if (settingsBackup) {
        localStorage.setItem('premiumUISettings', settingsBackup);
      }
      
      // Очищаем кэши приложения
      if (window.quantumMemoryOptimizer) {
        window.quantumMemoryOptimizer.performCleanup();
      }
      
      this.showSuccessMessage('Кэш очищен');
    } catch (error) {
      console.error('Failed to clear cache:', error);
      this.showErrorMessage('Ошибка очистки кэша');
    }
  }

  /**
   * Сохранение в localStorage
   */
  saveSettingsToStorage() {
    try {
      localStorage.setItem('premiumUISettings', JSON.stringify(this.settings));
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }

  /**
   * Применение настроек
   */
  applySettings() {
    // Применяем настройки к UI
    Object.entries(this.settings).forEach(([key, value]) => {
      this.applySetting(key, value);
    });
  }

  /**
   * Применение отдельной настройки
   */
  applySetting(key, value) {
    switch (key) {
      case 'animations':
        this.toggleAnimations(value);
        break;
      case 'debug':
        window.DEBUG_MODE = value;
        break;
      // Добавить другие настройки по необходимости
    }
  }

  /**
   * Показ сообщения об успехе
   */
  showSuccessMessage(message) {
    this.showMessage(message, 'success');
  }

  /**
   * Показ сообщения об ошибке
   */
  showErrorMessage(message) {
    this.showMessage(message, 'error');
  }

  /**
   * Показ сообщения
   */
  showMessage(message, type = 'info') {
    import('../../utils/NotificationManager.js').then(module => {
      module.notificationManager.show(message, type, {
        duration: 2000
      });
    });
  }



  /**
   * Форматирование времени
   */
  formatTime(timestamp) {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (diff < 60000) return 'Только что';
    if (minutes < 60) return `${minutes} мин назад`;
    if (hours < 24) return `${hours} ч назад`;
    return `${days} дн назад`;
  }

  /**
   * Получение названия секции
   */
  getSectionName(section) {
    const names = {
      overview: 'Обзор',
      vehicles: 'Техника',
      compare: 'Сравнение',
      settings: 'Настройки'
    };
    return names[section] || section;
  }


}

// Экспорт синглтона
export const premiumUI = new PremiumUI();
