/**
 * МЕНЕДЖЕР ЗАВИСИМОСТЕЙ
 * Централизованное управление зависимостями для устранения циклических импортов
 */

class DependencyManager {
  constructor() {
    this.dependencies = new Map();
    this.loadingPromises = new Map();
    this.initialized = new Set();
  }

  /**
   * РЕГИСТРАЦИЯ ЗАВИСИМОСТИ
   */
  register(name, factory) {
    if (this.dependencies.has(name)) {
      console.warn(`Dependency ${name} already registered`);
      return;
    }
    
    this.dependencies.set(name, factory);
  }

  /**
   * ПОЛУЧЕНИЕ ЗАВИСИМОСТИ С ЛЕНИВОЙ ЗАГРУЗКОЙ
   */
  async get(name) {
    // Если уже загружена, возвращаем сразу
    if (this.initialized.has(name)) {
      return this.dependencies.get(name);
    }

    // Если уже загружается, ждем завершения
    if (this.loadingPromises.has(name)) {
      return this.loadingPromises.get(name);
    }

    // Начинаем загрузку
    const factory = this.dependencies.get(name);
    if (!factory) {
      throw new Error(`Dependency ${name} not found`);
    }

    const loadingPromise = this.loadDependency(name, factory);
    this.loadingPromises.set(name, loadingPromise);

    try {
      const result = await loadingPromise;
      this.initialized.add(name);
      this.loadingPromises.delete(name);
      return result;
    } catch (error) {
      this.loadingPromises.delete(name);
      throw error;
    }
  }

  /**
   * ЗАГРУЗКА ЗАВИСИМОСТИ
   */
  async loadDependency(name, factory) {
    if (typeof factory === 'function') {
      const result = await factory();
      this.dependencies.set(name, result);
      return result;
    }
    return factory;
  }

  /**
   * ПРОВЕРКА НАЛИЧИЯ ЗАВИСИМОСТИ
   */
  has(name) {
    return this.dependencies.has(name);
  }

  /**
   * ПОЛУЧЕНИЕ ВСЕХ ЗАВИСИМОСТЕЙ
   */
  getAll() {
    return Array.from(this.dependencies.keys());
  }

  /**
   * ОЧИСТКА ЗАВИСИМОСТЕЙ
   */
  clear() {
    this.dependencies.clear();
    this.loadingPromises.clear();
    this.initialized.clear();
  }
}

// Создаем глобальный экземпляр
export const dependencyManager = new DependencyManager();

/**
 * РЕГИСТРАЦИЯ ОСНОВНЫХ ЗАВИСИМОСТЕЙ
 */
export function registerCoreDependencies() {
  // Состояние приложения
  dependencyManager.register('state', async () => {
    const { unifiedStateManager } = await import('../store/UltraStateManager.js');
    return unifiedStateManager.state;
  });

  // Менеджер танков
  dependencyManager.register('tankManager', async () => {
    const { TankManager } = await import('./TankManager.js');
    return new TankManager();
  });

  // Сервис фильтрации
  dependencyManager.register('filterService', async () => {
    return import('../services/FilterService.js');
  });

  // Данные танков
  dependencyManager.register('tanksData', async () => {
    const { tanksData } = await import('../data/tanks.js');
    return tanksData;
  });

  // Система сравнения
  dependencyManager.register('compareSystem', async () => {
    const { unifiedCompareSystem } = await import('../components/CompareSystem.js');
    return unifiedCompareSystem;
  });

  // Менеджер уведомлений
  dependencyManager.register('notificationManager', async () => {
    const { notificationManager } = await import('../utils/NotificationManager.js');
    return notificationManager;
  });

  // Менеджер фильтров
  dependencyManager.register('filterManager', async () => {
    const { filterManager } = await import('./FilterManager.js');
    return filterManager;
  });

  // Менеджер событий
  dependencyManager.register('eventManager', async () => {
    const { eventManager } = await import('./EventManager.js');
    return eventManager;
  });

  console.log('✅ Core dependencies registered');
}

/**
 * ИНИЦИАЛИЗАЦИЯ ЗАВИСИМОСТЕЙ
 */
export async function initializeDependencies() {
  registerCoreDependencies();
  
  // Предварительно загружаем критические зависимости
  const criticalDeps = ['state', 'tanksData'];
  
  await Promise.all(
    criticalDeps.map(dep => dependencyManager.get(dep))
  );

  console.log('✅ Critical dependencies initialized');
}

/**
 * ХЕЛПЕРЫ ДЛЯ ПОЛУЧЕНИЯ ЗАВИСИМОСТЕЙ
 */
export const getDependency = (name) => dependencyManager.get(name);
export const hasDependency = (name) => dependencyManager.has(name);

/**
 * ТИПИЗИРОВАННЫЕ ХЕЛПЕРЫ
 */
export const getState = () => getDependency('state');
export const getTankManager = () => getDependency('tankManager');
export const getFilterService = () => getDependency('filterService');
export const getTanksData = () => getDependency('tanksData');
export const getCompareSystem = () => getDependency('compareSystem');
export const getNotificationManager = () => getDependency('notificationManager');
export const getFilterManager = () => getDependency('filterManager');
export const getEventManager = () => getDependency('eventManager');

/**
 * ИНЖЕКТОР ЗАВИСИМОСТЕЙ ДЛЯ КЛАССОВ
 */
export function withDependencies(dependencies) {
  return function(target) {
    const originalConstructor = target;
    
    function newConstructor(...args) {
      const instance = new originalConstructor(...args);
      
      // Инжектируем зависимости
      dependencies.forEach(async (depName) => {
        try {
          const dep = await getDependency(depName);
          instance[depName] = dep;
        } catch (error) {
          console.error(`Failed to inject dependency ${depName}:`, error);
        }
      });
      
      return instance;
    }
    
    newConstructor.prototype = originalConstructor.prototype;
    return newConstructor;
  };
}

/**
 * ДЕКОРАТОР ДЛЯ МЕТОДОВ С ЗАВИСИМОСТЯМИ
 */
export function requiresDependency(depName) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function(...args) {
      if (!this[depName]) {
        this[depName] = await getDependency(depName);
      }
      return originalMethod.apply(this, args);
    };
    
    return descriptor;
  };
}

/**
 * ПРОВЕРКА ЦИКЛИЧЕСКИХ ЗАВИСИМОСТЕЙ
 */
export function checkCircularDependencies() {
  const visited = new Set();
  const recursionStack = new Set();
  
  function hasCycle(dep) {
    if (recursionStack.has(dep)) {
      return true;
    }
    
    if (visited.has(dep)) {
      return false;
    }
    
    visited.add(dep);
    recursionStack.add(dep);
    
    // Здесь можно добавить логику проверки зависимостей
    // Для простоты пропускаем
    
    recursionStack.delete(dep);
    return false;
  }
  
  const allDeps = dependencyManager.getAll();
  for (const dep of allDeps) {
    if (hasCycle(dep)) {
      console.error(`Circular dependency detected: ${dep}`);
      return false;
    }
  }
  
  console.log('✅ No circular dependencies found');
  return true;
}

/**
 * ОТЧЕТ О ЗАВИСИМОСТЯХ
 */
export function getDependencyReport() {
  return {
    registered: dependencyManager.getAll(),
    initialized: Array.from(dependencyManager.initialized),
    loading: Array.from(dependencyManager.loadingPromises.keys())
  };
}
