/**
 * СИСТЕМА ОПТИМИЗАЦИИ ПРОИЗВОДИТЕЛЬНОСТИ
 * Виртуализация, ленивая загрузка, кэширование, дебаунсинг
 */

export class PerformanceOptimizer {
  constructor() {
    this.virtualizedLists = new Map();
    this.lazyLoadObserver = null;
    this.intersectionObserver = null;
    this.performanceMetrics = new Map();
    this.cacheManager = new CacheManager();
    this.debounceTimers = new Map();
    
    // Настройки оптимизации
    this.settings = {
      virtualization: {
        enabled: true,
        itemHeight: 60,
        bufferSize: 5,
        overscan: 3
      },
      lazyLoading: {
        enabled: true,
        rootMargin: '50px',
        threshold: 0.1
      },
      caching: {
        enabled: true,
        maxSize: 1000,
        ttl: 300000 // 5 минут
      },
      debouncing: {
        search: 300,
        filter: 200,
        resize: 100,
        scroll: 16 // 60fps
      }
    };
    
    this.initializeOptimizer();
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ ОПТИМИЗАТОРА
   */
  initializeOptimizer() {
    this.setupVirtualization();
    this.setupLazyLoading();
    this.setupPerformanceMonitoring();
    this.setupMemoryManagement();
    this.optimizeEventListeners();
    
    console.log('⚡ Performance Optimizer initialized');
  }

  /**
   * НАСТРОЙКА ВИРТУАЛИЗАЦИИ
   */
  setupVirtualization() {
    if (!this.settings.virtualization.enabled) return;
    
    // Создаем виртуализированный список
    this.virtualList = new VirtualList({
      itemHeight: this.settings.virtualization.itemHeight,
      bufferSize: this.settings.virtualization.bufferSize,
      overscan: this.settings.virtualization.overscan
    });
    
    console.log('📋 Virtualization enabled');
  }

  /**
   * НАСТРОЙКА ЛЕНИВОЙ ЗАГРУЗКИ
   */
  setupLazyLoading() {
    if (!this.settings.lazyLoading.enabled || !window.IntersectionObserver) return;
    
    this.lazyLoadObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadLazyContent(entry.target);
          this.lazyLoadObserver.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: this.settings.lazyLoading.rootMargin,
      threshold: this.settings.lazyLoading.threshold
    });
    
    console.log('🔄 Lazy loading enabled');
  }

  /**
   * НАСТРОЙКА МОНИТОРИНГА ПРОИЗВОДИТЕЛЬНОСТИ
   */
  setupPerformanceMonitoring() {
    // Мониторинг FPS
    this.fpsMonitor = new FPSMonitor();
    
    // Мониторинг памяти
    if (performance.memory) {
      this.memoryMonitor = new MemoryMonitor();
    }
    
    // Мониторинг времени рендеринга
    this.renderTimeMonitor = new RenderTimeMonitor();
    
    console.log('📊 Performance monitoring enabled');
  }

  /**
   * НАСТРОЙКА УПРАВЛЕНИЯ ПАМЯТЬЮ
   */
  setupMemoryManagement() {
    // Автоматическая очистка кэша
    setInterval(() => {
      this.cacheManager.cleanup();
    }, 60000); // Каждую минуту
    
    // Очистка неиспользуемых DOM элементов
    setInterval(() => {
      this.cleanupUnusedElements();
    }, 300000); // Каждые 5 минут
    
    console.log('🧹 Memory management enabled');
  }

  /**
   * ОПТИМИЗАЦИЯ ОБРАБОТЧИКОВ СОБЫТИЙ
   */
  optimizeEventListeners() {
    // Пассивные слушатели для скролла
    document.addEventListener('scroll', this.debounce((e) => {
      this.handleOptimizedScroll(e);
    }, this.settings.debouncing.scroll), { passive: true });
    
    // Дебаунсинг для изменения размера
    window.addEventListener('resize', this.debounce(() => {
      this.handleOptimizedResize();
    }, this.settings.debouncing.resize));
    
    console.log('🎯 Event listeners optimized');
  }

  /**
   * ВИРТУАЛИЗИРОВАННЫЙ СПИСОК
   */
  createVirtualizedList(container, items, renderItem) {
    if (!this.settings.virtualization.enabled) {
      return this.createRegularList(container, items, renderItem);
    }
    
    const virtualList = new VirtualizedList(container, {
      items,
      renderItem,
      itemHeight: this.settings.virtualization.itemHeight,
      bufferSize: this.settings.virtualization.bufferSize,
      overscan: this.settings.virtualization.overscan
    });
    
    this.virtualizedLists.set(container, virtualList);
    return virtualList;
  }

  /**
   * ЛЕНИВАЯ ЗАГРУЗКА ИЗОБРАЖЕНИЙ
   */
  setupLazyImages(container) {
    const images = container.querySelectorAll('img[data-src]');
    
    images.forEach(img => {
      img.classList.add('lazy-image');
      this.lazyLoadObserver.observe(img);
    });
  }

  /**
   * ЗАГРУЗКА ЛЕНИВОГО КОНТЕНТА
   */
  loadLazyContent(element) {
    const startTime = performance.now();
    
    if (element.tagName === 'IMG') {
      this.loadLazyImage(element);
    } else if (element.hasAttribute('data-lazy-component')) {
      this.loadLazyComponent(element);
    }
    
    const loadTime = performance.now() - startTime;
    this.recordMetric('lazyLoadTime', loadTime);
  }

  /**
   * ЗАГРУЗКА ЛЕНИВОГО ИЗОБРАЖЕНИЯ
   */
  loadLazyImage(img) {
    const src = img.getAttribute('data-src');
    if (!src) return;
    
    // Создаем новое изображение для предзагрузки
    const newImg = new Image();
    
    newImg.onload = () => {
      img.src = src;
      img.classList.add('loaded');
      img.removeAttribute('data-src');
    };
    
    newImg.onerror = () => {
      img.classList.add('error');
      console.warn('Failed to load image:', src);
    };
    
    newImg.src = src;
  }

  /**
   * ЗАГРУЗКА ЛЕНИВОГО КОМПОНЕНТА
   */
  async loadLazyComponent(element) {
    const componentName = element.getAttribute('data-lazy-component');
    
    try {
      const module = await import(`./lazy/${componentName}.js`);
      const Component = module.default;
      
      const component = new Component(element);
      await component.render();
      
      element.classList.add('lazy-loaded');
      element.removeAttribute('data-lazy-component');
    } catch (error) {
      console.error('Failed to load lazy component:', componentName, error);
      element.classList.add('lazy-error');
    }
  }

  /**
   * ДЕБАУНСИНГ ФУНКЦИЙ
   */
  debounce(func, delay, key = null) {
    return (...args) => {
      const timerKey = key || func.name || 'default';
      
      if (this.debounceTimers.has(timerKey)) {
        clearTimeout(this.debounceTimers.get(timerKey));
      }
      
      const timer = setTimeout(() => {
        func.apply(this, args);
        this.debounceTimers.delete(timerKey);
      }, delay);
      
      this.debounceTimers.set(timerKey, timer);
    };
  }

  /**
   * ТРОТТЛИНГ ФУНКЦИЙ
   */
  throttle(func, delay) {
    let lastCall = 0;
    
    return (...args) => {
      const now = Date.now();
      
      if (now - lastCall >= delay) {
        lastCall = now;
        return func.apply(this, args);
      }
    };
  }

  /**
   * ОПТИМИЗИРОВАННЫЙ СКРОЛЛ
   */
  handleOptimizedScroll(event) {
    const scrollTop = event.target.scrollTop || window.pageYOffset;
    
    // Обновляем виртуализированные списки
    this.virtualizedLists.forEach(list => {
      list.updateScrollPosition(scrollTop);
    });
    
    // Проверяем видимость элементов
    this.updateVisibleElements(scrollTop);
  }

  /**
   * ОПТИМИЗИРОВАННОЕ ИЗМЕНЕНИЕ РАЗМЕРА
   */
  handleOptimizedResize() {
    // Пересчитываем размеры виртуализированных списков
    this.virtualizedLists.forEach(list => {
      list.recalculateSize();
    });
    
    // Обновляем кэш размеров
    this.cacheManager.clearSizeCache();
  }

  /**
   * ОБНОВЛЕНИЕ ВИДИМЫХ ЭЛЕМЕНТОВ
   */
  updateVisibleElements(scrollTop) {
    const viewportHeight = window.innerHeight;
    const elements = document.querySelectorAll('[data-optimize-visibility]');
    
    elements.forEach(element => {
      const rect = element.getBoundingClientRect();
      const isVisible = rect.top < viewportHeight && rect.bottom > 0;
      
      if (isVisible && !element.classList.contains('visible')) {
        element.classList.add('visible');
        this.activateElement(element);
      } else if (!isVisible && element.classList.contains('visible')) {
        element.classList.remove('visible');
        this.deactivateElement(element);
      }
    });
  }

  /**
   * АКТИВАЦИЯ ЭЛЕМЕНТА
   */
  activateElement(element) {
    // Запускаем анимации
    if (element.hasAttribute('data-animate-on-visible')) {
      element.classList.add('animate');
    }
    
    // Загружаем контент
    if (element.hasAttribute('data-load-on-visible')) {
      this.loadElementContent(element);
    }
  }

  /**
   * ДЕАКТИВАЦИЯ ЭЛЕМЕНТА
   */
  deactivateElement(element) {
    // Останавливаем анимации
    if (element.hasAttribute('data-animate-on-visible')) {
      element.classList.remove('animate');
    }
    
    // Выгружаем тяжелый контент
    if (element.hasAttribute('data-unload-on-hidden')) {
      this.unloadElementContent(element);
    }
  }

  /**
   * ЗАГРУЗКА КОНТЕНТА ЭЛЕМЕНТА
   */
  async loadElementContent(element) {
    const contentType = element.getAttribute('data-load-on-visible');
    
    switch (contentType) {
      case 'charts':
        await this.loadCharts(element);
        break;
      case 'images':
        await this.loadImages(element);
        break;
      case 'data':
        await this.loadData(element);
        break;
    }
  }

  /**
   * ВЫГРУЗКА КОНТЕНТА ЭЛЕМЕНТА
   */
  unloadElementContent(element) {
    const contentType = element.getAttribute('data-unload-on-hidden');
    
    switch (contentType) {
      case 'charts':
        this.unloadCharts(element);
        break;
      case 'heavy-animations':
        this.pauseAnimations(element);
        break;
    }
  }

  /**
   * ОЧИСТКА НЕИСПОЛЬЗУЕМЫХ ЭЛЕМЕНТОВ
   */
  cleanupUnusedElements() {
    // Удаляем неиспользуемые DOM элементы
    const unusedElements = document.querySelectorAll('[data-cleanup-unused]');
    
    unusedElements.forEach(element => {
      const lastUsed = parseInt(element.getAttribute('data-last-used') || '0');
      const now = Date.now();
      
      if (now - lastUsed > 300000) { // 5 минут
        element.remove();
      }
    });
    
    // Очищаем кэш
    this.cacheManager.cleanup();
  }

  /**
   * ЗАПИСЬ МЕТРИКИ ПРОИЗВОДИТЕЛЬНОСТИ
   */
  recordMetric(name, value) {
    if (!this.performanceMetrics.has(name)) {
      this.performanceMetrics.set(name, []);
    }
    
    const metrics = this.performanceMetrics.get(name);
    metrics.push({
      value,
      timestamp: Date.now()
    });
    
    // Ограничиваем размер массива метрик
    if (metrics.length > 100) {
      metrics.shift();
    }
  }

  /**
   * ПОЛУЧЕНИЕ МЕТРИК ПРОИЗВОДИТЕЛЬНОСТИ
   */
  getPerformanceMetrics() {
    const metrics = {};
    
    this.performanceMetrics.forEach((values, name) => {
      if (values.length > 0) {
        const recent = values.slice(-10);
        const average = recent.reduce((sum, m) => sum + m.value, 0) / recent.length;
        const min = Math.min(...recent.map(m => m.value));
        const max = Math.max(...recent.map(m => m.value));
        
        metrics[name] = { average, min, max, count: values.length };
      }
    });
    
    // Добавляем системные метрики
    if (this.fpsMonitor) {
      metrics.fps = this.fpsMonitor.getCurrentFPS();
    }
    
    if (this.memoryMonitor) {
      metrics.memory = this.memoryMonitor.getCurrentUsage();
    }
    
    return metrics;
  }

  /**
   * ОПТИМИЗАЦИЯ РЕНДЕРИНГА
   */
  optimizeRender(renderFunction) {
    return (...args) => {
      const startTime = performance.now();
      
      // Используем requestAnimationFrame для плавности
      requestAnimationFrame(() => {
        const result = renderFunction.apply(this, args);
        
        const renderTime = performance.now() - startTime;
        this.recordMetric('renderTime', renderTime);
        
        return result;
      });
    };
  }

  /**
   * ПАКЕТНАЯ ОБРАБОТКА DOM ОПЕРАЦИЙ
   */
  batchDOMOperations(operations) {
    return new Promise(resolve => {
      requestAnimationFrame(() => {
        const startTime = performance.now();
        
        operations.forEach(operation => {
          operation();
        });
        
        const batchTime = performance.now() - startTime;
        this.recordMetric('batchDOMTime', batchTime);
        
        resolve();
      });
    });
  }

  /**
   * ПРЕДЗАГРУЗКА РЕСУРСОВ
   */
  preloadResources(resources) {
    resources.forEach(resource => {
      if (resource.type === 'image') {
        this.preloadImage(resource.url);
      } else if (resource.type === 'script') {
        this.preloadScript(resource.url);
      } else if (resource.type === 'style') {
        this.preloadStyle(resource.url);
      }
    });
  }

  /**
   * ПРЕДЗАГРУЗКА ИЗОБРАЖЕНИЯ
   */
  preloadImage(url) {
    const img = new Image();
    img.src = url;
  }

  /**
   * ПРЕДЗАГРУЗКА СКРИПТА
   */
  preloadScript(url) {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'script';
    link.href = url;
    document.head.appendChild(link);
  }

  /**
   * ПРЕДЗАГРУЗКА СТИЛЕЙ
   */
  preloadStyle(url) {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = url;
    document.head.appendChild(link);
  }

  /**
   * УНИЧТОЖЕНИЕ ОПТИМИЗАТОРА
   */
  destroy() {
    // Очищаем наблюдатели
    if (this.lazyLoadObserver) {
      this.lazyLoadObserver.disconnect();
    }
    
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
    
    // Очищаем таймеры
    this.debounceTimers.forEach(timer => clearTimeout(timer));
    this.debounceTimers.clear();
    
    // Очищаем виртуализированные списки
    this.virtualizedLists.forEach(list => list.destroy());
    this.virtualizedLists.clear();
    
    // Очищаем кэш
    this.cacheManager.clear();
    
    console.log('⚡ Performance Optimizer destroyed');
  }
}

/**
 * МЕНЕДЖЕР КЭША
 */
class CacheManager {
  constructor(maxSize = 1000, ttl = 300000) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  set(key, value) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  get(key) {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  has(key) {
    return this.get(key) !== null;
  }

  delete(key) {
    this.cache.delete(key);
  }

  clear() {
    this.cache.clear();
  }

  cleanup() {
    const now = Date.now();
    
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > this.ttl) {
        this.cache.delete(key);
      }
    }
  }

  clearSizeCache() {
    for (const key of this.cache.keys()) {
      if (key.includes('size') || key.includes('dimension')) {
        this.cache.delete(key);
      }
    }
  }
}

/**
 * МОНИТОР FPS
 */
class FPSMonitor {
  constructor() {
    this.fps = 0;
    this.lastTime = performance.now();
    this.frameCount = 0;
    this.startMonitoring();
  }

  startMonitoring() {
    const monitor = () => {
      this.frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - this.lastTime >= 1000) {
        this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
        this.frameCount = 0;
        this.lastTime = currentTime;
      }
      
      requestAnimationFrame(monitor);
    };
    
    requestAnimationFrame(monitor);
  }

  getCurrentFPS() {
    return this.fps;
  }
}

/**
 * МОНИТОР ПАМЯТИ
 */
class MemoryMonitor {
  getCurrentUsage() {
    if (!performance.memory) return null;
    
    return {
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
    };
  }
}

/**
 * МОНИТОР ВРЕМЕНИ РЕНДЕРИНГА
 */
class RenderTimeMonitor {
  constructor() {
    this.renderTimes = [];
  }

  recordRenderTime(time) {
    this.renderTimes.push(time);
    
    if (this.renderTimes.length > 100) {
      this.renderTimes.shift();
    }
  }

  getAverageRenderTime() {
    if (this.renderTimes.length === 0) return 0;
    
    const sum = this.renderTimes.reduce((a, b) => a + b, 0);
    return sum / this.renderTimes.length;
  }
}

/**
 * ВИРТУАЛИЗИРОВАННЫЙ СПИСОК
 */
class VirtualizedList {
  constructor(container, options) {
    this.container = container;
    this.items = options.items || [];
    this.renderItem = options.renderItem;
    this.itemHeight = options.itemHeight || 60;
    this.bufferSize = options.bufferSize || 5;
    this.overscan = options.overscan || 3;
    
    this.scrollTop = 0;
    this.containerHeight = 0;
    this.visibleStart = 0;
    this.visibleEnd = 0;
    
    this.initialize();
  }

  initialize() {
    this.container.style.position = 'relative';
    this.container.style.overflow = 'auto';
    
    this.viewport = document.createElement('div');
    this.viewport.style.position = 'relative';
    this.viewport.style.height = `${this.items.length * this.itemHeight}px`;
    
    this.container.appendChild(this.viewport);
    
    this.container.addEventListener('scroll', () => {
      this.updateScrollPosition(this.container.scrollTop);
    });
    
    this.updateVisibleRange();
    this.render();
  }

  updateScrollPosition(scrollTop) {
    this.scrollTop = scrollTop;
    this.updateVisibleRange();
    this.render();
  }

  updateVisibleRange() {
    this.containerHeight = this.container.clientHeight;
    
    this.visibleStart = Math.floor(this.scrollTop / this.itemHeight);
    this.visibleEnd = Math.min(
      this.items.length - 1,
      Math.ceil((this.scrollTop + this.containerHeight) / this.itemHeight)
    );
    
    // Добавляем буфер
    this.visibleStart = Math.max(0, this.visibleStart - this.bufferSize);
    this.visibleEnd = Math.min(this.items.length - 1, this.visibleEnd + this.bufferSize);
  }

  render() {
    // Очищаем viewport
    this.viewport.innerHTML = '';
    
    // Рендерим только видимые элементы
    for (let i = this.visibleStart; i <= this.visibleEnd; i++) {
      const item = this.items[i];
      const element = this.renderItem(item, i);
      
      element.style.position = 'absolute';
      element.style.top = `${i * this.itemHeight}px`;
      element.style.height = `${this.itemHeight}px`;
      element.style.width = '100%';
      
      this.viewport.appendChild(element);
    }
  }

  recalculateSize() {
    this.containerHeight = this.container.clientHeight;
    this.viewport.style.height = `${this.items.length * this.itemHeight}px`;
    this.updateVisibleRange();
    this.render();
  }

  updateItems(newItems) {
    this.items = newItems;
    this.viewport.style.height = `${this.items.length * this.itemHeight}px`;
    this.updateVisibleRange();
    this.render();
  }

  destroy() {
    this.container.innerHTML = '';
  }
}

// Создаем глобальный экземпляр
export const performanceOptimizer = new PerformanceOptimizer();
