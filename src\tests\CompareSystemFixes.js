/**
 * ТЕСТЫ ИСПРАВЛЕНИЙ СИСТЕМЫ СРАВНЕНИЯ
 */

export class CompareSystemFixes {
  constructor() {
    this.testResults = [];
  }

  /**
   * ЗАПУСК ВСЕХ ТЕСТОВ ИСПРАВЛЕНИЙ
   */
  async runFixTests() {
    console.log('🔧 Запуск тестов исправлений системы сравнения...');

    await this.testColorScheme();
    await this.testCompareButtonActivation();
    await this.testResultsNavigation();
    await this.testMinTanksForComparison();

    this.generateFixReport();
  }

  /**
   * ТЕСТ ЦВЕТОВОЙ СХЕМЫ
   */
  async testColorScheme() {
    const testName = 'Color Scheme Test';
    
    try {
      const container = document.querySelector('.advanced-compare-container');
      const header = document.querySelector('.comparison-table-header');
      const compareBtn = document.querySelector('.advanced-compare-button');
      
      let colorScore = 0;
      let maxScore = 3;

      // Проверка 1: Основной контейнер использует правильные цвета
      if (container) {
        const computedStyle = window.getComputedStyle(container);
        const background = computedStyle.background;
        if (background.includes('114, 84, 228') || background.includes('161, 123, 248')) {
          colorScore++;
        }
      }

      // Проверка 2: Заголовок таблицы использует правильные цвета
      if (header) {
        const computedStyle = window.getComputedStyle(header);
        const background = computedStyle.background;
        if (background.includes('114, 84, 228') || background.includes('161, 123, 248')) {
          colorScore++;
        }
      }

      // Проверка 3: Кнопка сравнения использует правильные цвета
      if (compareBtn) {
        const computedStyle = window.getComputedStyle(compareBtn);
        const background = computedStyle.background;
        if (background.includes('114, 84, 228') || background.includes('90, 63, 184')) {
          colorScore++;
        }
      }

      const percentage = (colorScore / maxScore) * 100;

      this.testResults.push({
        name: testName,
        status: percentage >= 66 ? 'PASS' : 'PARTIAL',
        details: `Цветовая схема: ${colorScore}/${maxScore} элементов (${percentage.toFixed(1)}%)`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ АКТИВАЦИИ КНОПКИ СРАВНЕНИЯ
   */
  async testCompareButtonActivation() {
    const testName = 'Compare Button Activation Test';
    
    try {
      if (!window.advancedCompareSystem) {
        this.testResults.push({
          name: testName,
          status: 'FAIL',
          details: 'Система сравнения не найдена'
        });
        return;
      }

      // Очищаем систему
      window.advancedCompareSystem.clearAll();

      // Проверяем, что кнопка disabled при 0 танков
      const compareBtn = document.getElementById('compare-btn');
      const floatingCompareBtn = document.querySelector('.floating-action-btn.compare-btn');

      let initiallyDisabled = true;
      if (compareBtn && !compareBtn.disabled) initiallyDisabled = false;
      if (floatingCompareBtn && !floatingCompareBtn.disabled) initiallyDisabled = false;

      // Добавляем 2 тестовых танка
      const testTank1 = {
        name: 'Test Tank 1',
        level: 10,
        type: 'heavyTank',
        nation: 'ussr'
      };
      
      const testTank2 = {
        name: 'Test Tank 2',
        level: 10,
        type: 'mediumTank',
        nation: 'germany'
      };

      window.advancedCompareSystem.addTank(testTank1);
      window.advancedCompareSystem.addTank(testTank2);

      // Ждем обновления UI
      await new Promise(resolve => setTimeout(resolve, 100));

      // Проверяем, что кнопка теперь активна
      let finallyEnabled = false;
      if (compareBtn && !compareBtn.disabled) finallyEnabled = true;
      if (floatingCompareBtn && !floatingCompareBtn.disabled) finallyEnabled = true;

      // Очищаем после теста
      window.advancedCompareSystem.clearAll();

      this.testResults.push({
        name: testName,
        status: (initiallyDisabled && finallyEnabled) ? 'PASS' : 'FAIL',
        details: `Начально disabled: ${initiallyDisabled}, После добавления enabled: ${finallyEnabled}`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ НАВИГАЦИИ К РЕЗУЛЬТАТАМ
   */
  async testResultsNavigation() {
    const testName = 'Results Navigation Test';
    
    try {
      if (!window.advancedCompareSystem) {
        this.testResults.push({
          name: testName,
          status: 'FAIL',
          details: 'Система сравнения не найдена'
        });
        return;
      }

      // Добавляем тестовые танки
      const testTank1 = {
        name: 'Nav Test Tank 1',
        level: 10,
        type: 'heavyTank',
        nation: 'ussr'
      };
      
      const testTank2 = {
        name: 'Nav Test Tank 2',
        level: 10,
        type: 'mediumTank',
        nation: 'germany'
      };

      window.advancedCompareSystem.addTank(testTank1);
      window.advancedCompareSystem.addTank(testTank2);

      // Пытаемся показать результаты
      window.advancedCompareSystem.showResults();

      // Ждем генерации результатов
      await new Promise(resolve => setTimeout(resolve, 500));

      // Проверяем, что результаты отображаются
      const resultsArea = document.getElementById('compare-results');
      const advancedResultsContent = document.getElementById('advanced-results-content');

      let resultsVisible = false;
      let contentGenerated = false;

      if (resultsArea && !resultsArea.classList.contains('hidden')) {
        resultsVisible = true;
      }

      if (advancedResultsContent && advancedResultsContent.innerHTML.trim() !== '') {
        contentGenerated = true;
      }

      // Очищаем после теста
      window.advancedCompareSystem.clearAll();

      this.testResults.push({
        name: testName,
        status: (resultsVisible && contentGenerated) ? 'PASS' : 'PARTIAL',
        details: `Результаты видимы: ${resultsVisible}, Контент сгенерирован: ${contentGenerated}`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ МИНИМАЛЬНОГО КОЛИЧЕСТВА ТАНКОВ
   */
  async testMinTanksForComparison() {
    const testName = 'Min Tanks For Comparison Test';
    
    try {
      if (!window.advancedCompareSystem) {
        this.testResults.push({
          name: testName,
          status: 'FAIL',
          details: 'Система сравнения не найдена'
        });
        return;
      }

      // Проверяем, что minTanksForComparison определено
      const minTanks = window.advancedCompareSystem.minTanksForComparison;
      const hasMinTanks = typeof minTanks === 'number' && minTanks > 0;

      // Проверяем логику с одним танком
      window.advancedCompareSystem.clearAll();
      const testTank = {
        name: 'Single Test Tank',
        level: 10,
        type: 'heavyTank',
        nation: 'ussr'
      };

      window.advancedCompareSystem.addTank(testTank);
      
      // Ждем обновления
      await new Promise(resolve => setTimeout(resolve, 100));

      const compareBtn = document.getElementById('compare-btn');
      const shouldBeDisabled = compareBtn ? compareBtn.disabled : true;

      // Очищаем
      window.advancedCompareSystem.clearAll();

      this.testResults.push({
        name: testName,
        status: (hasMinTanks && shouldBeDisabled) ? 'PASS' : 'FAIL',
        details: `minTanksForComparison: ${minTanks}, Кнопка disabled с 1 танком: ${shouldBeDisabled}`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        details: error.message
      });
    }
  }

  /**
   * ГЕНЕРАЦИЯ ОТЧЕТА ОБ ИСПРАВЛЕНИЯХ
   */
  generateFixReport() {
    const passedTests = this.testResults.filter(test => test.status === 'PASS').length;
    const totalTests = this.testResults.length;

    console.log('\n🔧 ОТЧЕТ ОБ ИСПРАВЛЕНИЯХ СИСТЕМЫ СРАВНЕНИЯ');
    console.log('='.repeat(50));
    console.log(`✅ Исправлено: ${passedTests}/${totalTests} проблем`);
    console.log(`📊 Успешность исправлений: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log('\n📋 ДЕТАЛИ ИСПРАВЛЕНИЙ:');
    
    this.testResults.forEach((test, index) => {
      const statusIcon = {
        'PASS': '✅',
        'PARTIAL': '⚠️',
        'FAIL': '❌',
        'ERROR': '💥'
      }[test.status] || '❓';

      console.log(`${index + 1}. ${statusIcon} ${test.name}`);
      console.log(`   Статус: ${test.status}`);
      console.log(`   Детали: ${test.details}`);
      console.log('');
    });

    console.log('='.repeat(50));
    
    if (passedTests === totalTests) {
      console.log('🎉 ВСЕ ИСПРАВЛЕНИЯ УСПЕШНО ПРИМЕНЕНЫ!');
    } else {
      console.log('⚠️  Некоторые проблемы требуют дополнительного внимания.');
    }
  }
}

// Экспорт для использования в консоли
window.CompareSystemFixes = CompareSystemFixes;

// Автоматический запуск тестов исправлений в dev режиме
if (import.meta.env?.MODE === 'development') {
  window.runFixTests = () => {
    const fixes = new CompareSystemFixes();
    fixes.runFixTests();
  };
  
  console.log('🔧 Тесты исправлений загружены! Запустите: runFixTests()');
}
