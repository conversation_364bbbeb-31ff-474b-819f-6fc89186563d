/**
 * ОПТИМИЗИРОВАННЫЙ TANK MANAGER
 * Эффективное управление танками с улучшенной производительностью
 */

import { getCachedElement } from '../utils/performance.js';
import { unifiedStateManager } from '../store/UltraStateManager.js';
import { updateFilterSelection } from '../components/tank-list/index.js';
import { RUSSIAN_TO_INTERNAL_TYPE_MAP, ENGLISH_TO_INTERNAL_TYPE_MAP, getTankIconPath } from '../utils/constants.js';
import { updateTankDetailsUI } from '../components/tank-details/index.js';
import { PERFORMANCE_THRESHOLDS } from '../utils/performance-constants.js';
import { domCache } from '../utils/CacheMigrationAdapter.js';

// Используем унифицированное состояние
const state = unifiedStateManager.state;
// Ultra-системы будут загружаться по требованию
let ultraSearchEngine = null;
let ultraRenderer = null;

// Ленивая загрузка Ultra-систем
async function initUltraSystems() {
  if (!ultraSearchEngine) {
    try {
      const { ultraSearchEngine: engine } = await import('../utils/UltraSearchEngine.js');
      ultraSearchEngine = engine;
    } catch (error) {
      console.warn('UltraSearchEngine not available, using fallback');
    }
  }

  if (!ultraRenderer) {
    try {
      const { ultraRenderer: renderer } = await import('../utils/UltraRenderer.js');
      ultraRenderer = renderer;
    } catch (error) {
      console.warn('UltraRenderer not available, using fallback');
    }
  }
}

/**
 * ОПТИМИЗИРОВАННЫЙ TANK MANAGER
 * Упрощенная архитектура для максимальной производительности
 */
export class TankManager {
  constructor() {
    // Основные флаги состояния
    this.isRendering = false;
    this.lastRenderTime = 0;
    this.currentRenderKey = null;
    this.renderQueue = [];

    // Оптимизированное кэширование
    this.filterCache = new Map();
    this.tankNameCache = new Map(); // Кэш для быстрого поиска танков по имени
    this.tankIdCache = new Map(); // Кэш для быстрого поиска танков по ID
    this.maxCacheSize = 20; // Уменьшено для экономии памяти
    // DOM кэш теперь использует унифицированную систему

    // Упрощенные метрики
    this.performanceMetrics = {
      filterTime: 0,
      renderTime: 0,
      cacheHits: 0,
      cacheMisses: 0,
      totalRenders: 0
    };

    // Система очистки памяти
    this.cleanupTasks = [];
    this.setupMemoryManagement();

    // Настройка виртуализации
    this.virtualScrolling = {
      enabled: false,
      visibleStart: 0,
      visibleEnd: PERFORMANCE_THRESHOLDS.VISIBLE_ITEMS
    };
  }

  /**
   * ОПТИМИЗИРОВАННАЯ СИСТЕМА УПРАВЛЕНИЯ ПАМЯТЬЮ
   * Использует глобальный менеджер очистки
   */
  setupMemoryManagement() {
    // Регистрируем функцию очистки в глобальном менеджере
    if (window.globalCleanupManager) {
      window.globalCleanupManager.registerCleanupFunction('tankManager', () => {
        this.performCacheCleanup();
      });
    } else {
      // Fallback если глобальный менеджер не доступен
      const cleanupInterval = setInterval(() => {
        this.performCacheCleanup();
      }, 300000);
      this.cleanupTasks.push(() => clearInterval(cleanupInterval));
    }

    // Очистка при выгрузке страницы
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    // Очистка при изменении видимости страницы
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.performCacheCleanup();
      }
    });
  }

  performCacheCleanup() {
    const now = Date.now();

    // Очистка фильтр кэша
    if (this.filterCache.size > this.maxCacheSize) {
      const entries = Array.from(this.filterCache.entries());
      const toDelete = entries.slice(0, Math.floor(this.maxCacheSize * 0.4));
      toDelete.forEach(([key]) => this.filterCache.delete(key));
    }

    // НЕ очищаем DOM кэш при обычной очистке - это ломает функциональность
    // domCache.clearElementCache();

    // console.log(`🧹 TankManager cache cleanup: ${this.filterCache.size} filter entries`);
  }

  cleanup() {
    this.cleanupTasks.forEach(task => task());
    this.filterCache.clear();
    this.tankNameCache.clear();
    this.tankIdCache.clear();
    // НЕ очищаем DOM кэш при cleanup - это ломает функциональность
    // domCache.clearElementCache();
    this.renderQueue = [];
    this.performanceMetrics = {
      filterTime: 0,
      renderTime: 0,
      cacheHits: 0,
      cacheMisses: 0,
      totalRenders: 0
    };
  }

  /**
   * Отмена текущего рендеринга
   */
  cancelCurrentRender() {
    if (this.isRendering) {
      this.isRendering = false;
      this.currentRenderKey = null;
    }
  }

  /**
   * Создание ID танка на основе имени
   */
  createTankId(tankName) {
    const iconPath = getTankIconPath(tankName);
    return iconPath.split('/').pop().replace('.webp', '');
  }

  /**
   * ВКЛЮЧЕНИЕ ВИРТУАЛИЗАЦИИ ДЛЯ БОЛЬШИХ СПИСКОВ
   */
  enableVirtualScrolling(tankCount) {
    this.virtualScrolling.enabled = tankCount > PERFORMANCE_THRESHOLDS.VIRTUAL_THRESHOLD;
    if (this.virtualScrolling.enabled) {
      // console.log(`🔧 Virtual scrolling enabled for ${tankCount} tanks`);
    }
  }

  /**
   * ПОЛУЧЕНИЕ КЭШИРОВАННОГО DOM ЭЛЕМЕНТА (делегируется к унифицированной системе)
   */
  getCachedDOMElement(selector) {
    return domCache.getCachedElement(selector);
  }

  /**
   * ПОЛУЧЕНИЕ ДАННЫХ ТАНКОВ С FALLBACK
   * Надежное получение данных танков из разных источников
   */
  getAllTanks() {
    // Приоритет: window.state.allTanks > window.tanksData > state.allTanks > this.allTanks
    return window.state?.allTanks || window.tanksData || state?.allTanks || this.allTanks || this.tanks || [];
  }

  /**
   * ОПТИМИЗИРОВАННАЯ ФИЛЬТРАЦИЯ ТАНКОВ
   * Высокопроизводительная фильтрация с кэшированием
   */
  getFilteredTanks() {
    const allTanks = this.getAllTanks();
    if (!allTanks || !Array.isArray(allTanks) || allTanks.length === 0) {
      console.warn('🚨 Tanks data not available');
      return [];
    }

    // Кэширование результатов фильтрации (обновлено для множественного выбора)
    const countriesKey = (state.selectedCountries || []).sort().join(',') || state.selectedCountry || 'all';
    const categoriesKey = (state.selectedCategories || []).sort().join(',') || state.selectedCategory || 'all';
    const filterKey = `${countriesKey}_${categoriesKey}_${state.searchQuery || ''}`;

    if (this.filterCache.has(filterKey)) {
      this.performanceMetrics.cacheHits++;
      return this.filterCache.get(filterKey);
    }

    this.performanceMetrics.cacheMisses++;
    const startTime = performance.now();
    let filteredTanks = allTanks;

    // Оптимизированная фильтрация по странам
    const selectedCountries = state.selectedCountries || [];
    const hasCountryFilter = selectedCountries.length > 0 || (state.selectedCountry && state.selectedCountry !== 'all');

    if (hasCountryFilter) {
      const countryMapping = this.getCountryMapping();
      let countrySet;

      if (selectedCountries.length > 0) {
        // Множественный выбор стран - используем Set для O(1) поиска
        countrySet = new Set(selectedCountries.map(country =>
          countryMapping[country.toLowerCase()] || country
        ));
      } else {
        // Одиночный выбор
        const expectedCountry = countryMapping[state.selectedCountry.toLowerCase()] || state.selectedCountry;
        countrySet = new Set([expectedCountry]);
      }

      filteredTanks = filteredTanks.filter(tank => countrySet.has(tank.country));
    }

    // Оптимизированная фильтрация по категориям
    const selectedCategories = state.selectedCategories || [];
    const hasCategoryFilter = selectedCategories.length > 0 || (state.selectedCategory && state.selectedCategory !== 'all');

    if (hasCategoryFilter) {
      let categorySet = new Set();

      if (selectedCategories.length > 0) {
        // Множественный выбор - предварительно создаем Set всех возможных типов
        selectedCategories.forEach(category => {
          categorySet.add(category);
          categorySet.add(RUSSIAN_TO_INTERNAL_TYPE_MAP[category]);
          // Добавляем обратное сопоставление
          Object.keys(RUSSIAN_TO_INTERNAL_TYPE_MAP).forEach(key => {
            if (RUSSIAN_TO_INTERNAL_TYPE_MAP[key] === category) {
              categorySet.add(key);
            }
          });
        });
      } else {
        // Одиночный выбор
        categorySet.add(state.selectedCategory);
        categorySet.add(RUSSIAN_TO_INTERNAL_TYPE_MAP[state.selectedCategory]);
        Object.keys(RUSSIAN_TO_INTERNAL_TYPE_MAP).forEach(key => {
          if (RUSSIAN_TO_INTERNAL_TYPE_MAP[key] === state.selectedCategory) {
            categorySet.add(key);
          }
        });
      }

      filteredTanks = filteredTanks.filter(tank => categorySet.has(tank.type));
    }

    // Поиск по названию
    if (state.searchQuery && state.searchQuery.trim()) {
      const query = state.searchQuery.toLowerCase().trim();
      filteredTanks = filteredTanks.filter(tank =>
        tank.name.toLowerCase().includes(query)
      );
    }

    // Кэшируем результат
    if (this.filterCache.size >= this.maxCacheSize) {
      const firstKey = this.filterCache.keys().next().value;
      this.filterCache.delete(firstKey);
    }
    this.filterCache.set(filterKey, filteredTanks);

    const filterTime = performance.now() - startTime;
    this.performanceMetrics.filterTime = filterTime;

    return filteredTanks;
  }

  /**
   * КЭШИРОВАННОЕ СОПОСТАВЛЕНИЕ СТРАН
   */
  getCountryMapping() {
    if (!this._countryMapping) {
      this._countryMapping = {
        'ussr': 'USSR',
        'germany': 'Germany',
        'usa': 'USA',
        'france': 'France',
        'uk': 'UK',
        'czech': 'Czech',
        'china': 'China',
        'japan': 'Japan',
        'poland': 'Poland',
        'sweden': 'Sweden',
        'italy': 'Italy',
        'international': 'International'
      };
    }
    return this._countryMapping;
  }

  /**
   * ОПТИМИЗИРОВАННЫЙ ПОИСК ТАНКА ПО ИМЕНИ
   */
  findTankByName(tankName) {
    // Проверяем кэш
    if (this.tankNameCache.has(tankName)) {
      return this.tankNameCache.get(tankName);
    }

    // Ищем танк
    const allTanks = this.getAllTanks();
    const tank = allTanks.find(t => t.name === tankName);

    // Кэшируем результат
    if (tank) {
      this.tankNameCache.set(tankName, tank);
      // Также кэшируем по ID
      const tankId = this.createTankId(tank.name);
      this.tankIdCache.set(tankId, tank);
    }

    return tank;
  }

  /**
   * ОПТИМИЗИРОВАННЫЙ ПОИСК ТАНКА ПО ID
   */
  findTankById(tankId) {
    // Проверяем кэш
    if (this.tankIdCache.has(tankId)) {
      return this.tankIdCache.get(tankId);
    }

    // Ищем танк
    const allTanks = this.getAllTanks();
    const tank = allTanks.find(t => this.createTankId(t.name) === tankId);

    // Кэшируем результат
    if (tank) {
      this.tankIdCache.set(tankId, tank);
      // Также кэшируем по имени
      this.tankNameCache.set(tank.name, tank);
    }

    return tank;
  }

  /**
   * ОБНОВЛЕНИЕ КЭШЕЙ ТАНКОВ
   */
  updateTankCaches() {
    // Очищаем старые кэши
    this.tankNameCache.clear();
    this.tankIdCache.clear();

    // Предварительно заполняем кэши для часто используемых танков
    const allTanks = this.getAllTanks();
    if (allTanks && allTanks.length > 0) {
      // Кэшируем первые 50 танков для быстрого доступа
      allTanks.slice(0, 50).forEach(tank => {
        this.tankNameCache.set(tank.name, tank);
        const tankId = this.createTankId(tank.name);
        this.tankIdCache.set(tankId, tank);
      });
    }
  }

  /**
   * Обновить список танков - С ЗАЩИТОЙ ОТ ПЕРЕКРЫТИЯ
   */
  updateTankList() {
    const filteredTanks = this.getFilteredTanks();

    if (!state.selectedTank) {
      this.showTankList(filteredTanks);
      this.hideTankCharacteristics();
    } else {
      this.showTankCharacteristics(state.selectedTank);
      this.hideTankList();
      this.forceHideTankList();
    }

    this.updateErrorState(filteredTanks);
  }

  /**
   * БЫСТРЫЙ ПОКАЗ СПИСКА ТАНКОВ - ИСПРАВЛЕНО ДЛЯ GRID LAYOUT
   */
  async showTankList(filteredTanks) {
    // КРИТИЧНО: Проверяем, что мы должны показывать tank-list
    const currentPath = window.location.pathname;
    const activeMenuItem = localStorage.getItem('activeMenuItem');

    if (currentPath !== '/vehicles' && activeMenuItem !== 'vehicles') {
      console.log('🚫 TankManager: Skipping tank list show - not on vehicles page');
      return;
    }

    // КРИТИЧНО: Не показываем список если выбран танк
    if (state.selectedTank) {
      console.log('🚫 TankManager: Skipping tank list show - tank is selected');
      return;
    }

    const tankListElement = getCachedElement('#tank-list');

    if (tankListElement) {
      // Если filteredTanks не передан, получаем его
      if (!filteredTanks) {
        filteredTanks = this.getFilteredTanks();
      }

      // ИСПРАВЛЕНО: ПРИНУДИТЕЛЬНОЕ ВОССТАНОВЛЕНИЕ GRID LAYOUT
      tankListElement.classList.remove('hidden', 'tank-list-hidden', 'tank-list-loading', 'visible');
      tankListElement.classList.add('tank-list-visible', 'fade-in');

      // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Принудительно восстанавливаем grid layout
      tankListElement.style.display = 'grid';
      tankListElement.style.visibility = 'visible';
      tankListElement.style.opacity = '1';
      // Убираем принудительные стили - используем CSS
      tankListElement.style.gridTemplateColumns = '';
      tankListElement.style.gap = '';
      tankListElement.style.padding = '';

      // БЫСТРЫЙ РЕНДЕРИНГ
      const { renderTankList } = await import('../components/tank-list/index.js');
      const allTanks = this.getAllTanks(); // Используем надежный источник данных
      renderTankList(filteredTanks, allTanks, this.handleTankSelection.bind(this), null);
    }

    const backButton = getCachedElement('#back-to-list-btn');
    if (backButton) {
      backButton.classList.add('hidden');
    }
  }

  /**
   * БЫСТРЫЙ ПОКАЗ ЭЛЕМЕНТА СПИСКА - ИСПРАВЛЕНО ДЛЯ GRID LAYOUT
   * Принудительное восстановление grid стилей
   */
  showTankListElement(filteredTanks) {
    const tankListElement = getCachedElement('#tank-list');

    if (tankListElement) {
      // БЫСТРАЯ ПРОВЕРКА: Проверяем текущее состояние
      const isAlreadyVisible = tankListElement.style.display === 'grid' &&
                               tankListElement.style.visibility === 'visible' &&
                               tankListElement.style.opacity === '1';

      if (isAlreadyVisible) {
        // console.log('🎯 Tank list already visible, skipping redundant show');
        return;
      }

      // ИСПРАВЛЕНО: ПРИНУДИТЕЛЬНОЕ ВОССТАНОВЛЕНИЕ GRID LAYOUT
      tankListElement.classList.remove('hidden', 'tank-list-hidden', 'tank-list-loading');
      tankListElement.classList.add('tank-list-visible');

      // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Принудительно восстанавливаем все grid стили
      tankListElement.style.display = 'grid';
      tankListElement.style.visibility = 'visible';
      tankListElement.style.opacity = '1';
      tankListElement.style.transform = 'translateY(0)';
      // Убираем принудительные стили - используем CSS
      tankListElement.style.gridTemplateColumns = '';
      tankListElement.style.gap = '';
      tankListElement.style.padding = '';

      // Дополнительная защита от проблем с layout
      tankListElement.style.width = '100%';
      tankListElement.style.maxWidth = '100%';
      tankListElement.style.boxSizing = 'border-box';

      // console.log(`✅ Tank list element shown: ${filteredTanks ? filteredTanks.length : 'unknown'} tanks`);
    }

    const backButton = getCachedElement('#back-to-list-btn');
    if (backButton) {
      backButton.classList.add('hidden');
    }
  }

  /**
   * ПОКАЗ СУЩЕСТВУЮЩЕГО СПИСКА С ПЛАВНОЙ АНИМАЦИЕЙ
   */
  showExistingTankList(tankListElement) {
    // Проверяем, не показан ли уже список
    if (tankListElement.classList.contains('instant-show')) {
      tankListElement.classList.remove('instant-show');
      return;
    }

    // Простая анимация без моргания
    tankListElement.classList.remove('hidden', 'tank-list-hidden', 'instant-show');
    tankListElement.classList.add('tank-list-visible');

    tankListElement.style.display = 'grid';
    tankListElement.style.visibility = 'visible';
    tankListElement.style.opacity = '0';
    tankListElement.style.transform = 'translateY(20px)';
    tankListElement.style.transition = 'none';

    // Красивая анимация появления снизу вверх
    setTimeout(() => {
      tankListElement.style.transition = 'opacity 0.4s ease-out, transform 0.4s ease-out';
      tankListElement.style.opacity = '1';
      tankListElement.style.transform = 'translateY(0)';
    }, 50);

    // Скрываем кнопку "Назад к списку"
    const backButton = getCachedElement('#back-to-list-btn');
    if (backButton) {
      backButton.classList.add('hidden');
    }
  }

  /**
   * Скрыть список танков - ОПТИМИЗИРОВАННАЯ ВЕРСИЯ
   */
  hideTankList() {
    const tankListElement = getCachedElement('#tank-list');
    if (tankListElement) {
      tankListElement.classList.add('hidden');
      tankListElement.style.display = 'none';
    }
  }

  /**
   * ПРИНУДИТЕЛЬНО СКРЫТЬ СПИСОК ТАНКОВ - ИСПРАВЛЕНО
   * КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Не используем cssText чтобы сохранить grid layout
   */
  forceHideTankList() {
    const tankListElement = getCachedElement('#tank-list');
    if (tankListElement) {
      console.log('🚫 FORCE HIDING tank list to prevent overlap');
      tankListElement.classList.add('hidden');
      tankListElement.classList.remove('tank-list-visible', 'instant-show', 'fade-in', 'visible');

      // ИСПРАВЛЕНО: Устанавливаем стили по отдельности, сохраняя grid layout
      tankListElement.style.display = 'none';
      tankListElement.style.visibility = 'hidden';
      tankListElement.style.opacity = '0';
    }
  }

  /**
   * Показать характеристики танка
   */
  async showTankCharacteristics(tank) {
    console.log(`🔥 TankManager.showTankCharacteristics: Показываем характеристики для ${tank.name}`);

    // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Создаем контейнер если он не существует
    let characteristicsContainer = document.getElementById('tank-characteristics-container');

    if (!characteristicsContainer) {
      console.warn('⚠️ Tank characteristics container not found, creating it...');

      // Создаем контейнер программно
      characteristicsContainer = document.createElement('div');
      characteristicsContainer.id = 'tank-characteristics-container';
      characteristicsContainer.className = 'content-section tank-characteristics hidden';
      characteristicsContainer.style.display = 'none';

      // Добавляем контейнер в main-content
      const mainContent = document.querySelector('.main-content') || document.querySelector('.main-content-container') || document.body;
      mainContent.appendChild(characteristicsContainer);

    }

    // Создаем контент через updateTankDetailsUI
    await updateTankDetailsUI(tank);

    // Показываем контейнер
    characteristicsContainer.classList.remove('hidden');
    characteristicsContainer.style.display = 'block';
    characteristicsContainer.style.visibility = 'visible';
    characteristicsContainer.style.opacity = '1';

    // Показываем кнопку "Назад к списку"
    const backButton = getCachedElement('#back-to-list-btn');
    if (backButton) {
      backButton.classList.remove('hidden');
    }
  }

  /**
   * Скрыть характеристики танка
   */
  hideTankCharacteristics() {
    const characteristicsContainer = document.getElementById('tank-characteristics-container');
    if (characteristicsContainer) {
      characteristicsContainer.classList.add('hidden');
      characteristicsContainer.style.display = 'none';
      characteristicsContainer.style.opacity = '0';
      characteristicsContainer.style.visibility = 'hidden';
    }
  }

  /**
   * Обновить состояние ошибки
   */
  updateErrorState(filteredTanks) {
    const tankError = getCachedElement('#tank-error');
    if (tankError) {
      const shouldShowError =
        !state.selectedTank && state.searchQuery.length > 0 && filteredTanks.length === 0;
      tankError.classList.toggle('hidden', !shouldShowError);
      if (shouldShowError) tankError.textContent = 'Танк не найден';
    }
  }

  /**
   * Обработка выбора танка
   */
  async handleTankSelection(tankName, forceSelect = false) {
    // ОПТИМИЗИРОВАНО: Убран избыточный лог для production

    const tank = this.findTankByName(tankName);

    if (!tank) {
      console.error(`Tank ${tankName} not found.`);
      return;
    }

    // Проверяем, выбран ли уже этот танк
    const isSelectedTank = state.selectedTank && state.selectedTank.name === tank.name;
    // ОПТИМИЗИРОВАНО: Убран избыточный лог для production

    if (state.restoringTankFromReload) {
      console.log('Восстановление танка после F5, устанавливаем forceSelect');
      forceSelect = true;
    }

    // ВАЖНО: Если танк уже выбран и это не принудительный выбор, отменяем выбор
    if (isSelectedTank && !forceSelect) {
      console.log('🔄 Отменяем выбор танка - возвращаемся к списку');
      state.selectedTank = null;
      localStorage.removeItem('selectedTank');

      // Возвращаемся к vehicles без танка
      history.pushState(null, 'Vehicles', '/vehicles');

      // Показываем список танков
      this.updateTankList();

      // Синхронизируем бейджики сравнения после возврата к списку
      if (window.advancedCompareSystem) {
        window.advancedCompareSystem.syncAllBadges();
      }

      return;
    }

    // СОХРАНЯЕМ ТЕКУЩИЕ ФИЛЬТРЫ ПЕРЕД ВЫБОРОМ ТАНКА
    if (!forceSelect) {
      const filtersBeforeSelection = {
        // Сохраняем только те фильтры, которые пользователь выбрал вручную
        selectedCountry: state.countrySelectedManually ? state.selectedCountry : 'all',
        selectedCategory: state.categorySelectedManually ? state.selectedCategory : 'all',
        searchQuery: state.searchQuery,
        countrySelectedManually: state.countrySelectedManually,
        categorySelectedManually: state.categorySelectedManually,
        // Добавляем флаг пользовательских фильтров
        userSelectedFilters: state.countrySelectedManually || state.categorySelectedManually
      };
      localStorage.setItem('filtersBeforeSelection', JSON.stringify(filtersBeforeSelection));
      console.log('💾 Сохранены фильтры перед выбором танка:', filtersBeforeSelection);
    }

    console.log(`🔥 Выбираем танк: ${tank.name}`);

    // ПРИНУДИТЕЛЬНАЯ ОЧИСТКА данных сборок при смене танка
    try {
      const { clearBuildsData } = await import('../components/builds/BuildsTable.js');
      clearBuildsData();
      console.log('🧹 TankManager: Builds data cleared for new tank selection');
    } catch (error) {
      console.warn('Failed to clear builds data:', error);
    }

    // Выбираем танк
    state.selectedTank = tank;

    // ВАЖНО: Обновляем фильтры при выборе танка
    try {
      const { updateFilterSelection } = await import('../components/tank-list/index.js');

      // Устанавливаем фильтры в состоянии
      state.selectedCountry = tank.country;
      state.selectedCategory = tank.type;

      // Обновляем визуальное отображение фильтров
      const countryForUI = tank.country === 'USSR' ? 'ussr' : tank.country.toLowerCase();
      updateFilterSelection('country', countryForUI);
      updateFilterSelection('category', tank.type);


    } catch (error) {
      console.error('Failed to update filters in TankManager:', error);
    }

    // Сохраняем в localStorage
    const tankToSave = {
      name: tank.name,
      country: tank.country,
      type: tank.type,
      countrySelectedManually: state.countrySelectedManually,
      categorySelectedManually: state.categorySelectedManually,
      tankId: this.createTankId(tank.name),
    };
    localStorage.setItem('selectedTank', JSON.stringify(tankToSave));

    // Обновляем фильтры
    this.updateFiltersForTank(tank);

    // ПРИНУДИТЕЛЬНО скрываем список танков перед показом характеристик
    this.forceHideTankList();

    // Обновляем URL для нового выбора танка
    if (!forceSelect) {
      import('../utils/helpers.js').then(module => {
        const shortId = module.createTankUrlId(tank.name);
        console.log(`🌐 TankManager: Creating URL for "${tank.name}" -> /vehicles/${shortId}`);
        history.pushState(
          { tankName: tank.name, section: 'vehicles' },
          `Tank: ${tank.name}`,
          `/vehicles/${shortId}`
        );
      });
    } else {
      // Используем replaceState при восстановлении состояния
      import('../utils/helpers.js').then(module => {
        const shortId = module.createTankUrlId(tank.name);
        console.log(`🌐 TankManager: Updating URL for "${tank.name}" -> /vehicles/${shortId}`);
        history.replaceState(
          { tankName: tank.name, section: 'vehicles' },
          `Tank: ${tank.name}`,
          `/vehicles/${shortId}`
        );
      });
    }

    // Показываем характеристики танка
    this.showTankCharacteristics(tank);
    state.restoringTankFromReload = false;
  }

  /**
   * Обновить фильтры для выбранного танка
   */
  updateFiltersForTank(tank) {
    if (tank.country) {
      state.selectedCountry = tank.country;
      const countryForUI = this.getCountryForUI(tank.country);
      updateFilterSelection('country', countryForUI);
    }

    if (tank.type) {
      const internalType =
        RUSSIAN_TO_INTERNAL_TYPE_MAP[tank.type] ||
        ENGLISH_TO_INTERNAL_TYPE_MAP[tank.type] ||
        tank.type;
      state.selectedCategory = internalType;
      updateFilterSelection('category', tank.type);
    }
  }

  /**
   * Преобразование названий стран из внутреннего формата в формат UI
   */
  getCountryForUI(internalCountry) {
    const countryMap = {
      USSR: 'ussr',
      Germany: 'germany',
      USA: 'usa',
      France: 'france',
      UK: 'uk',
      Czech: 'czech',
      China: 'china',
      Japan: 'japan',
      Poland: 'poland',
      Sweden: 'sweden',
      Italy: 'italy',
      International: 'international',
    };
    return countryMap[internalCountry] || internalCountry.toLowerCase();
  }

  /**
   * Применить фильтры и отобразить список танков - КВАНТОВО ИСПРАВЛЕННОЕ
   * КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Предотвращение дублирования танков
   */
  async applyFiltersAndRenderTankList(keepHidden = false, force = false) {

    // КРИТИЧНО: Проверяем, что мы должны рендерить tank-list
    const currentPath = window.location.pathname;
    const activeMenuItem = localStorage.getItem('activeMenuItem');
    const bodyActiveSection = document.body.getAttribute('data-active-section');

    // Проверяем, что мы на vehicles странице
    const isVehiclesPage = currentPath === '/vehicles' ||
                          activeMenuItem === 'vehicles' ||
                          bodyActiveSection === 'vehicles';

    if (!isVehiclesPage && !force) {
      return;
    }

    // КРИТИЧНО: Не рендерим список если выбран танк
    if (state.selectedTank && !force) {
      console.log('🚫 TankManager: Skipping render - tank is selected');
      return;
    }

    // ЗАЩИТА: Проверяем что танки загружены (приоритет window.state и window.tanksData)
    const allTanks = window.state?.allTanks || window.tanksData || state?.allTanks || this.allTanks || this.tanks;

    if (!allTanks || allTanks.length === 0) {
      console.warn('⚠️ TankManager: No tanks loaded, skipping render');
      console.warn('🔍 Debug - state.allTanks:', state?.allTanks?.length || 'undefined');
      console.warn('🔍 Debug - window.state.allTanks:', window.state?.allTanks?.length || 'undefined');
      console.warn('🔍 Debug - window.tanksData:', window.tanksData?.length || 'undefined');
      console.warn('🔍 Debug - this.allTanks:', this.allTanks?.length || 'undefined');
      return;
    }

    // Дополнительная проверка для overview страницы
    if (currentPath === '/' && bodyActiveSection === 'overview') {
      return;
    }
    console.log('✅ TankManager: Using tanks from:',
      window.state?.allTanks ? 'window.state.allTanks' :
      window.tanksData ? 'window.tanksData' :
      state?.allTanks ? 'state.allTanks' :
      this.allTanks ? 'this.allTanks' : 'unknown');
    console.log('✅ TankManager: Tank count:', allTanks.length);

    // УСИЛЕННАЯ ЗАЩИТА ОТ МНОЖЕСТВЕННЫХ ВЫЗОВОВ
    const now = Date.now();
    const renderKey = `${now}_${Math.random()}`;

    if (!force && (this.isRendering || (this.lastRenderTime && (now - this.lastRenderTime) < 50))) { // Уменьшено до 50ms для отзывчивости фильтров
      console.log(`🚫 TankManager: Already rendering or too frequent calls (${now - this.lastRenderTime}ms), SKIPPING for UI responsiveness`);
      return;
    }

    if (force) {
      console.log(`🚀 TankManager: FORCE rendering - bypassing protection`);
    }

    this.isRendering = true;
    this.lastRenderTime = now;
    this.currentRenderKey = renderKey;

    try {
      // ИСПРАВЛЕНО: Всегда выполняем рендеринг если он начат
      await this.renderTankListDirect(keepHidden);
    } finally {
      // Проверяем, что это тот же рендеринг
      if (this.currentRenderKey === renderKey) {
        this.isRendering = false;
        this.currentRenderKey = null;
      }
    }
  }

  /**
   * Прямой рендеринг списка танков
   */
  async renderTankListDirect(keepHidden = false) {
    try {
      const filteredTanks = this.getFilteredTanks();
      const tankListElement = getCachedElement('#tank-list');

      if (!tankListElement) {
        console.error('🚨 TankManager: Tank list element not found');
        return;
      }

      // Рендерим танки через оригинальный рендерер
      const { renderTankList } = await import('../components/tank-list/index.js');
      const allTanks = this.getAllTanks(); // Используем надежный источник данных
      renderTankList(
        filteredTanks,
        allTanks,
        this.handleTankSelection.bind(this),
        (state?.selectedTank || window.state?.selectedTank)?.name || null
      );

      // Показываем список если не нужно скрывать
      if (!keepHidden) {
        this.showTankListElement(filteredTanks);
      }

      // Бейджики уже созданы в правильном состоянии, минимальная синхронизация
      if (window.advancedCompareSystem) {
        window.advancedCompareSystem.syncAllBadges();
      }

    } catch (error) {
      console.error('Error in renderTankListDirect:', error);
    }
  }

  /**
   * Внутренний метод рендеринга - вызывается только через координатор
   */
  async internalRenderTankList() {
    // КРИТИЧЕСКАЯ ПРОВЕРКА: Если танк выбран, НЕ показываем список
    if (state.selectedTank) {
      // console.log('🚫 TANK SELECTED - blocking tank list render to prevent overlap');
      return;
    }

    // ПОЛУЧАЕМ ЭЛЕМЕНТ СПИСКА ТАНКОВ ОДИН РАЗ
    const tankListElement = getCachedElement('#tank-list');
    if (!tankListElement) {
      console.error('❌ Tank list element not found');
      return;
    }

    // console.log('🎬 TankManager: Internal render starting');

    const filteredTanks = this.getFilteredTanks();
    const selectedTankName = state.selectedTank ? state.selectedTank.name : null;

    // Скрываем детали танка
    this.hideTankCharacteristics();

    // console.log(`🎬 Отображаем ${filteredTanks.length} танков после фильтрации`);

    // Обновляем отображение фильтров
    const countryForUI = this.getCountryForUI(state.selectedCountry);
    updateFilterSelection('country', countryForUI);

    const categoryForUI =
      Object.keys(RUSSIAN_TO_INTERNAL_TYPE_MAP).find(
        key => RUSSIAN_TO_INTERNAL_TYPE_MAP[key] === state.selectedCategory
      ) || state.selectedCategory;
    updateFilterSelection('category', categoryForUI);

    // ПРИНУДИТЕЛЬНО скрываем характеристики если они показаны
    const characteristicsContainer = getCachedElement('#tank-characteristics-container');
    if (characteristicsContainer) {
      characteristicsContainer.classList.add('hidden');
      characteristicsContainer.style.display = 'none';
      characteristicsContainer.style.visibility = 'hidden';
      characteristicsContainer.style.opacity = '0';
    }

    // Скрываем кнопку "Назад к списку"
    const backButton = getCachedElement('#back-to-list-btn');
    if (backButton) {
      backButton.classList.add('hidden');
    }

    // МГНОВЕННОЕ ПОЯВЛЕНИЕ БЕЗ АНИМАЦИЙ
    tankListElement.classList.remove('hidden', 'tank-list-hidden');
    tankListElement.classList.add('tank-list-visible');
    tankListElement.style.display = 'grid';
    tankListElement.style.visibility = 'visible';
    tankListElement.style.opacity = '1'; // МГНОВЕННО ВИДИМЫЙ
    tankListElement.style.transform = 'none'; // БЕЗ ТРАНСФОРМАЦИЙ
    tankListElement.style.transition = 'none';

    // РЕНДЕРИНГ СПИСКА ТАНКОВ
    const { renderTankList } = await import('../components/tank-list/index.js');
    renderTankList(
      filteredTanks,
      state.allTanks,
      this.handleTankSelection.bind(this),
      selectedTankName
    );

    // МГНОВЕННОЕ ПОЯВЛЕНИЕ БЕЗ АНИМАЦИИ
    // Убираем setTimeout и анимации для мгновенного отображения

    console.log('🎬 Internal render complete - New list rendered with animation');
  }

  /**
   * Показать характеристики танка по ID
   */
  showTankCharacteristicsById(tankId) {
    const tank = this.findTankById(tankId);
    if (!tank) {
      console.error(`Tank with ID ${tankId} not found`);
      return;
    }

    // Устанавливаем выбранный танк в доступном состоянии
    if (state) {
      state.selectedTank = tank;
    }
    if (window.state) {
      window.state.selectedTank = tank;
    }
    window.currentTankId = tankId;

    // НЕ СКРЫВАЕМ СЕКЦИИ - просто показываем характеристики поверх списка
    this.showTankCharacteristics(tank);
  }

  /**
   * БЫСТРЫЙ ПРИНУДИТЕЛЬНЫЙ ПОКАЗ СПИСКА ТАНКОВ - ИСПРАВЛЕНО ДЛЯ GRID
   * Принудительное восстановление grid layout
   */
  async forceShowTankList() {
    console.log('🎬 FAST Forcing tank list display');

    state.selectedTank = null;
    window.appState = { selectedTank: null };

    const filteredTanks = this.getFilteredTanks();

    const tankListElement = getCachedElement('#tank-list');
    if (tankListElement) {
      // ИСПРАВЛЕНО: ПРИНУДИТЕЛЬНОЕ ВОССТАНОВЛЕНИЕ GRID LAYOUT
      tankListElement.classList.remove('hidden', 'tank-list-hidden', 'visible');
      tankListElement.classList.add('tank-list-visible', 'fade-in');

      // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Принудительно восстанавливаем grid стили
      tankListElement.style.display = 'grid';
      tankListElement.style.visibility = 'visible';
      tankListElement.style.opacity = '1';
      // Убираем принудительные стили - используем CSS
      tankListElement.style.gridTemplateColumns = '';
      tankListElement.style.gap = '';
      tankListElement.style.padding = '';

      // БЫСТРЫЙ РЕНДЕРИНГ
      const { renderTankList } = await import('../components/tank-list/index.js');
      const allTanks = this.getAllTanks(); // Используем надежный источник данных
      renderTankList(filteredTanks, allTanks, this.handleTankSelection.bind(this), null);

      console.log(`🎬 FAST Force show complete: ${filteredTanks.length} tanks`);
    }

    this.hideTankCharacteristics();

    const backBtn = getCachedElement('#back-icon');
    if (backBtn) {
      backBtn.classList.add('hidden');
    }

    return 'Tank list display forced quickly';
  }

  /**
   * Оптимизированное отображение списка танков без перерисовки
   */
  optimizedShowTankList() {
    console.log('Optimized tank list display - no DOM manipulation');

    // Сбрасываем выбранный танк если он был
    if (state.selectedTank) {
      state.selectedTank = null;
      window.appState = { selectedTank: null };
    }

    // Скрываем характеристики танка
    this.hideTankCharacteristics();

    // Показываем кнопку назад если нужно
    const backBtn = getCachedElement('#back-icon');
    if (backBtn) {
      backBtn.classList.add('hidden');
    }

    // Убираем выделение с танков если оно было
    const selectedTanks = document.querySelectorAll('.tank-card.selected');
    selectedTanks.forEach(tank => {
      tank.classList.remove('selected');
    });

    // Список уже отображен, просто обеспечиваем правильное состояние
    const tankListElement = getCachedElement('#tank-list');
    if (tankListElement) {
      tankListElement.classList.remove('hidden');
      tankListElement.style.display = 'grid';
      tankListElement.style.opacity = '1';
      tankListElement.style.visibility = 'visible';
    }

    return 'Tank list optimized display completed';
  }
}

// Экспортируем экземпляр (класс уже экспортирован выше)
export const tankManager = new TankManager();
