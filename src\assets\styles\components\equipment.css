/* Стили для системы оборудования танков */

.equipment-section {
  width: 100%;
  max-width: 1400px;
  margin-top: 40px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.equipment-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.btn-reset-equipment {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-reset-equipment:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Слоты оборудования - КОМПАКТНЫЕ КАК РАНЬШЕ */
.equipment-slots-container {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  justify-content: center;
}

.equipment-slot {
  width: 61px;
  height: 61px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.equipment-slot:hover {
  border-color: #4caf50;
  background: rgba(76, 175, 80, 0.2);
  transform: scale(1.05);
}

.slot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.slot-number {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
}

.slot-clear {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.slot-clear:hover {
  background: rgba(239, 68, 68, 0.4);
  transform: scale(1.1);
}

.slot-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.slot-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

.slot-icon {
  font-size: 1.5rem;
  margin-bottom: 4px;
  opacity: 0.7;
}

.slot-text {
  font-size: 0.6rem;
  opacity: 0.8;
  text-align: center;
  line-height: 1;
}

/* Установленное оборудование - КОМПАКТНОЕ */
.installed-equipment {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.installed-equipment .equipment-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 2px;
}

.installed-equipment .equipment-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.installed-equipment .equipment-name {
  font-size: 0.6rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1px;
  line-height: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 100%;
}

.installed-equipment .equipment-type {
  font-size: 0.5rem;
  padding: 1px 4px;
  border-radius: 6px;
  font-weight: 500;
}

/* Категории оборудования */
.equipment-categories {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.category-filter {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.category-filter:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(139, 92, 246, 0.5);
  color: #ffffff;
}

.category-filter.active {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  border-color: #8b5cf6;
  color: #ffffff;
}

.category-icon {
  font-size: 1rem;
}

/* Список оборудования */
.equipment-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
}

.equipment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.equipment-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(139, 92, 246, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.equipment-item .equipment-icon {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.equipment-item .equipment-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.equipment-info {
  flex: 1;
}

.equipment-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
}

.equipment-type {
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 8px;
  font-weight: 500;
  margin-bottom: 4px;
  display: inline-block;
}

.equipment-type.standard {
  background: rgba(107, 114, 128, 0.3);
  color: #9ca3af;
}

.equipment-type.special {
  background: rgba(59, 130, 246, 0.3);
  color: #60a5fa;
}

.equipment-type.improved {
  background: rgba(139, 92, 246, 0.3);
  color: #a78bfa;
}

.equipment-type.bond {
  background: rgba(245, 158, 11, 0.3);
  color: #fbbf24;
}

.equipment-description {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
  line-height: 1.3;
}

.equipment-cost {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

/* Адаптивность */
@media (max-width: 768px) {
  .equipment-slots-container {
    gap: 6px;
  }

  .equipment-slot {
    width: 55px;
    height: 55px;
  }

  .equipment-categories {
    justify-content: center;
  }

  .equipment-list {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .equipment-section {
    padding: 16px;
    margin-top: 24px;
  }

  .equipment-slot {
    width: 50px;
    height: 50px;
  }

  .equipment-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .category-filter {
    justify-content: center;
  }
}
