<!doctype html>
<html lang="ru" data-theme="tank-theme" class="h-full">
  <head>
    <!-- МГНОВЕННОЕ ПРИМЕНЕНИЕ ТЕМЫ БЕЗ МЕРЦАНИЯ -->
    <script>
        // Применяем тему ДО загрузки CSS
        const savedTheme = localStorage.getItem('theme') || 'dark';
        document.documentElement.className = `h-full theme-${savedTheme}`;
        document.documentElement.setAttribute('data-theme', savedTheme);

        // МГНОВЕННО устанавливаем правильный фон для body
        function applyInstantBackground() {
            const body = document.body;
            if (savedTheme === 'dark') {
                body.classList.add('gray-bg');
                body.style.backgroundColor = '#2a2155';
                body.style.backgroundImage = 'radial-gradient(circle at 30% 20%, rgba(85, 64, 174, 0.5) 0%, transparent 45%), radial-gradient(circle at 80% 80%, rgba(123, 31, 162, 0.4) 0%, transparent 50%)';
                body.style.transition = 'none';
                body.style.animation = 'none';
            } else {
                body.classList.remove('gray-bg');
                body.style.backgroundColor = '';
                body.style.backgroundImage = '';
            }
        }

        // Применяем сразу при загрузке DOM
        document.addEventListener('DOMContentLoaded', applyInstantBackground);

        // Применяем еще раз через небольшую задержку для гарантии
        setTimeout(applyInstantBackground, 10);

        // МГНОВЕННО устанавливаем CSS переменные
        const root = document.documentElement;
        if (savedTheme === 'dark') {
            root.style.setProperty('--bg-primary', '#2a2155');
            root.style.setProperty('--bg-secondary', '#1e293b');
            root.style.setProperty('--text-primary', '#ffffff');
            root.style.setProperty('--text-secondary', '#cbd5e1');
        } else {
            root.style.setProperty('--bg-primary', '#ffffff');
            root.style.setProperty('--bg-secondary', '#f8f9fa');
            root.style.setProperty('--text-primary', '#1a1a1a');
            root.style.setProperty('--text-secondary', '#6c757d');
        }
    </script>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self' ws: wss:; object-src 'none'; base-uri 'self'; form-action 'self';">
    <meta name="description" content="AI Efficiency - анализ эффективности танков World of Tanks с искусственным интеллектом" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>AI Efficiency</title>

    <!-- Critical loading styles -->
    <style>
      body {
        visibility: visible;
        opacity: 1;
        transition: none;
        animation: none;
        background-color: #2a2155;
        background-image: radial-gradient(circle at 30% 20%, rgba(85, 64, 174, 0.5) 0%, transparent 45%),
                          radial-gradient(circle at 80% 80%, rgba(123, 31, 162, 0.4) 0%, transparent 50%);
      }

      /* Определение класса gray-bg для предотвращения серого мигания */
      body.gray-bg {
        background-color: #2a2155;
        background-image: radial-gradient(circle at 30% 20%, rgba(85, 64, 174, 0.5) 0%, transparent 45%),
                          radial-gradient(circle at 80% 80%, rgba(123, 31, 162, 0.4) 0%, transparent 50%);
      }

      /* Отключение переходов только для body и app-container */
      body {
        transition: none;
        animation: none;
      }

      .app-container {
        transition: none;
        animation: none;
      }

      /* Принудительное включение анимаций только для уведомлений */
      .notification-container .notification,
      .notification-container {
        transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important, opacity 0.4s ease-out !important;
        animation: initial !important;
      }

      .notification-container .notification.swipe-out,
      .notification-container .notification.fade-out {
        transition: transform 0.4s ease-in !important, opacity 0.4s ease-in !important;
        animation: none !important;
      }

      /* КРИТИЧЕСКИЕ СТИЛИ - предотвращение серого мигания */
      /* Все окна имеют одинаковый многоцветный градиент как AI Efficiency */
      .settings-category,
      .action-card,
      .feature-card,
      .overview-hero {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(59, 130, 246, 0.12) 25%, rgba(139, 92, 246, 0.15) 50%, rgba(236, 72, 153, 0.12) 75%, rgba(245, 158, 11, 0.15) 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.08) !important;
      }

      /* КРИТИЧЕСКИЕ СТИЛИ - предотвращение серого мигания */
      .settings-category,
      .action-card,
      .feature-card,
      .overview-hero {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(51, 65, 85, 0.4) 100%) !important;
        border: 1px solid rgba(139, 92, 246, 0.15) !important;
      }

      .action-card {
        border: 1px solid rgba(59, 130, 246, 0.25) !important;
      }

      .feature-card {
        border: 1px solid rgba(16, 185, 129, 0.2) !important;
      }



      .loader {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        max-width: 6rem;
        margin-top: 3rem;
        margin-bottom: 3rem;
      }

      .loader:before,
      .loader:after {
        content: "";
        position: absolute;
        border-radius: 50%;
        animation: pulsOut 1.8s ease-in-out infinite;
        filter: drop-shadow(0 0 1rem rgba(255, 255, 255, 0.75));
      }

      .loader:before {
        width: 100%;
        padding-bottom: 100%;
        box-shadow: inset 0 0 0 1rem #fff;
        animation: pulsIn 1.8s ease-in-out infinite !important;
        animation-play-state: running !important;
      }

      .loader:after {
        width: calc(100% - 2rem);
        padding-bottom: calc(100% - 2rem);
        box-shadow: 0 0 0 0 #fff;
        animation: pulsOut 1.8s ease-in-out infinite !important;
        animation-play-state: running !important;
      }

      @keyframes pulsIn {
        0% {
          box-shadow: inset 0 0 0 1rem #fff;
          opacity: 1;
        }
        50%, 100% {
          box-shadow: inset 0 0 0 0 #fff;
          opacity: 0;
        }
      }

      @keyframes pulsOut {
        0%, 50% {
          box-shadow: 0 0 0 0 #fff;
          opacity: 0;
        }
        100% {
          box-shadow: 0 0 0 1rem #fff;
          opacity: 1;
        }
      }
    </style>

    <!-- Loading overlay -->
    <script>
      // МАКСИМАЛЬНАЯ защита от дублирования лоадеров
      (function() {
        if (window.loaderCreated || document.getElementById('ultra-instant-loading')) {
          console.log('🛡️ Loader already exists, aborting creation');
          return;
        }
        window.loaderCreated = true;

        // Удаляем все существующие лоадеры
        const existingLoaders = document.querySelectorAll('[id*="loading"], [class*="loader"], [style*="z-index: 999"]');
        existingLoaders.forEach(loader => loader.remove());

        document.write(`
          <div id="ultra-instant-loading" style="position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(42,33,85,0.8);backdrop-filter:blur(10px);z-index:999999;display:flex;align-items:center;justify-content:center">
          <div style="width:380px;height:270px;background:linear-gradient(135deg,rgba(32,25,55,0.95),rgba(26,20,45,0.95));border:1px solid rgba(139,92,246,0.3);border-radius:16px;padding:30px;text-align:center;display:flex;flex-direction:column;justify-content:space-between;align-items:center">
            <div style="flex:1;display:flex;align-items:center;justify-content:center;min-height:120px">
              <div style="width:70px;height:70px;display:flex;align-items:center;justify-content:center">
                <span class="loader"></span>
              </div>
            </div>
            <div style="flex:0 0 auto;width:100%;margin-top:auto">
              <div id="ultra-instant-status" style="color:#ffffff;font-size:1.1rem;font-weight:600;margin:0 0 15px 0;text-align:center">Инициализация...</div>
              <div style="width:100%;height:6px;background:rgba(139,92,246,0.2);border-radius:3px;margin:15px 0 10px 0;overflow:hidden;position:relative">
                <div id="ultra-instant-progress-fill" style="height:100%;background:linear-gradient(90deg,#8b5cf6,#a855f7,#c084fc);border-radius:3px;transition:width 0.3s ease;width:0%;position:relative"></div>
              </div>
              <div id="ultra-instant-progress-text" style="color:rgba(255,255,255,0.9);font-size:1rem;font-weight:600;margin:8px 0 0 0;width:100%;text-align:center">0%</div>
            </div>
          </div>
        </div>
        `);
        console.log('🎯 Single loader created successfully');
      })();
      // Ultra-instant loader created

      // Упрощенная анимация лоадера для быстрой загрузки
      setTimeout(() => {
        const loader = document.querySelector('.loader');
        if (loader) {
          // Простая анимация без лишних стилей
          loader.style.animation = 'pulsOut 1.8s ease-in-out infinite';
          console.log('🎯 Simple loader animation started');
        }
      }, 1);

      window.hideUltraInstantLoader = function() {
        if (window.loaderHidden) return;
        window.loaderHidden = true;
        const ultra = document.getElementById('ultra-instant-loading');
        if (ultra) {
          ultra.style.opacity = '0';
          ultra.style.transition = 'opacity 0.15s ease';
          setTimeout(() => ultra.remove(), 150);
        }
      };

      window.updateProgress = function(percent, status) {
        const progressFill = document.getElementById('ultra-instant-progress-fill');
        const progressText = document.getElementById('ultra-instant-progress-text');
        const statusText = document.getElementById('ultra-instant-status');
        if (progressFill) progressFill.style.width = percent + '%';
        if (progressText) progressText.textContent = percent + '%';
        if (statusText && status) statusText.textContent = status;
      };
    </script>

    <style>
      :root {
        --primary-color: #3b82f6;
        --secondary-color: #1e40af;
        --background-color: #0f172a;
        --surface-color: #1e293b;
        --text-primary: #f8fafc;
        --text-secondary: #cbd5e1;
        --border-color: #334155;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --error-color: #ef4444;
        --transition-fast: 150ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
        --transition-normal: 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
        --border-radius: 8px;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      *, *::before, *::after {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        transition: none;
        animation: none;
        animation-duration: 0s;
        animation-delay: 0s;
      }

      /* ВОССТАНОВЛЕНИЕ ОРИГИНАЛЬНЫХ СТИЛЕЙ ЛОАДЕРА */
      .loader {
        position: relative;
        display: block;
        width: 4rem;
        height: 4rem;
        border-radius: 50%;
        margin: 0 auto;
      }

      .loader::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        padding-bottom: 100%;
        border-radius: 50%;
        box-shadow: inset 0 0 0 1rem #fff;
        animation: pulsIn 1.8s ease-in-out infinite !important;
        animation-play-state: running !important;
      }

      .loader::after {
        content: '';
        position: absolute;
        top: 1rem;
        left: 1rem;
        width: calc(100% - 2rem);
        padding-bottom: calc(100% - 2rem);
        border-radius: 50%;
        box-shadow: 0 0 0 0 #fff;
        animation: pulsOut 1.8s ease-in-out infinite !important;
        animation-play-state: running !important;
      }

      html, body {
        margin: 0;
        padding: 0;
        background: #2a2155;
        background-image: radial-gradient(circle at 30% 20%, rgba(85, 64, 174, 0.5) 0%, transparent 45%),
                          radial-gradient(circle at 80% 80%, rgba(123, 31, 162, 0.4) 0%, transparent 50%),
                          radial-gradient(circle at 10% 60%, rgba(59, 28, 218, 0.3) 0%, transparent 40%),
                          radial-gradient(circle at 70% 30%, rgba(172, 69, 230, 0.2) 0%, transparent 35%);
        background-attachment: fixed;
        overflow-x: hidden;
        opacity: 1;
        visibility: visible;
        font-family: 'Inter', sans-serif;
        color: #f8fafc;
        line-height: 1.6;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-feature-settings: 'kern' 1;
      }

      #app {
        opacity: 1;
        visibility: visible;
        background: transparent;
        min-height: 100vh;
        display: block;
      }

      .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        width: 250px;
        height: 100vh;
        background: linear-gradient(180deg, rgba(45, 35, 75, 0.75) 0%, rgba(35, 28, 60, 0.7) 100%);
        backdrop-filter: blur(20px) saturate(200%);
        border-right: 1.5px solid rgba(255, 255, 255, 0.12);
        z-index: 10;
        color: var(--text-secondary);
        transform: translateZ(0);
        will-change: transform;
        contain: layout style paint;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        opacity: 1;
        visibility: visible;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      /* СТИЛИ КОНТЕЙНЕРА УПРАВЛЯЮТСЯ В scrollbar-new.css */

      /* СТИЛИ СКРОЛЛБАРА УПРАВЛЯЮТСЯ В scrollbar-new.css */

      .hidden,
      body .hidden,
      .content-section.hidden,
      #tank-list.hidden,
      #tank-characteristics-container.hidden {
        display: none;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        position: absolute;
        left: -9999px;
        z-index: -1;
      }

      .section-visible {
        display: block;
        opacity: 1;
        visibility: visible;
        pointer-events: auto;
        position: relative;
        left: auto;
        z-index: auto;
      }

      .tank-list-visible {
        display: grid;
      }

      /* Vehicles Section Styles */
      #vehicles-section {
        padding: 1rem;
        max-width: 100%;
        overflow-y: auto;
      }

      .vehicles-filters {
        margin-bottom: 0.5rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .tank-list-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, 200px);
        gap: 0.5rem;
        justify-content: space-evenly;
        padding: 0;
      }

      /* Убираем отступы для страниц характеристик танков */
      #vehicles-section.tank-page {
        padding: 0;
      }

      /* Убираем отступы у main-content-container для страниц танков - высокая специфичность */
      body .main-content-container.tank-details-page {
        padding: 0;
        padding-top: 0;
        margin: 0;
        margin-top: 0;
      }

      /* Дополнительная защита для кнопки назад при обновлении страницы */
      body .main-content-container.tank-details-page .back-button-container {
        margin-top: 0.5rem;
        padding-top: 0;
        padding: 0;
      }

      /* Защита от отступов vehicles-section - высокая специфичность */
      body #vehicles-section.tank-page {
        padding: 0;
        padding-top: 0;
        margin: 0;
        margin-top: 0;
      }

      /* Убираем отступы у content-section внутри vehicles */
      body #vehicles-section.tank-page .content-section {
        padding: 0;
        padding-top: 0;
        margin: 0;
        margin-top: 0;
      }

      /* Убираем отступы у tank-characteristics-page */
      body .tank-characteristics-page {
        padding-top: 0;
        margin-top: 0;
      }

      /* Дополнительная специфичность для section-container */
      body #vehicles-section.tank-page .section-container {
        padding: 0;
        margin: 0;
      }

      /* Tier Badge Styles */
      .tank-tier-badge {
        position: absolute;
        top: 8px;
        left: 8px;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.2);
      }

      .tier-roman {
        color: white;
        font-weight: bold;
        font-size: 0.9rem;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      }

      /* Tier фильтр в ряд в сайдбаре */
      .tier-filter-row {
        display: flex;
        gap: 0.3rem;
        padding: 0.5rem 1rem;
        justify-content: space-between;
        min-height: 50px;
      }

      .tier-btn-row {
        background:
          radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.08) 0.8px, transparent 0.8px),
          radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.06) 0.6px, transparent 0.6px),
          radial-gradient(circle at 40% 80%, rgba(0, 0, 0, 0.04) 0.7px, transparent 0.7px),
          radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
          radial-gradient(circle at 10% 90%, rgba(0, 0, 0, 0.06) 0.5px, transparent 0.5px),
          rgba(15, 23, 42, 0.8);
        background-size: 2.5px 2.5px, 2px 2px, 1.8px 1.8px, 3px 3px, 2.2px 2.2px, 100% 100%;
        border: 1px solid var(--tier-color);
        border-radius: 6px;
        color: #ffffff;
        cursor: pointer;
        padding: 0.4rem 0.3rem;
        font-size: 0.75rem;
        font-weight: 700;
        font-family: inherit;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        flex: 1;
        text-align: center;
        backdrop-filter: blur(8px);
        position: relative;
        overflow: hidden;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
        box-shadow:
          0 2px 4px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1),
          inset 0 -1px 0 rgba(0, 0, 0, 0.1);
      }

      .tier-btn-row:hover {
        transform: translateY(-1px);
        border-color: var(--tier-color);
        box-shadow:
          0 3px 8px rgba(0, 0, 0, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.15),
          inset 0 -1px 0 rgba(0, 0, 0, 0.15);
        filter: brightness(1.1) saturate(1.1);
      }

      .tier-btn-row.active {
        border-color: var(--tier-color);
        color: var(--tier-color);
        box-shadow:
          0 2px 6px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          inset 0 -1px 0 rgba(0, 0, 0, 0.1);
        background:
          radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.12) 0.8px, transparent 0.8px),
          radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.08) 0.6px, transparent 0.6px),
          radial-gradient(circle at 40% 80%, rgba(0, 0, 0, 0.06) 0.7px, transparent 0.7px),
          radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
          radial-gradient(circle at 10% 90%, rgba(0, 0, 0, 0.08) 0.5px, transparent 0.5px),
          rgba(15, 23, 42, 0.9);
        background-size: 2.5px 2.5px, 2px 2px, 1.8px 1.8px, 3px 3px, 2.2px 2.2px, 100% 100%;
      }

      .tier-separator {
        height: 1px;
        background: linear-gradient(90deg,
          transparent 0%,
          rgba(139, 92, 246, 0.3) 5%,
          rgba(139, 92, 246, 0.6) 50%,
          rgba(139, 92, 246, 0.3) 95%,
          transparent 100%
        );
        margin: 0.5rem 0.2rem;
        transition: max-height 0.3s ease, opacity 0.3s ease;
      }

      .tier-label-minimal {
        display: flex;
        align-items: center;
        gap: 0.4rem;
        white-space: nowrap;
      }

      .tier-icon {
        font-size: 0.85rem;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
      }

      .tier-text {
        font-size: 0.8rem;
        font-weight: 700;
        background: linear-gradient(135deg, #ffffff, #f1f5f9, #e2e8f0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        filter: contrast(1.2) brightness(1.1);
        letter-spacing: 0.2px;
      }

      .tier-buttons-minimal {
        display: flex;
        gap: 0.4rem;
        align-items: center;
        flex-wrap: wrap;
      }

      .tier-btn-minimal {
        background:
          radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.08) 0.8px, transparent 0.8px),
          radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.06) 0.6px, transparent 0.6px),
          radial-gradient(circle at 40% 80%, rgba(0, 0, 0, 0.04) 0.7px, transparent 0.7px),
          radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
          radial-gradient(circle at 10% 90%, rgba(0, 0, 0, 0.06) 0.5px, transparent 0.5px),
          rgba(15, 23, 42, 0.8);
        background-size: 2.5px 2.5px, 2px 2px, 1.8px 1.8px, 3px 3px, 2.2px 2.2px, 100% 100%;
        border: 1px solid var(--tier-color);
        border-radius: 6px;
        color: #ffffff;
        cursor: pointer;
        padding: 0.3rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 700;
        font-family: inherit;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        min-width: 28px;
        text-align: center;
        backdrop-filter: blur(8px);
        position: relative;
        overflow: hidden;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
        box-shadow:
          0 2px 4px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1),
          inset 0 -1px 0 rgba(0, 0, 0, 0.1);
      }

      .tier-btn-minimal::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--tier-color);
        opacity: 0;
        transition: opacity 0.25s ease;
        z-index: 0;
      }

      .tier-btn-minimal:hover::before {
        opacity: 0.08;
      }

      .tier-btn-minimal.active::before {
        opacity: 0.15;
      }

      .tier-btn-minimal:hover {
        transform: translateY(-1px);
        border-color: rgba(var(--tier-color-rgb, 71, 85, 105), 0.6);
        box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.15),
          0 0 0 1px rgba(var(--tier-color-rgb, 71, 85, 105), 0.2);
      }

      .tier-btn-minimal.active {
        border-color: var(--tier-color);
        color: var(--tier-color);
        box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.1),
          0 0 0 1px rgba(var(--tier-color-rgb, 71, 85, 105), 0.3);
      }

      .tier-btn-minimal.active:hover {
        box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.15),
          0 0 0 1px rgba(var(--tier-color-rgb, 71, 85, 105), 0.4);
      }

      .tier-reset-minimal {
        background: rgba(239, 68, 68, 0.08);
        border: 1.5px solid rgba(239, 68, 68, 0.25);
        border-radius: 10px;
        color: #f87171;
        cursor: pointer;
        padding: 0.6rem;
        font-family: inherit;
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(8px);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: auto;
        position: relative;
        overflow: hidden;
      }

      .tier-reset-minimal::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #ef4444;
        opacity: 0;
        transition: opacity 0.25s ease;
        z-index: 0;
      }

      .tier-reset-minimal:hover::before {
        opacity: 0.08;
      }

      .tier-reset-minimal:hover {
        transform: translateY(-1px);
        border-color: rgba(239, 68, 68, 0.4);
        box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.15),
          0 0 0 1px rgba(239, 68, 68, 0.2);
      }

      .tier-reset-minimal svg {
        position: relative;
        z-index: 1;
      }

      body:not([data-active-section="vehicles"]) #vehicles-section {
        display: none;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
      }

      body:not([data-active-section="vehicles"]) #tank-list {
        display: none;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
      }

      .bottom-elements {
        margin-top: auto;
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 0;
      }

      .loading-status {
        color: #ffffff;
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        text-align: center;
      }

      .loading-progress {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        font-weight: 600;
        margin: 8px 0 0 0;
        width: 100%;
        text-align: center;
      }

      .progress-container {
        width: 100%;
        height: 6px;
        background: rgba(139, 92, 246, 0.2);
        border-radius: 3px;
        margin: 15px 0 10px 0;
        overflow: hidden;
        position: relative;
      }

      .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #8b5cf6, #a855f7, #c084fc);
        border-radius: 3px;
        transition: width 0.3s ease;
        width: 0%;
        position: relative;
        overflow: hidden;
        display: block;
        visibility: visible;
        opacity: 1;
      }

      .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        animation: progressShine 1.5s infinite;
      }

      @keyframes progressShine {
        0% { left: -100%; }
        100% { left: 100%; }
      }

      .sidebar-menu-item {
        background: transparent;
        color: var(--text-secondary);
        border-radius: var(--border-radius);
        cursor: pointer;
        padding: 0.75rem 1rem;
        margin: 0.25rem 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      .sidebar-menu-item.active {
        background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
        color: #ffffff;
      }

      .sidebar-header-content {
        padding: 1.5rem 1rem;
        border-bottom: 2px solid rgba(255, 0, 100, 0.9);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        animation: rgbLineColor 4s linear infinite;
      }

      .ai-title-text {
        font-size: 1.1rem;
        font-weight: 600;
        color: #ffffff;
      }

      @keyframes rgbLineColor {
        0% { border-bottom-color: rgba(255, 0, 100, 0.9); }
        16.66% { border-bottom-color: rgba(255, 100, 0, 0.9); }
        33.33% { border-bottom-color: rgba(255, 255, 0, 0.9); }
        50% { border-bottom-color: rgba(0, 255, 100, 0.9); }
        66.66% { border-bottom-color: rgba(0, 100, 255, 0.9); }
        83.33% { border-bottom-color: rgba(100, 0, 255, 0.9); }
        100% { border-bottom-color: rgba(255, 0, 100, 0.9); }
      }

      @keyframes iconColorFlow1 {
        0%, 100% { stroke: #ff2db3; }
        25% { stroke: #ff2db3; }
        50% { stroke: #ffa020; }
        75% { stroke: #1eb83a; }
      }

      @keyframes iconColorFlow2 {
        0%, 100% { stroke: #ffa020; }
        25% { stroke: #1eb83a; }
        50% { stroke: #0a7fff; }
        75% { stroke: #ff2db3; }
      }

      @keyframes iconColorFlow3 {
        0%, 100% { stroke: #1eb83a; }
        25% { stroke: #0a7fff; }
        50% { stroke: #ff2db3; }
        75% { stroke: #ffa020; }
      }

      @keyframes iconColorFlow4 {
        0%, 100% { stroke: #0a7fff; }
        25% { stroke: #ff2db3; }
        50% { stroke: #ffa020; }
        75% { stroke: #1eb83a; }
      }

      .sidebar-logo-icon path:nth-child(1) {
        stroke: #ff2db3;
        animation: iconColorFlow1 4s ease-in-out infinite;
      }
      .sidebar-logo-icon path:nth-child(2) {
        stroke: #ffa020;
        animation: iconColorFlow2 4s ease-in-out infinite;
      }
      .sidebar-logo-icon path:nth-child(3) {
        stroke: #1eb83a;
        animation: iconColorFlow3 4s ease-in-out infinite;
      }
      .sidebar-logo-icon path:nth-child(4) {
        stroke: #0a7fff;
        animation: iconColorFlow4 4s ease-in-out infinite;
      }

      .flex { display: flex; }
      .items-center { align-items: center; }
      .justify-center { justify-content: center; }
      .w-full { width: 100%; }
      .h-full { height: 100%; }
      .min-h-screen { min-height: 100vh; }

      /* МЕДИА-ЗАПРОСЫ УПРАВЛЯЮТСЯ В scrollbar-new.css */

      .modern-spinner,
      .modern-spinner::before,
      .modern-spinner::after,
      .loader-spinner,
      .spinner,
      .loading-spinner,
      .animate-spin,
      .single-loader,
      .modern-loading-container,
      .simple-loading-container {
        display: none;
        animation: none;
        visibility: hidden;
        opacity: 0;
        pointer-events: none;
      }

      @keyframes spin { 0%, 100% { display: none; } }
      @keyframes rotate { 0%, 100% { display: none; } }
      @keyframes simpleRotate { 0%, 100% { display: none; } }
      @keyframes onlySpinner { 0%, 100% { display: none; } }
      @keyframes counterSpinner { 0%, 100% { display: none; } }
      @keyframes coinFlip { 0%, 100% { display: none; } }

      *[class*="spin"],
      *[class*="rotate"] {
        display: none;
        visibility: hidden;
        opacity: 0;
      }
    </style>

    <!-- Загрузка шрифта Inter для правильного отображения текста -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="modulepreload" href="src/main.js">
    <link rel="modulepreload" href="src/utils/ui.js">
    <!-- Favicon для всех браузеров с cache busting -->
    <link rel="icon" type="image/png" href="/src/assets/images/web/pie-chart.png?v=3" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico?v=3" />
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico?v=3" />
    <link rel="icon" type="image/png" sizes="32x32" href="/src/assets/images/web/pie-chart.png?v=3" />
    <link rel="icon" type="image/png" sizes="16x16" href="/src/assets/images/web/pie-chart.png?v=3" />
    <link rel="apple-touch-icon" sizes="180x180" href="/src/assets/images/web/pie-chart.png?v=3" />
    <meta name="msapplication-TileImage" content="/src/assets/images/web/pie-chart.png?v=3" />
    <meta name="msapplication-TileColor" content="#2a2155" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <script>
      // Проверяем загрузку favicon
      console.log('🎯 Favicon paths configured:');
      console.log('  - /favicon.ico (from public/)');
      console.log('  - /src/assets/images/web/pie-chart.png');
      console.log('  - /manifest.json (from public/)');

      // Принудительно обновляем favicon
      function updateFavicon() {
        const timestamp = Date.now();

        // Удаляем старые favicon ссылки
        const oldLinks = document.querySelectorAll('link[rel*="icon"]');
        oldLinks.forEach(link => link.remove());

        // Добавляем новые favicon ссылки с timestamp
        const faviconLinks = [
          { rel: 'icon', type: 'image/x-icon', href: `/favicon.ico?v=${timestamp}` },
          { rel: 'shortcut icon', type: 'image/x-icon', href: `/favicon.ico?v=${timestamp}` },
          { rel: 'icon', type: 'image/png', sizes: '32x32', href: `/src/assets/images/web/pie-chart.png?v=${timestamp}` },
          { rel: 'icon', type: 'image/png', sizes: '16x16', href: `/src/assets/images/web/pie-chart.png?v=${timestamp}` }
        ];

        faviconLinks.forEach(linkData => {
          const link = document.createElement('link');
          Object.assign(link, linkData);
          document.head.appendChild(link);
        });

        if (window.DEBUG_MODE) console.log('🔄 Favicon forcibly updated with timestamp:', timestamp);
      }

      // Обновляем favicon при загрузке
      document.addEventListener('DOMContentLoaded', updateFavicon);

      // Дополнительная попытка убрана для оптимизации

      // Проверяем доступность favicon
      fetch('/favicon.ico')
        .then(response => {
          // Favicon check completed
        })
        .catch(error => {
          // Favicon load error
        });
    </script>
    <link rel="preload" href="src/assets/styles/main.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="src/assets/styles/main.css"></noscript>
    <link rel="stylesheet" href="src/assets/styles/components/new-compare.css">



    <script>
      (function() {
        if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
          const extensionPatterns = ['runtime.lastError', 'message port closed', 'Extension context invalidated', 'Could not establish connection', 'chrome-extension://', 'moz-extension://', 'Receiving end does not exist', 'The message port closed before a response was received', 'Unchecked runtime.lastError'];
          const isExtensionError = (message) => typeof message === 'string' && extensionPatterns.some(pattern => message.toLowerCase().includes(pattern.toLowerCase()));
          const originalError = console.error;
          console.error = function(...args) { if (!(args[0] && isExtensionError(args[0]))) originalError.apply(console, args); };
          const originalWarn = console.warn;
          console.warn = function(...args) { if (!(args[0] && isExtensionError(args[0]))) originalWarn.apply(console, args); };
          window.addEventListener('error', function(event) { if (event.message && isExtensionError(event.message)) { event.preventDefault(); event.stopPropagation(); return false; } }, true);
          window.addEventListener('unhandledrejection', function(event) { if (event.reason && isExtensionError(event.reason.toString())) { event.preventDefault(); return false; } });
        }
      })();

      (function() {
        const criticalStyle = document.createElement('style');
        criticalStyle.textContent = `*:not(.loader):not(.loader::before):not(.loader::after),*::before:not(.loader::before),*::after:not(.loader::after){transition:none;animation:none;animation-duration:0s;animation-delay:0s;transform:none}.loader,.loader::before,.loader::after{animation-play-state:running !important;animation:inherit !important}html,body{margin:0;padding:0;box-sizing:border-box;background:#2a2155;background-image:radial-gradient(circle at 30% 20%,rgba(85,64,174,0.5) 0%,transparent 45%),radial-gradient(circle at 80% 80%,rgba(123,31,162,0.4) 0%,transparent 50%),radial-gradient(circle at 10% 60%,rgba(59,28,218,0.3) 0%,transparent 40%),radial-gradient(circle at 70% 30%,rgba(172,69,230,0.2) 0%,transparent 35%);background-attachment:fixed;overflow-x:hidden;color:white;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;opacity:1;visibility:visible}#app{opacity:1;visibility:visible;display:block;min-height:100vh}.sidebar{position:fixed;left:0;top:0;width:250px;height:100vh;background:rgba(42,33,85,0.95);backdrop-filter:blur(20px);border-right:1px solid rgba(139,92,246,0.3);z-index:1000;opacity:1;visibility:visible;display:block}.header-container,.nav-container{opacity:1;visibility:visible;display:block}`;
        document.head.appendChild(criticalStyle);

        const initDOM = () => {
          const elements = [
            { selector: '#app', styles: { opacity: '1', visibility: 'visible', display: 'block' }},
            { selector: '.main-content-container', styles: { opacity: '1', visibility: 'visible', display: 'block' }},
            { selector: '.sidebar', styles: { opacity: '1', visibility: 'visible', display: 'block' }}
          ];
          elements.forEach(({ selector, styles }) => {
            const el = document.querySelector(selector);
            if (el) Object.assign(el.style, styles);
          });
        };
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initDOM);
        } else {
          initDOM();
        }
        window.enableTransitions = () => criticalStyle.remove();
      })();

      (function() {
        const preInitDOM = () => {
          const body = document.body;
          if (body) {
            body.setAttribute('data-loading', 'true');
            body.style.overflow = 'hidden';
          }
          const containers = [
            { id: 'app', styles: { display: 'block', opacity: '1', visibility: 'visible' }},
            { class: 'main-content-container', styles: { display: 'block', opacity: '1', visibility: 'visible' }},
            { class: 'sidebar', styles: { display: 'block', opacity: '1', visibility: 'visible' }}
          ];
          containers.forEach(({ id, class: className, styles }) => {
            const selector = id ? `#${id}` : `.${className}`;
            const element = document.querySelector(selector);
            if (element) Object.assign(element.style, styles);
          });
          const activeMenuItem = localStorage.getItem('activeMenuItem') || 'overview';
          document.body.setAttribute('data-active-section', activeMenuItem);
        };
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', preInitDOM);
        } else {
          preInitDOM();
        }
      })();


    </script>


  </head>

  <body class="font-sans antialiased tracking-wide font-normal leading-normal overflow-x-hidden relative z-0 min-h-screen h-full transition-colors duration-300 ease-in-out p-5" style="background-color:#2a2155;background-image:radial-gradient(circle at 30% 20%,rgba(85,64,174,0.5) 0%,transparent 45%),radial-gradient(circle at 80% 80%,rgba(123,31,162,0.4) 0%,transparent 50%),radial-gradient(circle at 10% 60%,rgba(59,28,218,0.3) 0%,transparent 40%),radial-gradient(circle at 70% 30%,rgba(172,69,230,0.2) 0%,transparent 35%);background-attachment:fixed;visibility:hidden">
    <script>
      window.DEBUG_MODE = true; // ВКЛЮЧАЕМ ЛОГИ ДЛЯ РАЗРАБОТКИ
      window.originalConsoleLog = console.log;
      console.log = function(...args) {
        if (window.DEBUG_MODE || args[0]?.includes('🚨') || args[0]?.includes('⚠️')) {
          window.originalConsoleLog.apply(console, args);
        }
      };
      document.body.style.visibility = 'visible';
      document.body.style.opacity = '1';
    </script>
    <div id="bg-gradient-layer" class="fixed inset-0 -z-20 pointer-events-none dark:bg-gm-dark-gradient"></div>
    <div id="bg-pattern-layer" class="fixed inset-0 -z-10 bg-cover bg-center bg-fixed opacity-10 bg-bg-pattern-light dark:bg-bg-pattern-dark"></div>
    <div class="glow-effect-top-right hidden"></div>
    <div class="glow-effect-bottom-left hidden"></div>
    <div class="glow-effect-center hidden"></div>

    <div class="min-h-screen w-full relative z-10 transition-all duration-300 ease-linear">
      <div class="sidebar flex flex-col overflow-hidden transition-all duration-200 ease-linear isolate transform-gpu will-change-transform" style="position:fixed;top:0;left:0;bottom:0;width:250px;z-index:10">
        <div class="sidebar-header-content">
          <svg class="sidebar-logo-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 15h-6.5a2.5 2.5 0 1 1 0 -5h.5" />
            <path d="M15 12v6.5a2.5 2.5 0 1 1 -5 0v-.5" />
            <path d="M12 9h6.5a2.5 2.5 0 1 1 0 5h-.5" />
            <path d="M9 12v-6.5a2.5 2.5 0 0 1 5 0v.5" />
          </svg>
          <div class="ai-title-text">AI Efficiency</div>
        </div>

        <div class="sidebar-section">
          <div class="section-title">ANALYSIS</div>

          <div class="sidebar-menu-item" data-section="overview">
            <div class="menu-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
                <path d="M14 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
                <path d="M4 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
                <path d="M14 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
              </svg>
            </div>
            <div class="menu-text">Overview</div>
          </div>

          <div class="sidebar-menu-item" data-section="vehicles">
            <div class="menu-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                <path d="M2 12m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v0a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z" />
                <path d="M6 12l1 -5h5l3 5" />
                <path d="M21 9l-7.8 0" />
              </svg>
            </div>
            <div class="menu-text">Vehicles</div>
          </div>
          <!-- Tier Filter (простые кнопки в ряд) -->
          <div id="tier-filter-container" class="tier-filter-row"
               style="display: none; opacity: 0; visibility: hidden; pointer-events: none;">
            <button class="tier-btn-row active" data-tier="X" style="--tier-color: #f59e0b;">X</button>
            <button class="tier-btn-row" data-tier="IX" style="--tier-color: #ec4899;">IX</button>
            <button class="tier-btn-row" data-tier="VIII" style="--tier-color: #8b5cf6;">VIII</button>
            <button class="tier-btn-row" data-tier="VII" style="--tier-color: #3b82f6;">VII</button>
            <button class="tier-btn-row" data-tier="VI" style="--tier-color: #10b981;">VI</button>
          </div>

          <!-- Линия разделения -->
          <div id="tier-separator" class="tier-separator"
               style="display: none; opacity: 0; visibility: hidden; pointer-events: none;"></div>

          <!-- Flags section (visible only when Vehicles tab is active) -->
          <div
            id="flag-section"
            class="flag-section"
            style="max-height: 0; opacity: 0; overflow: hidden; visibility: hidden; pointer-events: none;"
          >
            <div id="nation-filter" class="flag-list">
              <button class="filter-item flag-item" data-country="ussr">
                <img src="src/assets/images/flags/ussr.webp" alt="USSR" /><span class="flag-name"
                  >USSR</span
                >
              </button>
              <button class="filter-item flag-item" data-country="germany">
                <img src="src/assets/images/flags/germany.webp" alt="Germany" /><span
                  class="flag-name"
                  >Germany</span
                >
              </button>
              <button class="filter-item flag-item" data-country="usa">
                <img src="src/assets/images/flags/usa.webp" alt="USA" /><span class="flag-name"
                  >USA</span
                >
              </button>
              <button class="filter-item flag-item" data-country="france">
                <img src="src/assets/images/flags/france.webp" alt="France" /><span
                  class="flag-name"
                  >France</span
                >
              </button>
              <button class="filter-item flag-item" data-country="uk">
                <img src="src/assets/images/flags/uk.webp" alt="UK" /><span class="flag-name"
                  >UK</span
                >
              </button>
              <button class="filter-item flag-item" data-country="czech">
                <img src="src/assets/images/flags/czech.webp" alt="Czech" /><span class="flag-name"
                  >Czech</span
                >
              </button>
              <button class="filter-item flag-item" data-country="china">
                <img src="src/assets/images/flags/china.webp" alt="China" /><span class="flag-name"
                  >China</span
                >
              </button>
              <button class="filter-item flag-item" data-country="japan">
                <img src="src/assets/images/flags/japan.webp" alt="Japan" /><span class="flag-name"
                  >Japan</span
                >
              </button>
              <button class="filter-item flag-item" data-country="poland">
                <img src="src/assets/images/flags/poland.webp" alt="Poland" /><span
                  class="flag-name"
                  >Poland</span
                >
              </button>
              <button class="filter-item flag-item" data-country="sweden">
                <img src="src/assets/images/flags/sweden.webp" alt="Sweden" /><span
                  class="flag-name"
                  >Sweden</span
                >
              </button>
              <button class="filter-item flag-item" data-country="italy">
                <img src="src/assets/images/flags/italy.webp" alt="Italy" /><span class="flag-name"
                  >Italy</span
                >
              </button>
              <button class="filter-item flag-item" data-country="international">
                <img src="src/assets/images/flags/intunion.webp" alt="Int'l Union" /><span
                  class="flag-name"
                  >Int'l Union</span
                >
              </button>
            </div>
            <div class="flag-divider"></div>
            <!-- Tank type filters -->
            <div id="type-filter" class="type-list">
              <button class="filter-item type-item" data-category="ЛТ">
                <img src="src/assets/images/upgrades/lightTank.png" alt="ЛТ" /><span
                  class="type-name"
                  >ЛТ</span
                >
              </button>
              <button class="filter-item type-item" data-category="СТ">
                <img src="src/assets/images/upgrades/mediumTank.png" alt="СТ" /><span
                  class="type-name"
                  >СТ</span
                >
              </button>
              <button class="filter-item type-item" data-category="ТТ">
                <img src="src/assets/images/upgrades/heavyTank.png" alt="ТТ" /><span
                  class="type-name"
                  >ТТ</span
                >
              </button>
              <button class="filter-item type-item" data-category="ПТ-САУ">
                <img src="src/assets/images/upgrades/AT-SPG.png" alt="ПТ-САУ" /><span
                  class="type-name"
                  >ПТ-САУ</span
                >
              </button>
              <button class="filter-item type-item" data-category="САУ">
                <img src="src/assets/images/upgrades/SPG.png" alt="САУ" /><span class="type-name"
                  >САУ</span
                >
              </button>
            </div>
          </div>

          <div class="sidebar-menu-item" data-section="compare">
            <div class="menu-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="1"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M6 6m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" />
                <path d="M18 18m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" />
                <path d="M11 6h5a2 2 0 0 1 2 2v8" />
                <path d="M14 9l-3 -3l3 -3" />
                <path d="M13 18h-5a2 2 0 0 1 -2 -2v-8" />
                <path d="M10 15l3 3l-3 3" />
              </svg>
            </div>
            <div class="menu-text">Compare</div>
          </div>

          <div class="sidebar-menu-item" data-section="tactics">
            <div class="menu-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="1"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M3 7l6 -3l6 3l6 -3v13l-6 3l-6 -3l-6 3z" />
                <path d="M9 4v13" />
                <path d="M15 7v13" />
                <path d="M7 10l2 -2" />
                <path d="M11 14l2 -2" />
                <path d="M17 12l2 -2" />
              </svg>
            </div>
            <div class="menu-text">Tactics</div>
          </div>

          <div class="sidebar-menu-item" data-section="settings">
            <div class="menu-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="1"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path
                  d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"
                />
                <path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0" />
              </svg>
            </div>
            <div class="menu-text">Settings</div>
          </div>
        </div>
      </div>

      <div class="main-content-container" role="main">
        <!-- Content starts directly without filter panel -->

        <!-- Секция обзора (Overview) -->
        <div id="overview-section" class="content-section">
          <!-- Hero Section -->
          <div class="overview-hero">
            <!-- Animated Background -->
            <div class="hero-background">
              <div class="hero-particles"></div>
              <div class="hero-gradient-orb hero-gradient-orb-1"></div>
              <div class="hero-gradient-orb hero-gradient-orb-2"></div>
              <div class="hero-gradient-orb hero-gradient-orb-3"></div>
            </div>

            <div class="hero-content">
              <div class="hero-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12 15h-6.5a2.5 2.5 0 1 1 0 -5h.5" />
                  <path d="M15 12v6.5a2.5 2.5 0 1 1 -5 0v-.5" />
                  <path d="M12 9h6.5a2.5 2.5 0 1 1 0 5h-.5" />
                  <path d="M9 12v-6.5a2.5 2.5 0 0 1 5 0v.5" />
                </svg>
              </div>
              <h1 class="hero-title">AI Efficiency</h1>
              <p class="hero-subtitle">Умный анализ Мир Танков на основе ИИ</p>
              <div class="hero-stats">
                <div class="hero-stat">
                  <span class="stat-number">113+</span>
                  <span class="stat-label">Танков</span>
                </div>
                <div class="hero-stat">
                  <span class="stat-number">12</span>
                  <span class="stat-label">Наций</span>
                </div>
                <div class="hero-stat">
                  <span class="stat-number">5</span>
                  <span class="stat-label">Типов</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="overview-actions">
            <div class="action-card" data-action="vehicles">
              <div class="action-icon emoji-icon">
                🛡️
              </div>
              <h3 class="action-title">Просмотр техники</h3>
              <p class="action-description">Изучите характеристики всех танков</p>
            </div>

            <div class="action-card" data-action="compare">
              <div class="action-icon emoji-icon">
                ⚖️
              </div>
              <h3 class="action-title">Сравнение</h3>
              <p class="action-description">Сравните характеристики танков</p>
            </div>

            <div class="action-card coming-soon" data-action="tier-list">
              <div class="action-icon emoji-icon">
                🏆
              </div>
              <h3 class="action-title">Tier List</h3>
              <p class="action-description">Рейтинг танков по эффективности</p>
              <div class="coming-soon-badge">Coming Soon</div>
            </div>
          </div>

          <!-- Features Grid -->
          <div class="overview-features">
            <div class="feature-card">
              <div class="feature-icon emoji-icon">
                🤖
              </div>
              <h3 class="feature-title">AI Анализ</h3>
              <p class="feature-description">Умный анализ эффективности танков с использованием машинного обучения</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon emoji-icon">
                📊
              </div>
              <h3 class="feature-title">Статистика</h3>
              <p class="feature-description">Подробная статистика и метрики производительности для каждого танка</p>
            </div>

            <div class="feature-card coming-soon">
              <div class="feature-icon emoji-icon">
                ⭐
              </div>
              <h3 class="feature-title">Рекомендации</h3>
              <p class="feature-description">Персонализированные рекомендации танков на основе вашего стиля игры</p>
              <div class="coming-soon-badge">Coming Soon</div>
            </div>

            <div class="feature-card coming-soon">
              <div class="feature-icon emoji-icon">
                🌐
              </div>
              <h3 class="feature-title">Сообщество</h3>
              <p class="feature-description">Делитесь билдами и стратегиями с другими игроками</p>
              <div class="coming-soon-badge">Coming Soon</div>
            </div>
          </div>


        </div>

        <!-- Список танков (Vehicles) -->
        <div id="vehicles-section" class="content-section hidden">
          <!-- Здесь можно добавить другие фильтры -->
          <div class="vehicles-filters">
          </div>

          <!-- Список танков -->
          <div id="tank-list" class="tank-list-grid" role="list" aria-label="Список танков"></div>
        </div>

        <!-- Страница характеристик танка -->
        <div
          id="tank-characteristics-container"
          class="content-section tank-characteristics hidden"
          style="display: none"
        >
          <!-- Содержимое будет добавлено через JavaScript -->
        </div>

        <!-- Секция сравнения (Compare) - НОВЫЙ ИНТЕРФЕЙС -->
        <div id="compare-section" class="content-section hidden" style="display: none">
          <div class="new-compare-container">
            <!-- Заголовок -->
            <!-- Заголовок и действия -->
            <div class="compare-header">
              <div class="compare-title-section">
                <h2 class="compare-title">Сравнение техники</h2>
                <p class="compare-subtitle">Добавляйте танки для сравнения характеристик</p>
              </div>

              <div class="compare-actions">
                <button id="clear-all-compare" class="compare-action-btn clear-btn">
                  <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9a1 1 0 00-1 1v3M4 7h16"/>
                  </svg>
                  <span>Очистить всё</span>
                </button>

                <button id="compare-btn" class="compare-action-btn compare-btn" disabled>
                  <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                  <span class="button-text">Выберите минимум 1 танк</span>
                </button>
              </div>
            </div>

            <!-- Таблица сравнения -->
            <div class="new-comparison-table" id="new-comparison-table">
              <!-- Таблица будет сгенерирована через JavaScript -->
            </div>
          </div>
        </div>

        <!-- Секция тактик (Tactics) -->
        <div id="tactics-section" class="content-section hidden" style="display: none">
          <div class="tactics-premium-container">
            <!-- Tactics Header -->
            <div class="tactics-premium-header">
              <div class="tactics-hero">
                <div class="tactics-hero-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 7l6 -3l6 3l6 -3v13l-6 3l-6 -3l-6 3z" />
                    <path d="M9 4v13" />
                    <path d="M15 7v13" />
                    <path d="M7 10l2 -2" />
                    <path d="M11 14l2 -2" />
                    <path d="M17 12l2 -2" />
                  </svg>
                </div>
                <h1 class="tactics-hero-title">Tactical Map Editor</h1>
                <p class="tactics-hero-subtitle">Создавайте профессиональные тактические схемы для World of Tanks</p>
              </div>
            </div>

            <!-- Main Interface Preview -->
            <div class="tactics-interface-preview">
              <!-- Left Panel Preview -->
              <div class="tactics-panel-preview">
                <div class="panel-header">
                  <h3>Панель инструментов</h3>
                </div>
                <div class="panel-tools">
                  <div class="tool-group">
                    <div class="tool-item">
                      <div class="tool-icon">🖊️</div>
                      <span>Рисование</span>
                    </div>
                    <div class="tool-item">
                      <div class="tool-icon">🛡️</div>
                      <span>Танки</span>
                    </div>
                    <div class="tool-item">
                      <div class="tool-icon">📍</div>
                      <span>Позиции</span>
                    </div>
                    <div class="tool-item">
                      <div class="tool-icon">💬</div>
                      <span>Комментарии</span>
                    </div>
                  </div>
                  <div class="tool-group">
                    <div class="tool-item">
                      <div class="tool-icon">👥</div>
                      <span>Участники</span>
                    </div>
                    <div class="tool-item">
                      <div class="tool-icon">🔗</div>
                      <span>Поделиться</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Map Preview -->
              <div class="tactics-map-preview">
                <div class="map-header">
                  <div class="map-title">Карта: Прохоровка</div>
                  <div class="map-controls">
                    <button class="map-btn">🔍 Масштаб</button>
                    <button class="map-btn">💾 Сохранить</button>
                    <button class="map-btn">📤 Экспорт</button>
                  </div>
                </div>
                <div class="map-canvas">
                  <div class="map-grid"></div>
                  <div class="map-overlay">
                    <div class="demo-tank tank-1">🟢</div>
                    <div class="demo-tank tank-2">🔴</div>
                    <div class="demo-arrow">➡️</div>
                    <div class="demo-position">📍</div>
                  </div>
                </div>
              </div>

              <!-- Coming Soon Overlay -->
              <div class="coming-soon-overlay">
                <div class="coming-soon-badge-corner">Coming Soon</div>
              </div>
            </div>

            <!-- Features Grid -->
            <div class="tactics-features-grid">
              <div class="feature-card-premium">
                <div class="feature-icon-premium">🗺️</div>
                <h3>Интерактивные карты</h3>
                <p>Все официальные карты WoT с высоким разрешением</p>
              </div>

              <div class="feature-card-premium">
                <div class="feature-icon-premium">🎨</div>
                <h3>Инструменты рисования</h3>
                <p>Стрелки, линии, фигуры и текстовые комментарии</p>
              </div>

              <div class="feature-card-premium">
                <div class="feature-icon-premium">👥</div>
                <h3>Совместная работа</h3>
                <p>Создавайте уникальные ссылки для командной работы</p>
              </div>

              <div class="feature-card-premium">
                <div class="feature-icon-premium">🔒</div>
                <h3>Управление доступом</h3>
                <p>Контролируйте права редактирования участников</p>
              </div>

              <div class="feature-card-premium">
                <div class="feature-icon-premium">💾</div>
                <h3>Сохранение и экспорт</h3>
                <p>Сохраняйте схемы и экспортируйте в различных форматах</p>
              </div>

              <div class="feature-card-premium">
                <div class="feature-icon-premium">⚡</div>
                <h3>Реальное время</h3>
                <p>Синхронизация изменений в реальном времени</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Секция настроек (Settings) -->
        <div id="settings-section" class="content-section hidden" style="display: none">
          <div class="settings-container">
            <!-- Settings Header -->
            <div class="settings-header">
              <div class="settings-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"/>
                  <path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"/>
                </svg>
              </div>
              <h1 class="settings-title">Настройки</h1>
              <p class="settings-subtitle">Персонализируйте ваш опыт использования AI Efficiency</p>
            </div>

            <!-- Settings Categories -->
            <div class="settings-categories">
              <!-- Appearance Settings -->
              <div class="settings-category">
                <div class="category-header">
                  <div class="category-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z"/>
                    </svg>
                  </div>
                  <h2 class="category-title">Внешний вид</h2>
                </div>

                <div class="settings-group">
                  <div class="setting-item">
                    <div class="setting-info">
                      <label class="setting-label">Тема интерфейса</label>
                      <p class="setting-description">Выберите светлую или темную тему</p>
                    </div>
                    <div class="setting-control">
                      <div class="custom-dropdown" data-dropdown="theme">
                        <div class="dropdown-selected" data-value="dark">
                          <span class="dropdown-text">Темная</span>
                          <svg class="dropdown-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                          </svg>
                        </div>
                        <div class="dropdown-options">
                          <div class="dropdown-option" data-value="dark">Темная</div>
                          <div class="dropdown-option" data-value="light">Светлая</div>
                          <div class="dropdown-option" data-value="auto">Автоматически</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="setting-item">
                    <div class="setting-info">
                      <label class="setting-label">Размер шрифта</label>
                      <p class="setting-description">Настройте размер текста для комфортного чтения</p>
                    </div>
                    <div class="setting-control">
                      <div class="custom-dropdown" data-dropdown="font-size">
                        <div class="dropdown-selected" data-value="medium">
                          <span class="dropdown-text">Средний</span>
                          <svg class="dropdown-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                          </svg>
                        </div>
                        <div class="dropdown-options">
                          <div class="dropdown-option" data-value="small">Мелкий</div>
                          <div class="dropdown-option" data-value="medium">Средний</div>
                          <div class="dropdown-option" data-value="large">Крупный</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="setting-item">
                    <div class="setting-info">
                      <label class="setting-label">Анимации</label>
                      <p class="setting-description">Включить плавные анимации интерфейса</p>
                    </div>
                    <div class="setting-control">
                      <div class="modern-toggle">
                        <input type="checkbox" id="animations-toggle" checked />
                        <span class="modern-toggle-slider"></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Language & Region -->
              <div class="settings-category">
                <div class="category-header">
                  <div class="category-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18z"/>
                      <path d="M2 12h20"/>
                      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
                    </svg>
                  </div>
                  <h2 class="category-title">Язык и регион</h2>
                </div>

                <div class="settings-group">
                  <div class="setting-item">
                    <div class="setting-info">
                      <label class="setting-label">Язык интерфейса</label>
                      <p class="setting-description">Выберите предпочитаемый язык</p>
                    </div>
                    <div class="setting-control">
                      <div class="custom-dropdown" data-dropdown="language">
                        <div class="dropdown-selected" data-value="ru">
                          <span class="dropdown-text">Русский</span>
                          <svg class="dropdown-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                          </svg>
                        </div>
                        <div class="dropdown-options">
                          <div class="dropdown-option" data-value="ru">Русский</div>
                          <div class="dropdown-option" data-value="en">English</div>
                          <div class="dropdown-option" data-value="de">Deutsch</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="setting-item">
                    <div class="setting-info">
                      <label class="setting-label">Регион сервера</label>
                      <p class="setting-description">Выберите ваш игровой регион</p>
                    </div>
                    <div class="setting-control">
                      <div class="custom-dropdown" data-dropdown="region">
                        <div class="dropdown-selected" data-value="ru">
                          <span class="dropdown-text">RU - Россия</span>
                          <svg class="dropdown-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                          </svg>
                        </div>
                        <div class="dropdown-options">
                          <div class="dropdown-option" data-value="ru">RU - Россия</div>
                          <div class="dropdown-option" data-value="eu">EU - Европа</div>
                          <div class="dropdown-option" data-value="na">NA - Северная Америка</div>
                          <div class="dropdown-option" data-value="asia">ASIA - Азия</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Performance Settings -->
              <div class="settings-category">
                <div class="category-header">
                  <div class="category-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16z"/>
                      <path d="M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
                      <path d="M12 2v2"/>
                      <path d="M12 20v2"/>
                      <path d="M4.93 4.93l1.41 1.41"/>
                      <path d="M17.66 17.66l1.41 1.41"/>
                      <path d="M2 12h2"/>
                      <path d="M20 12h2"/>
                      <path d="M6.34 17.66l-1.41 1.41"/>
                      <path d="M19.07 4.93l-1.41 1.41"/>
                    </svg>
                  </div>
                  <h2 class="category-title">Производительность</h2>
                </div>

                <div class="settings-group">
                  <div class="setting-item">
                    <div class="setting-info">
                      <label class="setting-label">Кэширование данных</label>
                      <p class="setting-description">Сохранять данные для быстрого доступа</p>
                    </div>
                    <div class="setting-control">
                      <div class="modern-toggle">
                        <input type="checkbox" id="cache-toggle" checked />
                        <span class="modern-toggle-slider"></span>
                      </div>
                    </div>
                  </div>

                  <div class="setting-item">
                    <div class="setting-info">
                      <label class="setting-label">Автоматическая оптимизация</label>
                      <p class="setting-description">Автоматически оптимизировать производительность</p>
                    </div>
                    <div class="setting-control">
                      <div class="modern-toggle">
                        <input type="checkbox" id="auto-optimize-toggle" checked />
                        <span class="modern-toggle-slider"></span>
                      </div>
                    </div>
                  </div>

                  <div class="setting-item">
                    <div class="setting-info">
                      <label class="setting-label">Звук уведомлений</label>
                      <p class="setting-description">Воспроизводить звуковые сигналы при показе уведомлений</p>
                    </div>
                    <div class="setting-control">
                      <div class="modern-toggle" data-setting="notificationSounds">
                        <input type="checkbox" id="notificationSounds-toggle" checked />
                        <span class="modern-toggle-slider"></span>
                      </div>
                    </div>
                  </div>

                  <div class="setting-item">
                    <div class="setting-info">
                      <label class="setting-label">Управление данными</label>
                      <p class="setting-description">Очистить кэш и сохраненные данные</p>
                    </div>
                    <div class="setting-control">
                      <button id="clear-cache-btn" class="modern-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M3 6h18"/>
                          <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                          <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                        </svg>
                        Очистить кэш
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Advanced Settings -->
              <div class="settings-category">
                <div class="category-header">
                  <div class="category-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 20h9"/>
                      <path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"/>
                    </svg>
                  </div>
                  <h2 class="category-title">Дополнительно</h2>
                </div>

                <div class="settings-group">
                  <div class="setting-item">
                    <div class="setting-info">
                      <label class="setting-label">Режим разработчика</label>
                      <p class="setting-description">Показывать дополнительную отладочную информацию</p>
                    </div>
                    <div class="setting-control">
                      <div class="modern-toggle">
                        <input type="checkbox" id="debug-toggle" />
                        <span class="modern-toggle-slider"></span>
                      </div>
                    </div>
                  </div>

                  <div class="setting-item">
                    <div class="setting-info">
                      <label class="setting-label">Экспериментальные функции</label>
                      <p class="setting-description">Включить новые функции в разработке</p>
                    </div>
                    <div class="setting-control">
                      <div class="modern-toggle">
                        <input type="checkbox" id="experimental-toggle" />
                        <span class="modern-toggle-slider"></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Settings Footer -->
            <div class="settings-footer">
              <div class="settings-info">
                <p class="version-info">AI Efficiency v2.1.0</p>
                <p class="build-info">Build: 2025.12.07</p>
              </div>
              <div class="settings-actions">
                <button class="settings-button secondary" id="reset-settings-btn">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                    <path d="M21 3v5h-5"/>
                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                    <path d="M3 21v-5h5"/>
                  </svg>
                  Сбросить настройки
                </button>
                <button class="settings-button primary" id="save-settings-btn" disabled>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                    <polyline points="17,21 17,13 7,13 7,21"/>
                    <polyline points="7,3 7,8 15,8"/>
                  </svg>
                  Сохранить изменения
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Конец .main-content-container -->
    </div>
    <!-- Конец .app-container -->

    <!-- Модальное окно в стиле Unity (скрыто по умолчанию) -->
    <div
      id="build-modal"
      class="modal hidden unity-modal"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <div id="modal-body" class="unity-modal-content">
        <!-- Контент будет добавлен через JavaScript -->
      </div>
    </div>

    <script type="module" src="src/main.js"></script>

    <!-- Оптимизированный скрипт для управления примечаниями -->
    <script>
      document.addEventListener('DOMContentLoaded', function () {
        // Функция для инициализации состояния примечаний
        function initializeNotesState() {
          // Находим секцию примечаний
          const notesSection = document.querySelector('.notes-section');
          const toggleIcon = document.getElementById('toggle-notes-icon');

          if (notesSection && toggleIcon) {
            // По умолчанию примечания ПОКАЗАНЫ
            notesSection.classList.remove('notes-hidden');
            toggleIcon.style.transform = 'rotate(0deg)';
            toggleIcon.style.stroke = '#ff3b30';
          }
        }

        // Запускаем инициализацию при загрузке страницы
        initializeNotesState();

        // Также запускаем при изменении хэша (переход к другому танку)
        window.addEventListener('hashchange', function () {
          // Даем время для загрузки контента
          setTimeout(initializeNotesState, 500);
        });
      });

      // 🚀 УЛЬТРА-ОПТИМИЗИРОВАННОЕ ВКЛЮЧЕНИЕ АНИМАЦИЙ ПОСЛЕ ПОЛНОЙ ЗАГРУЗКИ 🚀
      (function() {
        let animationsEnabled = false;

        const enableAnimations = () => {
          if (animationsEnabled) return;
          animationsEnabled = true;

          console.log('🎨 Enabling smooth animations after full load');

          // Создаем стиль для включения анимаций
          const animationStyle = document.createElement('style');
          animationStyle.id = 'enable-animations';
          animationStyle.textContent = `
            /* ВКЛЮЧАЕМ ПЛАВНЫЕ АНИМАЦИИ ПОСЛЕ ЗАГРУЗКИ */
            *, *::before, *::after {
              transition: all 0.2s ease;
              animation-duration: initial;
              animation-delay: initial;
            }

            /* СПЕЦИАЛЬНЫЕ АНИМАЦИИ ДЛЯ ИНТЕРАКТИВНЫХ ЭЛЕМЕНТОВ */
            .sidebar-menu-item {
              /* ИСПРАВЛЕНО: убираем transition для мгновенного переключения */
            }

            .tank-item {
              transition: transform 0.25s ease, box-shadow 0.25s ease;
            }

            .nav-button {
              transition: all 0.2s ease;
            }

            /* ЛОАДЕРЫ УДАЛЕНЫ */

            /* СОХРАНЯЕМ ТОЛЬКО ВАЖНЫЕ АНИМАЦИИ */
            .sidebar-header-content {
              animation: rgbLineColor 4s linear infinite !important;
            }

            .sidebar-logo-icon {
              animation: smoothRotate 8s linear infinite !important;
            }

            .sidebar-logo-icon path:nth-child(1) {
              animation: iconColorFlow1 4s ease-in-out infinite !important;
            }
            .sidebar-logo-icon path:nth-child(2) {
              animation: iconColorFlow2 4s ease-in-out infinite !important;
            }
            .sidebar-logo-icon path:nth-child(3) {
              animation: iconColorFlow3 4s ease-in-out infinite !important;
            }
            .sidebar-logo-icon path:nth-child(4) {
              animation: iconColorFlow4 4s ease-in-out infinite !important;
            }

            /* ВОССТАНАВЛИВАЕМ АНИМАЦИЮ ФИЛЬТРОВ */
            .flag-section {
              transition: max-height 0.3s ease, opacity 0.3s ease, transform 0.3s ease !important;
            }
          `;

          document.head.appendChild(animationStyle);

          // Убираем класс загрузки с body
          document.body.classList.add('animations-enabled');

          // Smooth animations enabled successfully
        };

        // Включаем анимации после полной загрузки приложения
        const checkAppReady = () => {
          // Проверяем что приложение полностью загружено
          if (window.stableOptimizer && window.stableOptimizer.isInitialized) {
            enableAnimations();
          } else {
            // Если приложение еще не готово, ждем
            setTimeout(checkAppReady, 100);
          }
        };

        // Запускаем проверку после загрузки DOM
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', () => {
            setTimeout(checkAppReady, 500); // Даем время на инициализацию
          });
        } else {
          setTimeout(checkAppReady, 500);
        }

        // Резервный таймер на случай если что-то пойдет не так
        setTimeout(enableAnimations, 2000);
      })();


    </script>

    <script>
      // ПРЕДОТВРАЩЕНИЕ МЕЛЬКАНИЯ - ПРАВИЛЬНОЕ ОТОБРАЖЕНИЕ СЕКЦИЙ ПРИ ЗАГРУЗКЕ
      (function() {
        const currentPath = window.location.pathname;

        // МГНОВЕННО добавляем класс для страниц танков
        if (currentPath.startsWith('/vehicles/')) {
          const mainContainer = document.querySelector('.main-content-container');
          if (mainContainer) {
            mainContainer.classList.add('tank-details-page');
            // ПРИНУДИТЕЛЬНО убираем отступы сразу при загрузке
            mainContainer.style.padding = '0';
            mainContainer.style.paddingTop = '0';
            console.log('🔧 Anti-flicker: Added tank-details-page class EARLY and removed padding');
          }

          // ТАКЖЕ добавляем класс к vehicles-section
          const vehiclesSection = document.getElementById('vehicles-section');
          if (vehiclesSection) {
            vehiclesSection.classList.add('tank-page');
            vehiclesSection.style.padding = '0';
            vehiclesSection.style.paddingTop = '0';
            console.log('🔧 Anti-flicker: Added tank-page class to vehicles-section');
          }
        }

        const overviewSection = document.getElementById('overview-section');
        const settingsSection = document.getElementById('settings-section');
        const vehiclesSection = document.getElementById('vehicles-section');
        const tankList = document.getElementById('tank-list');

        console.log('🔧 Anti-flicker: Current path:', currentPath);

        if (currentPath === '/vehicles') {
          // Если загружаем /vehicles (список танков) - показываем vehicles-section
          if (overviewSection) {
            overviewSection.style.display = 'none';
            overviewSection.style.visibility = 'hidden';
            overviewSection.classList.add('hidden');
            overviewSection.classList.remove('section-visible');
          }
          if (settingsSection) {
            settingsSection.style.display = 'none';
            settingsSection.style.visibility = 'hidden';
            settingsSection.classList.add('hidden');
            settingsSection.classList.remove('section-visible');
          }
          if (vehiclesSection) {
            vehiclesSection.style.display = 'block';
            vehiclesSection.style.visibility = 'visible';
            vehiclesSection.classList.remove('hidden');
            vehiclesSection.classList.add('section-visible');
            vehiclesSection.classList.remove('tank-page'); /* Убираем класс для списка танков */
          }

          // Убираем класс tank-details-page для списка танков
          const mainContainer = document.querySelector('.main-content-container');
          if (mainContainer) {
            mainContainer.classList.remove('tank-details-page');
          }

          // Показываем список танков
          if (tankList) {
            tankList.style.display = 'grid';
            tankList.style.visibility = 'visible';
            tankList.classList.remove('hidden');
            tankList.classList.add('section-visible');
          }

          // Показываем tier filter и separator для vehicles
          const tierFilter = document.getElementById('tier-filter-container');
          const tierSeparator = document.getElementById('tier-separator');
          if (tierFilter) {
            tierFilter.style.display = 'flex';
            tierFilter.style.opacity = '1';
            tierFilter.style.visibility = 'visible';
            tierFilter.style.pointerEvents = 'auto';
          }
          if (tierSeparator) {
            tierSeparator.style.display = 'block';
            tierSeparator.style.opacity = '1';
            tierSeparator.style.visibility = 'visible';
            tierSeparator.style.pointerEvents = 'auto';
          }

          console.log('🔧 Anti-flicker: Set vehicles list view');
        } else if (currentPath.startsWith('/vehicles/')) {
          // Если загружаем страницу конкретного танка - показываем vehicles-section БЕЗ отступов
          if (overviewSection) {
            overviewSection.style.display = 'none';
            overviewSection.style.visibility = 'hidden';
            overviewSection.classList.add('hidden');
            overviewSection.classList.remove('section-visible');
          }
          if (settingsSection) {
            settingsSection.style.display = 'none';
            settingsSection.style.visibility = 'hidden';
            settingsSection.classList.add('hidden');
            settingsSection.classList.remove('section-visible');
          }
          if (vehiclesSection) {
            vehiclesSection.style.display = 'block';
            vehiclesSection.style.visibility = 'visible';
            vehiclesSection.classList.remove('hidden');
            vehiclesSection.classList.add('section-visible');
            vehiclesSection.classList.add('tank-page'); /* Добавляем класс для убирания отступов */
          }

          // ДОБАВЛЯЕМ КЛАСС К MAIN-CONTENT-CONTAINER ДЛЯ УБИРАНИЯ ОТСТУПОВ
          const mainContainer = document.querySelector('.main-content-container');
          if (mainContainer) {
            mainContainer.classList.add('tank-details-page');
            // Принудительно убираем отступы для страниц танков
            mainContainer.style.padding = '0';
          }

          // ТАКЖЕ добавляем класс к vehicles-section
          const vehiclesSection = document.getElementById('vehicles-section');
          if (vehiclesSection) {
            vehiclesSection.classList.add('tank-page');
            vehiclesSection.style.padding = '0';
            vehiclesSection.style.paddingTop = '0';
          }

          // Для страниц танков скрываем список и tier элементы
          if (tankList) {
            tankList.style.display = 'none';
            tankList.style.visibility = 'hidden';
            tankList.classList.add('hidden');
            tankList.classList.remove('section-visible');
          }

          // Скрываем tier filter и separator для страниц танков
          const tierFilter = document.getElementById('tier-filter-container');
          const tierSeparator = document.getElementById('tier-separator');
          if (tierFilter) {
            tierFilter.style.display = 'none';
            tierFilter.style.opacity = '0';
            tierFilter.style.visibility = 'hidden';
            tierFilter.style.pointerEvents = 'none';
          }
          if (tierSeparator) {
            tierSeparator.style.display = 'none';
            tierSeparator.style.opacity = '0';
            tierSeparator.style.visibility = 'hidden';
            tierSeparator.style.pointerEvents = 'none';
          }

          console.log('🔧 Anti-flicker: Set tank details view');
        } else if (currentPath === '/settings') {
          // Если загружаем /settings - показываем settings
          if (overviewSection) {
            overviewSection.style.display = 'none';
            overviewSection.style.visibility = 'hidden';
            overviewSection.classList.add('hidden');
            overviewSection.classList.remove('section-visible');
          }
          if (settingsSection) {
            settingsSection.style.display = 'block';
            settingsSection.style.visibility = 'visible';
            settingsSection.classList.remove('hidden');
            settingsSection.classList.add('section-visible');
          }
          if (tankList) {
            tankList.style.display = 'none';
            tankList.style.visibility = 'hidden';
            tankList.classList.add('hidden');
            tankList.classList.remove('section-visible');
          }
          console.log('🔧 Anti-flicker: Set settings view');
        } else {
          // Если загружаем / или другие страницы - показываем vehicles (танки по умолчанию)
          if (overviewSection) {
            overviewSection.style.display = 'none';
            overviewSection.style.visibility = 'hidden';
            overviewSection.classList.add('hidden');
            overviewSection.classList.remove('section-visible');
          }
          if (settingsSection) {
            settingsSection.style.display = 'none';
            settingsSection.style.visibility = 'hidden';
            settingsSection.classList.add('hidden');
            settingsSection.classList.remove('section-visible');
          }
          if (vehiclesSection) {
            vehiclesSection.style.display = 'block';
            vehiclesSection.style.visibility = 'visible';
            vehiclesSection.classList.remove('hidden');
            vehiclesSection.classList.add('section-visible');
            vehiclesSection.classList.remove('tank-page'); /* Убираем класс для списка танков */
          }

          // Убираем класс tank-details-page для списка танков
          const mainContainer = document.querySelector('.main-content-container');
          if (mainContainer) {
            mainContainer.classList.remove('tank-details-page');
          }

          // Показываем список танков
          if (tankList) {
            tankList.style.display = 'grid';
            tankList.style.visibility = 'visible';
            tankList.classList.remove('hidden');
            tankList.classList.add('section-visible');
          }

          // Показываем tier filter и separator для vehicles
          const tierFilter = document.getElementById('tier-filter-container');
          const tierSeparator = document.getElementById('tier-separator');
          if (tierFilter) {
            tierFilter.style.display = 'flex';
            tierFilter.style.opacity = '1';
            tierFilter.style.visibility = 'visible';
            tierFilter.style.pointerEvents = 'auto';
          }
          if (tierSeparator) {
            tierSeparator.style.display = 'block';
            tierSeparator.style.opacity = '1';
            tierSeparator.style.visibility = 'visible';
            tierSeparator.style.pointerEvents = 'auto';
          }

          console.log('🔧 Anti-flicker: Set vehicles as default view');
        }
      })();
    </script>

    <!-- Новая система сравнения -->
    <script type="module" src="src/components/NewCompareSystem.js"></script>
  </body>
</html>
