/**
 * ПРОФЕССИОНАЛЬНАЯ АРХИТЕКТУРА СОСТОЯНИЯ
 * Harvard-level state management with validation and performance optimization
 */

// Константы для валидации
const VALID_COUNTRIES = ['all', 'ussr', 'germany', 'usa', 'france', 'uk', 'czech', 'china', 'japan', 'poland', 'sweden', 'italy', 'international'];
const VALID_CATEGORIES = ['all', 'ЛТ', 'СТ', 'ТТ', 'ПТ-САУ', 'САУ'];
const VALID_MENUS = ['overview', 'vehicles', 'compare', 'settings'];

// Валидатор состояния для предотвращения ошибок
const validateStateProperty = (key, value) => {
  switch (key) {
    case 'selectedCountry':
      return VALID_COUNTRIES.includes(value) ? value : 'all';
    case 'selectedCategory':
      return VALID_CATEGORIES.includes(value) ? value : 'all';
    case 'selectedCountries':
      return Array.isArray(value) ? value.filter(c => VALID_COUNTRIES.includes(c)) : [];
    case 'selectedCategories':
      return Array.isArray(value) ? value.filter(c => VALID_CATEGORIES.includes(c)) : [];
    case 'currentMenuName':
      return VALID_MENUS.includes(value) ? value : 'overview';
    case 'selectedTank':
      return value === null || (value && typeof value === 'object') ? value : null;
    default:
      return value;
  }
};

// УТИЛИТА ДЛЯ БЫСТРОГО ПОВЕРХНОСТНОГО СРАВНЕНИЯ ОБЪЕКТОВ
const shallowEqual = (obj1, obj2) => {
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (let key of keys1) {
    if (obj1[key] !== obj2[key]) {
      return false;
    }
  }

  return true;
};

// Создаем состояние с валидацией
const createValidatedState = () => ({
  // navigation & UI
  currentMenuName: 'overview',
  restoringTankFromReload: false,

  // filters & selection (с гарантированной валидностью)
  selectedTank: null,
  selectedCountry: 'all', // Для обратной совместимости
  selectedCategory: 'all', // Для обратной совместимости
  selectedCountries: [], // Новый массив для множественного выбора стран
  selectedCategories: [], // Новый массив для множественного выбора типов
  selectedTiers: ['X'], // Фильтр по уровням танков (по умолчанию только X)
  searchQuery: '',

  // filter tracking - отслеживание пользовательских фильтров
  countrySelectedManually: false,
  categorySelectedManually: false,

  // sorting state
  sortColumn: -1,
  sortDirection: 0,

  // loaded data
  allTanks: [],
  selectedEquipment: [null, null, null],
  selectedInstruction: null,

  // builds
  currentBuilds: {},
  defaultBuilds: {},

  // Метаданные для отладки и производительности
  _lastUpdate: Date.now(),
  _version: '2.1.0'
});

// УПРОЩЕННАЯ И ЭФФЕКТИВНАЯ РЕАКТИВНАЯ СИСТЕМА
const createStateProxy = (initialState) => {
  const listeners = new Map();
  const pendingUpdates = new Set(); // Используем Set для простоты
  let updateScheduled = false;

  // ОПТИМИЗИРОВАННЫЙ БАТЧИНГ - простой и эффективный
  const scheduleUpdate = (property) => {
    pendingUpdates.add(property);

    if (!updateScheduled) {
      updateScheduled = true;

      // Критические свойства обновляем через микротаск
      const isCritical = property === 'selectedTank' || property === 'currentMenuName';

      if (isCritical) {
        queueMicrotask(processPendingUpdates);
      } else {
        requestAnimationFrame(processPendingUpdates);
      }
    }
  };

  const processPendingUpdates = () => {
    if (pendingUpdates.size === 0) return;

    const propertiesToUpdate = Array.from(pendingUpdates);
    pendingUpdates.clear();
    updateScheduled = false;

    // Обрабатываем обновления в порядке приоритета
    const criticalProps = propertiesToUpdate.filter(p => p === 'selectedTank' || p === 'currentMenuName');
    const normalProps = propertiesToUpdate.filter(p => p !== 'selectedTank' && p !== 'currentMenuName');

    [...criticalProps, ...normalProps].forEach(property => {
      const propertyListeners = listeners.get(property);
      if (propertyListeners) {
        const value = proxy[property];
        propertyListeners.forEach(callback => {
          try {
            callback(value, property);
          } catch (error) {
            console.error(`[State] Listener error for ${property}:`, error);
          }
        });
      }
    });
  };

  const proxy = new Proxy(initialState, {
    set(target, property, value) {
      const oldValue = target[property];

      // Валидируем значение
      const validatedValue = validateStateProperty(property, value);

      // ОПТИМИЗИРОВАННОЕ СРАВНЕНИЕ: быстрая проверка равенства
      if (oldValue === validatedValue) {
        return true;
      }

      // Для объектов используем поверхностное сравнение (быстрее JSON.stringify)
      if (typeof oldValue === 'object' && typeof validatedValue === 'object' &&
          oldValue !== null && validatedValue !== null) {
        if (shallowEqual(oldValue, validatedValue)) {
          return true;
        }
      }

      // МИНИМАЛЬНОЕ ЛОГИРОВАНИЕ: только критические изменения в dev режиме
      if (__DEV__ && (property === 'selectedTank' || property === 'currentMenuName')) {
        console.log(`🔄 State.${property}:`, validatedValue?.name || validatedValue);
      }

      // Устанавливаем валидированное значение
      target[property] = validatedValue;
      target._lastUpdate = Date.now();

      // УПРОЩЕННОЕ УВЕДОМЛЕНИЕ: планируем обновление если есть слушатели
      if (listeners.has(property)) {
        scheduleUpdate(property);
      }

      return true;
    },

    get(target, property) {
      // Оптимизация доступа к часто используемым свойствам
      return target[property];
    }
  });

  // Методы для управления слушателями
  proxy.subscribe = (property, callback) => {
    if (!listeners.has(property)) {
      listeners.set(property, new Set());
    }
    listeners.get(property).add(callback);

    // Возвращаем функцию отписки
    return () => {
      const propertyListeners = listeners.get(property);
      if (propertyListeners) {
        propertyListeners.delete(callback);
        if (propertyListeners.size === 0) {
          listeners.delete(property);
        }
      }
    };
  };

  proxy.unsubscribe = (property, callback) => {
    const propertyListeners = listeners.get(property);
    if (propertyListeners) {
      propertyListeners.delete(callback);
      if (propertyListeners.size === 0) {
        listeners.delete(property);
      }
    }
  };

  // УПРОЩЕННЫЕ МЕТОДЫ УПРАВЛЕНИЯ
  proxy.flushUpdates = () => {
    if (updateScheduled) {
      processPendingUpdates();
    }
  };

  // Метод для массового обновления состояния (с батчингом)
  proxy.batchUpdate = (updates) => {
    const wasScheduled = updateScheduled;
    updateScheduled = true; // Предотвращаем промежуточные обновления

    Object.entries(updates).forEach(([key, value]) => {
      proxy[key] = value;
    });

    if (!wasScheduled) {
      updateScheduled = false;
      processPendingUpdates();
    }
  };

  // Метод для получения снимка состояния
  proxy.getSnapshot = () => ({ ...initialState });

  // Метод для сброса состояния
  proxy.reset = () => {
    const fresh = createValidatedState();
    Object.keys(target).forEach(key => {
      target[key] = fresh[key];
    });
    target._lastUpdate = Date.now();
  };

  // Метод для получения метрик производительности
  proxy.getMetrics = () => ({
    listenersCount: listeners.size,
    pendingUpdatesCount: pendingUpdates.size,
    lastUpdate: target._lastUpdate,
    version: target._version
  });

  return proxy;
};

export const state = createStateProxy(createValidatedState());

// УПРОЩЕННЫЕ УТИЛИТЫ ДЛЯ РАБОТЫ С СОСТОЯНИЕМ
export const stateUtils = {
  // Создание мемоизированного селектора
  createSelector: (selector, deps = []) => {
    let lastResult = null;
    let lastDeps = [];

    return () => {
      const currentDeps = deps.map(dep => state[dep]);
      const depsChanged = currentDeps.some((dep, i) => dep !== lastDeps[i]);

      if (depsChanged || lastResult === null) {
        lastResult = selector(state);
        lastDeps = currentDeps;
      }

      return lastResult;
    };
  },

  // Дебаунсированное обновление состояния
  debouncedUpdate: (() => {
    const timeouts = new Map();

    return (property, value, delay = 300) => {
      if (timeouts.has(property)) {
        clearTimeout(timeouts.get(property));
      }

      const timeout = setTimeout(() => {
        state[property] = value;
        timeouts.delete(property);
      }, delay);

      timeouts.set(property, timeout);
    };
  })(),

  // Простая функция для подписки на изменения
  watch: (property, callback) => state.subscribe(property, callback),

  // Получение текущего состояния
  getState: () => state.getSnapshot()
};

// Экспорт для обратной совместимости
export default state;

// Глобальный доступ для отладки
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.appState = state;
  window.stateUtils = stateUtils;
}

// ВАЖНО: Интеграция с унифицированным менеджером состояния
// Этот файл теперь является базой для UltraStateManager, избегая дублирования

// Temporary: expose for legacy global access while refactor is ongoing
if (typeof window !== 'undefined') {
  window.state = state;
  window.stateUtils = stateUtils;

  // Добавляем метку для отслеживания унификации
  window.STATE_UNIFIED = true;
}
