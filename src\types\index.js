/**
 * СИСТЕМА ТИПИЗАЦИИ ДЛЯ JAVASCRIPT
 * Обеспечивает проверку типов во время выполнения
 */

/**
 * БАЗОВЫЕ ТИПЫ
 */
export const Types = {
  STRING: 'string',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  OBJECT: 'object',
  ARRAY: 'array',
  FUNCTION: 'function',
  NULL: 'null',
  UNDEFINED: 'undefined'
};

/**
 * ПРОВЕРКА ТИПОВ
 */
export function isType(value, type) {
  switch (type) {
    case Types.STRING:
      return typeof value === 'string';
    case Types.NUMBER:
      return typeof value === 'number' && !isNaN(value);
    case Types.BOOLEAN:
      return typeof value === 'boolean';
    case Types.OBJECT:
      return value !== null && typeof value === 'object' && !Array.isArray(value);
    case Types.ARRAY:
      return Array.isArray(value);
    case Types.FUNCTION:
      return typeof value === 'function';
    case Types.NULL:
      return value === null;
    case Types.UNDEFINED:
      return value === undefined;
    default:
      return false;
  }
}

/**
 * ВАЛИДАЦИЯ ПАРАМЕТРОВ
 */
export function validateParams(params, schema) {
  const errors = [];
  
  for (const [key, expectedType] of Object.entries(schema)) {
    const value = params[key];
    
    if (expectedType.required && (value === undefined || value === null)) {
      errors.push(`Parameter '${key}' is required`);
      continue;
    }
    
    if (value !== undefined && value !== null) {
      const type = expectedType.type || expectedType;
      if (!isType(value, type)) {
        errors.push(`Parameter '${key}' must be of type ${type}, got ${typeof value}`);
      }
      
      // Дополнительные валидации
      if (expectedType.min !== undefined && value < expectedType.min) {
        errors.push(`Parameter '${key}' must be >= ${expectedType.min}`);
      }
      
      if (expectedType.max !== undefined && value > expectedType.max) {
        errors.push(`Parameter '${key}' must be <= ${expectedType.max}`);
      }
      
      if (expectedType.pattern && !expectedType.pattern.test(value)) {
        errors.push(`Parameter '${key}' does not match required pattern`);
      }
    }
  }
  
  return errors;
}

/**
 * ДЕКОРАТОР ДЛЯ ВАЛИДАЦИИ МЕТОДОВ
 */
export function validate(schema) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function(...args) {
      const params = {};
      const paramNames = getParameterNames(originalMethod);
      
      paramNames.forEach((name, index) => {
        params[name] = args[index];
      });
      
      const errors = validateParams(params, schema);
      if (errors.length > 0) {
        throw new Error(`Validation failed for ${propertyKey}: ${errors.join(', ')}`);
      }
      
      return originalMethod.apply(this, args);
    };
    
    return descriptor;
  };
}

/**
 * ПОЛУЧЕНИЕ ИМЕН ПАРАМЕТРОВ ФУНКЦИИ
 */
function getParameterNames(func) {
  const funcStr = func.toString();
  const match = funcStr.match(/\(([^)]*)\)/);
  if (!match || !match[1]) return [];
  
  return match[1]
    .split(',')
    .map(param => param.trim().split('=')[0].trim())
    .filter(param => param);
}

/**
 * ТИПЫ ДЛЯ ТАНКОВ
 */
export const TankTypes = {
  Tank: {
    name: { type: Types.STRING, required: true },
    country: { type: Types.STRING, required: true },
    type: { type: Types.STRING, required: true },
    tier: { type: Types.NUMBER, required: true, min: 1, max: 10 },
    characteristics: { type: Types.OBJECT, required: false }
  },
  
  TankFilter: {
    country: { type: Types.STRING, required: false },
    type: { type: Types.STRING, required: false },
    tier: { type: Types.ARRAY, required: false },
    searchQuery: { type: Types.STRING, required: false }
  },
  
  ComparisonSlot: {
    tank: { type: Types.OBJECT, required: false },
    index: { type: Types.NUMBER, required: true, min: 0, max: 7 }
  }
};

/**
 * ВАЛИДАТОРЫ ДЛЯ ТАНКОВ
 */
export function validateTank(tank) {
  return validateParams(tank, TankTypes.Tank);
}

export function validateTankFilter(filter) {
  return validateParams(filter, TankTypes.TankFilter);
}

export function validateComparisonSlot(slot) {
  return validateParams(slot, TankTypes.ComparisonSlot);
}

/**
 * СОЗДАНИЕ ТИПИЗИРОВАННЫХ ОБЪЕКТОВ
 */
export function createTank(data) {
  const errors = validateTank(data);
  if (errors.length > 0) {
    throw new Error(`Invalid tank data: ${errors.join(', ')}`);
  }
  
  return {
    ...data,
    id: data.id || generateTankId(data.name),
    __type: 'Tank'
  };
}

export function createTankFilter(data = {}) {
  const errors = validateTankFilter(data);
  if (errors.length > 0) {
    throw new Error(`Invalid filter data: ${errors.join(', ')}`);
  }
  
  return {
    country: 'all',
    type: 'all',
    tier: [],
    searchQuery: '',
    ...data,
    __type: 'TankFilter'
  };
}

/**
 * ПРОВЕРКА ТИПОВ ОБЪЕКТОВ
 */
export function isTank(obj) {
  return obj && obj.__type === 'Tank';
}

export function isTankFilter(obj) {
  return obj && obj.__type === 'TankFilter';
}

/**
 * УТИЛИТЫ
 */
function generateTankId(name) {
  return name.toLowerCase().replace(/[^a-z0-9]/g, '-');
}

/**
 * СИСТЕМА КОНТРАКТОВ
 */
export class Contract {
  static requires(condition, message) {
    if (!condition) {
      throw new Error(`Precondition failed: ${message}`);
    }
  }
  
  static ensures(condition, message) {
    if (!condition) {
      throw new Error(`Postcondition failed: ${message}`);
    }
  }
  
  static invariant(condition, message) {
    if (!condition) {
      throw new Error(`Invariant violated: ${message}`);
    }
  }
}

/**
 * ДЕКОРАТОР ДЛЯ КОНТРАКТОВ
 */
export function contract(preconditions = [], postconditions = []) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function(...args) {
      // Проверяем предусловия
      preconditions.forEach(condition => {
        if (typeof condition === 'function') {
          Contract.requires(condition.apply(this, args), `Precondition failed for ${propertyKey}`);
        }
      });
      
      const result = originalMethod.apply(this, args);
      
      // Проверяем постусловия
      postconditions.forEach(condition => {
        if (typeof condition === 'function') {
          Contract.ensures(condition.call(this, result, ...args), `Postcondition failed for ${propertyKey}`);
        }
      });
      
      return result;
    };
    
    return descriptor;
  };
}

// Основные функции уже экспортированы выше
