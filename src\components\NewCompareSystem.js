/**
 * НОВАЯ СИСТЕМА СРАВНЕНИЯ ТАНКОВ
 * Компактная вертикальная таблица характеристик
 */

import { BaseCompareSystem } from './base/BaseCompareSystem.js';

export class NewCompareSystem extends BaseCompareSystem {
  constructor() {
    super('newCompareSystemState', 12);
    this.init();
  }

  init() {
    this.restoreState();
    this.generateTable();
    this.setupEventListeners();
    this.syncWithOldSystem();
  }

  syncWithOldSystem() {
    if (window.advancedCompareSystem && window.advancedCompareSystem.tanks) {
      this.syncWithSystem(window.advancedCompareSystem);
      this.updateTable();
    }
  }

  generateTable() {
    const container = document.getElementById('new-comparison-table');
    if (!container) return;

    container.innerHTML = '';
    const table = this.createCompactTable();
    container.appendChild(table);

    // Обновляем счетчик танков
    this.updateTankCounter();
  }

  updateTankCounter() {
    const tankCount = this.tanks.filter(tank => tank !== null).length;
    const counter = document.getElementById('tank-count');
    if (counter) {
      counter.textContent = tankCount;
    }

    // Обновляем кнопку сравнения
    const compareBtn = document.getElementById('compare-btn');
    if (compareBtn) {
      const buttonText = compareBtn.querySelector('.button-text');
      if (tankCount === 0) {
        compareBtn.disabled = true;
        if (buttonText) buttonText.textContent = 'Выберите минимум 1 танк';
      } else {
        compareBtn.disabled = false;
        if (buttonText) buttonText.textContent = `Сравнить ${tankCount} ${this.getTankWord(tankCount)}`;
      }
    }

    // Обновляем кнопку очистки
    const clearBtn = document.getElementById('clear-all-compare');
    if (clearBtn) {
      clearBtn.style.opacity = tankCount > 0 ? '1' : '0.5';
      clearBtn.disabled = tankCount === 0;
    }
  }

  getTankWord(count) {
    if (count === 1) return 'танк';
    if (count >= 2 && count <= 4) return 'танка';
    return 'танков';
  }

  createCompactTable() {
    const container = document.createElement('div');
    container.className = 'permanent-comparison-table';

    // Создаем постоянную таблицу сравнения
    const table = this.createPermanentComparisonTable();
    container.appendChild(table);

    return container;
  }

  createPermanentComparisonTable() {
    const table = document.createElement('div');
    table.className = 'permanent-table';

    // Создаем заголовок таблицы
    const header = this.createPermanentTableHeader();
    table.appendChild(header);

    // Создаем секции категорий
    const categories = this.getDetailedCharacteristicsStructure();
    categories.forEach(category => {
      const categorySection = this.createPermanentCategorySection(category);
      table.appendChild(categorySection);
    });

    return table;
  }

  createPermanentTableHeader() {
    const header = document.createElement('div');
    header.className = 'permanent-table-header';

    // Колонка с названием характеристик
    const charColumn = document.createElement('div');
    charColumn.className = 'header-characteristics';
    charColumn.textContent = 'Характеристики';
    header.appendChild(charColumn);

    // 12 колонок для танков
    for (let i = 0; i < 12; i++) {
      const tankColumn = document.createElement('div');
      tankColumn.className = 'header-tank-slot';
      tankColumn.dataset.slotIndex = i;

      const tank = this.tanks[i];
      if (tank && tank !== null) {
        tankColumn.innerHTML = this.createTankHeaderContent(tank, i);
        tankColumn.classList.add('has-tank');
      } else {
        tankColumn.innerHTML = this.createEmptyTankSlot(i);
        tankColumn.classList.remove('has-tank');
      }

      header.appendChild(tankColumn);
    }

    return header;
  }

  createTankHeaderContent(tank, index) {
    return `
      <div class="tank-header-info">
        <img src="${this.getTankIconPath(tank.name)}" alt="${tank.name}" class="tank-icon" onerror="this.style.display='none'">
        <div class="tank-details">
          <div class="tank-name">${tank.name}</div>
          <div class="tank-meta">T${tank.tier} • ${tank.country}</div>
        </div>
        <button class="remove-tank-btn" onclick="window.newCompareSystem.removeTank(${index})" title="Удалить">×</button>
      </div>
    `;
  }

  createEmptyTankSlot(index) {
    return `
      <div class="empty-tank-slot" onclick="window.newCompareSystem.openTankSelector(${index})">
        <div class="empty-slot-icon">+</div>
        <div class="empty-slot-text">Слот ${index + 1}</div>
      </div>
    `;
  }

  createPermanentCategorySection(category) {
    const section = document.createElement('div');
    section.className = 'permanent-category-section';
    section.dataset.category = category.name;

    // Заголовок категории
    const header = document.createElement('div');
    header.className = 'permanent-category-header';
    header.style.borderLeft = `4px solid ${category.color}`;
    header.style.background = `linear-gradient(90deg, ${category.color}15, transparent)`;
    header.onclick = () => this.togglePermanentCategory(category.name);

    header.innerHTML = `
      <div class="category-header-content">
        <span class="category-name" style="color: ${category.color};">${category.displayName}</span>
        <span class="category-arrow" id="permanent-arrow-${category.name}">▼</span>
      </div>
      <div class="category-tank-headers">
        ${Array(12).fill(0).map(() => '<div class="category-tank-header"></div>').join('')}
      </div>
    `;
    section.appendChild(header);

    // Контент категории
    const content = document.createElement('div');
    content.className = 'permanent-category-content';
    content.id = `permanent-content-${category.name}`;
    content.style.display = category.name === 'Firepower' ? 'block' : 'none';

    // Создаем строки характеристик
    category.characteristics.forEach(char => {
      const row = this.createPermanentCharacteristicRow(char);
      content.appendChild(row);
    });

    section.appendChild(content);
    return section;
  }

  createPermanentCharacteristicRow(characteristic) {
    const row = document.createElement('div');
    row.className = `permanent-characteristic-row ${characteristic.indent ? 'indented' : ''}`;

    // Название характеристики
    const nameCell = document.createElement('div');
    nameCell.className = 'permanent-characteristic-name';
    if (characteristic.indent) {
      nameCell.innerHTML = `<span class="indent-marker">└</span> ${characteristic.name}`;
    } else {
      nameCell.textContent = characteristic.name;
    }
    row.appendChild(nameCell);

    // Собираем значения для сравнения
    const values = [];
    for (let i = 0; i < 12; i++) {
      const tank = this.tanks[i];
      if (tank && tank !== null) {
        const value = this.getTankCharacteristicValue(tank, characteristic.key);
        const numValue = parseFloat(value);
        values.push({ value, numValue, hasValue: !isNaN(numValue), slotIndex: i });
      } else {
        values.push({ value: '-', numValue: null, hasValue: false, slotIndex: i });
      }
    }

    // Определяем лучшие и худшие значения
    const validValues = values.filter(v => v.hasValue);
    let bestValue = null, worstValue = null;
    const isLowerBetter = this.isLowerBetter(characteristic.key);

    if (validValues.length > 1) {
      const numValues = validValues.map(v => v.numValue);
      if (isLowerBetter) {
        bestValue = Math.min(...numValues);
        worstValue = Math.max(...numValues);
      } else {
        bestValue = Math.max(...numValues);
        worstValue = Math.min(...numValues);
      }
    }

    // 8 ячеек для значений танков с цветовым выделением
    values.forEach(({ value, numValue, hasValue, slotIndex }) => {
      const valueCell = document.createElement('div');
      valueCell.className = 'permanent-characteristic-value';
      valueCell.dataset.slotIndex = slotIndex;
      valueCell.dataset.characteristicKey = characteristic.key;

      // Определяем класс для цветового выделения
      let className = 'equal';
      if (hasValue && bestValue !== null && worstValue !== null && bestValue !== worstValue) {
        if (numValue === bestValue) className = 'best';
        else if (numValue === worstValue) className = 'worst';
      }

      valueCell.classList.add(className);

      // Устанавливаем содержимое
      if (value && value !== '-') {
        const unit = characteristic.unit || '';
        valueCell.innerHTML = `${value} <span class="unit">${unit}</span>`;
      } else {
        valueCell.innerHTML = '-';
      }

      row.appendChild(valueCell);
    });

    return row;
  }

  togglePermanentCategory(categoryName) {
    const content = document.getElementById(`permanent-content-${categoryName}`);
    const arrow = document.getElementById(`permanent-arrow-${categoryName}`);

    if (content && arrow) {
      const isVisible = content.style.display === 'block';
      content.style.display = isVisible ? 'none' : 'block';
      arrow.textContent = isVisible ? '▶' : '▼';
    }
  }

  createComparisonTable() {
    const activeTanks = this.tanks.filter(tank => tank !== null);

    if (activeTanks.length === 0) {
      return this.createEmptyTableMessage();
    }

    const container = document.createElement('div');
    container.className = 'full-comparison-container';

    // Создаем заголовок таблицы с танками
    const header = this.createTableHeader(activeTanks);
    container.appendChild(header);

    // Создаем вкладки категорий
    const categories = this.getDetailedCharacteristicsStructure();
    categories.forEach(category => {
      const categorySection = this.createCategorySection(category, activeTanks);
      container.appendChild(categorySection);
    });

    return container;
  }

  createEmptyTableMessage() {
    const message = document.createElement('div');
    message.className = 'empty-table-message';
    message.innerHTML = `
      <p>Добавьте танки для отображения таблицы сравнения</p>
    `;
    return message;
  }

  createTableHeader(tanks) {
    const header = document.createElement('div');
    header.className = 'table-header';

    // Первая колонка - название характеристики
    const charColumn = document.createElement('div');
    charColumn.className = 'header-cell characteristic-header';
    charColumn.textContent = 'Характеристика';
    header.appendChild(charColumn);

    // Колонки для каждого танка
    tanks.forEach((tank, index) => {
      const tankColumn = document.createElement('div');
      tankColumn.className = 'header-cell tank-header';
      tankColumn.innerHTML = `
        <div class="tank-header-content">
          <img src="${this.getTankIconPath(tank.name)}" alt="${tank.name}" class="tank-header-icon" onerror="this.style.display='none'">
          <div class="tank-header-info">
            <div class="tank-header-name">${tank.name}</div>
            <div class="tank-header-details">T${tank.tier} • ${tank.country}</div>
          </div>
          <button class="tank-remove-btn" onclick="window.newCompareSystem.removeTank(${this.tanks.indexOf(tank)})" title="Удалить">×</button>
        </div>
      `;
      header.appendChild(tankColumn);
    });

    return header;
  }

  createCategorySection(category, tanks) {
    const section = document.createElement('div');
    section.className = 'category-section';
    section.dataset.category = category.name;

    // Заголовок категории (кликабельный)
    const header = document.createElement('div');
    header.className = 'category-header';
    header.style.borderLeft = `4px solid ${category.color}`;
    header.style.background = `linear-gradient(90deg, ${category.color}15, transparent)`;
    header.onclick = () => this.toggleCategory(category.name);

    header.innerHTML = `
      <div class="category-header-content">
        <span class="category-icon" style="color: ${category.color};">${category.icon}</span>
        <span class="category-name" style="color: ${category.color};">${category.displayName}</span>
        <span class="category-arrow" id="arrow-${category.name}">▼</span>
      </div>
    `;
    section.appendChild(header);

    // Контент категории (таблица характеристик)
    const content = document.createElement('div');
    content.className = 'category-content';
    content.id = `content-${category.name}`;
    content.style.display = category.name === 'Firepower' ? 'block' : 'none';

    const table = document.createElement('div');
    table.className = 'characteristics-table';

    // Создаем строки характеристик
    category.characteristics.forEach(char => {
      const row = this.createCharacteristicRow(char, tanks);
      table.appendChild(row);
    });

    content.appendChild(table);
    section.appendChild(content);

    return section;
  }

  toggleCategory(categoryName) {
    const content = document.getElementById(`content-${categoryName}`);
    const arrow = document.getElementById(`arrow-${categoryName}`);

    if (content && arrow) {
      const isVisible = content.style.display === 'block';
      content.style.display = isVisible ? 'none' : 'block';
      arrow.textContent = isVisible ? '▶' : '▼';
    }
  }

  createCharacteristicRow(characteristic, tanks) {
    const row = document.createElement('div');
    row.className = `characteristic-row ${characteristic.indent ? 'indented' : ''}`;

    // Название характеристики
    const nameCell = document.createElement('div');
    nameCell.className = 'characteristic-name-cell';
    if (characteristic.indent) {
      nameCell.innerHTML = `<span class="indent-marker">└</span> ${characteristic.name}`;
    } else {
      nameCell.textContent = characteristic.name;
    }
    row.appendChild(nameCell);

    // Значения для каждого танка
    tanks.forEach(tank => {
      const valueCell = document.createElement('div');
      valueCell.className = 'characteristic-value-cell';
      const value = this.getTankCharacteristicValue(tank, characteristic.key);
      const unit = characteristic.unit || '';
      valueCell.innerHTML = value ? `${value} <span class="unit">${unit}</span>` : '-';
      row.appendChild(valueCell);
    });

    return row;
  }

  getDetailedCharacteristicsStructure() {
    return [
      {
        name: 'Firepower',
        displayName: 'Firepower',
        icon: '🔥',
        color: '#ef4444',
        characteristics: [
          { name: 'Average Damage', key: 'damage', unit: 'HP' },
          { name: 'Penetration at 50m / 500m', key: 'penetration', unit: 'mm' },
          { name: 'Rate of Fire', key: 'rateOfFire', unit: 'rounds/min' },
          { name: 'Average Damage per Minute', key: 'dpm', unit: 'HP/min' },
          { name: 'Gun Loading', key: 'reload', unit: 's' },
          { name: 'Aiming Time', key: 'aimTime', unit: 's' },
          { name: 'At 100 m', key: 'dispersion100', unit: 'm', indent: true },
          { name: 'Moving', key: 'dispersionMoving', unit: 'm', indent: true },
          { name: 'Tank Traverse', key: 'dispersionTraverse', unit: 'm', indent: true },
          { name: 'Turret Traverse', key: 'dispersionTurret', unit: 'm', indent: true },
          { name: 'After Firing', key: 'dispersionFiring', unit: 'm', indent: true },
          { name: 'Damage to Modules', key: 'moduleDamage', unit: 'HP' },
          { name: 'Caliber', key: 'caliber', unit: 'mm' },
          { name: 'Shell Velocity', key: 'shellVelocity', unit: 'm/s' },
          { name: 'Shell Gravity', key: 'shellGravity', unit: 'm/s²' },
          { name: 'Max Range', key: 'maxRange', unit: 'm' }
        ]
      },
      {
        name: 'Survivability',
        displayName: 'Survivability',
        icon: '🛡️',
        color: '#3b82f6',
        characteristics: [
          { name: 'Hit Points', key: 'hp', unit: 'HP' },
          { name: 'Hull Armor', key: 'hullArmor', unit: 'mm' },
          { name: 'Turret Armor', key: 'turretArmor', unit: 'mm' },
          { name: 'Gun HP / Repaired', key: 'gunHP', unit: 'HP' },
          { name: 'Engine HP / Repaired', key: 'engineHP', unit: 'HP' },
          { name: 'Track HP / Repaired', key: 'trackHP', unit: 'HP' },
          { name: 'Ammo Rack HP / Repaired', key: 'ammoRackHP', unit: 'HP' },
          { name: 'Fuel Tank HP / Repaired', key: 'fuelTankHP', unit: 'HP' },
          { name: 'Suspension Repair Time', key: 'suspensionRepair', unit: 's' }
        ]
      },
      {
        name: 'Mobility',
        displayName: 'Mobility',
        icon: '⚡',
        color: '#f59e0b',
        characteristics: [
          { name: 'Weight', key: 'weight', unit: 't' },
          { name: 'Engine Power', key: 'enginePower', unit: 'h.p.' },
          { name: 'Specific Power', key: 'specificPower', unit: 'h.p./t' },
          { name: 'Max Speed', key: 'maxSpeed', unit: 'km/h' },
          { name: 'Reverse Speed', key: 'reverseSpeed', unit: 'km/h' },
          { name: 'Hull Traverse Speed', key: 'hullTraverse', unit: 'deg/s' },
          { name: 'Turret Traverse Speed', key: 'turretTraverse', unit: 'deg/s' },
          { name: 'Terrain Resistance', key: 'terrainResistance', unit: '' }
        ]
      },
      {
        name: 'Spotting',
        displayName: 'Spotting',
        icon: '👁️',
        color: '#10b981',
        characteristics: [
          { name: 'View Range', key: 'viewRange', unit: 'm' },
          { name: 'Signal Range', key: 'signalRange', unit: 'm' }
        ]
      },
      {
        name: 'Concealment',
        displayName: 'Concealment',
        icon: '🌿',
        color: '#8b5cf6',
        characteristics: [
          { name: 'Concealment of Stationary Vehicle', key: 'concealment', unit: '%' },
          { name: 'Concealment of Moving Vehicle', key: 'concealmentMoving', unit: '%' },
          { name: 'Concealment of Firing Vehicle', key: 'concealmentFiring', unit: '%' }
        ]
      },
      {
        name: 'Miscellaneous',
        displayName: 'Miscellaneous',
        icon: '⚙️',
        color: '#6b7280',
        characteristics: [
          { name: 'Battle Tiers', key: 'battleTiers', unit: '' },
          { name: 'Cost', key: 'cost', unit: 'credits' },
          { name: 'Repair Cost', key: 'repairCost', unit: 'credits' }
        ]
      }
    ];
  }

  getTankCharacteristicValue(tank, key) {
    // Заглушка для получения значений характеристик
    // В реальном приложении здесь будет логика получения данных из tank объекта
    const mockValues = {
      damage: Math.floor(Math.random() * 200) + 200,
      penetration: Math.floor(Math.random() * 100) + 150,
      rateOfFire: (Math.random() * 5 + 3).toFixed(1),
      dpm: Math.floor(Math.random() * 1000) + 1500,
      reload: (Math.random() * 5 + 8).toFixed(1),
      aimTime: (Math.random() * 1 + 2).toFixed(1),
      dispersion100: (Math.random() * 0.1 + 0.3).toFixed(2),
      dispersionMoving: (Math.random() * 0.1 + 0.15).toFixed(2),
      dispersionTraverse: (Math.random() * 0.1 + 0.1).toFixed(2),
      dispersionTurret: (Math.random() * 0.1 + 0.05).toFixed(2),
      dispersionFiring: (Math.random() * 2 + 2).toFixed(1),
      moduleDamage: Math.floor(Math.random() * 100) + 150,
      caliber: Math.floor(Math.random() * 50) + 75,
      shellVelocity: Math.floor(Math.random() * 500) + 800,
      shellGravity: (Math.random() * 2 + 8).toFixed(1),
      maxRange: Math.floor(Math.random() * 200) + 500,
      hp: Math.floor(Math.random() * 500) + 1000,
      hullArmor: Math.floor(Math.random() * 100) + 50,
      turretArmor: Math.floor(Math.random() * 150) + 100,
      gunHP: Math.floor(Math.random() * 50) + 150,
      engineHP: Math.floor(Math.random() * 100) + 200,
      trackHP: Math.floor(Math.random() * 150) + 250,
      ammoRackHP: Math.floor(Math.random() * 80) + 120,
      fuelTankHP: Math.floor(Math.random() * 60) + 100,
      suspensionRepair: (Math.random() * 5 + 10).toFixed(1),
      weight: Math.floor(Math.random() * 20) + 30,
      enginePower: Math.floor(Math.random() * 300) + 400,
      specificPower: (Math.random() * 10 + 10).toFixed(1),
      maxSpeed: Math.floor(Math.random() * 20) + 30,
      reverseSpeed: Math.floor(Math.random() * 10) + 10,
      hullTraverse: Math.floor(Math.random() * 20) + 25,
      turretTraverse: Math.floor(Math.random() * 15) + 20,
      terrainResistance: (Math.random() * 0.5 + 0.8).toFixed(1),
      viewRange: Math.floor(Math.random() * 100) + 350,
      signalRange: Math.floor(Math.random() * 200) + 400,
      concealment: (Math.random() * 10 + 5).toFixed(1),
      concealmentMoving: (Math.random() * 5 + 2).toFixed(1),
      concealmentFiring: (Math.random() * 2 + 1).toFixed(1),
      battleTiers: `${Math.floor(Math.random() * 3) + 8}-${Math.floor(Math.random() * 2) + 10}`,
      cost: Math.floor(Math.random() * 2000000) + 1000000,
      repairCost: Math.floor(Math.random() * 5000) + 3000
    };

    return mockValues[key] || '-';
  }

  generateCompactTable() {
    const container = document.getElementById('new-comparison-table');
    if (!container) return;

    container.innerHTML = '';
    const table = this.createCompactTable();
    container.appendChild(table);
  }

  updatePermanentTable() {
    // Обновляем заголовки танков
    this.updateTankHeaders();

    // Обновляем значения характеристик
    this.updateCharacteristicValues();
  }

  updateTankHeaders() {
    const headers = document.querySelectorAll('.header-tank-slot');
    headers.forEach((header, index) => {
      const tank = this.tanks[index];
      if (tank && tank !== null) {
        header.innerHTML = this.createTankHeaderContent(tank, index);
        header.classList.add('has-tank');
      } else {
        header.innerHTML = this.createEmptyTankSlot(index);
        header.classList.remove('has-tank');
      }
    });
  }

  updateCharacteristicValues() {
    // Группируем ячейки по характеристикам для сравнения
    const characteristicGroups = {};
    const valueCells = document.querySelectorAll('.permanent-characteristic-value');

    valueCells.forEach(cell => {
      const characteristicKey = cell.dataset.characteristicKey;
      if (!characteristicGroups[characteristicKey]) {
        characteristicGroups[characteristicKey] = [];
      }
      characteristicGroups[characteristicKey].push(cell);
    });

    // Обновляем каждую группу характеристик
    Object.keys(characteristicGroups).forEach(characteristicKey => {
      const cells = characteristicGroups[characteristicKey];
      const characteristic = this.findCharacteristicByKey(characteristicKey);
      const isLowerBetter = this.isLowerBetter(characteristicKey);

      // Собираем значения для сравнения
      const values = [];
      cells.forEach(cell => {
        const slotIndex = parseInt(cell.dataset.slotIndex);
        const tank = this.tanks[slotIndex];

        if (tank && tank !== null) {
          const value = this.getTankCharacteristicValue(tank, characteristicKey);
          const numValue = parseFloat(value);
          values.push({ cell, value, numValue, hasValue: !isNaN(numValue) });
        } else {
          values.push({ cell, value: '-', numValue: null, hasValue: false });
        }
      });

      // Находим лучшие и худшие значения
      const validValues = values.filter(v => v.hasValue);
      let bestValue = null, worstValue = null;

      if (validValues.length > 1) {
        const numValues = validValues.map(v => v.numValue);
        if (isLowerBetter) {
          bestValue = Math.min(...numValues);
          worstValue = Math.max(...numValues);
        } else {
          bestValue = Math.max(...numValues);
          worstValue = Math.min(...numValues);
        }
      }

      // Обновляем ячейки с цветовым выделением
      values.forEach(({ cell, value, numValue, hasValue }) => {
        const unit = characteristic ? characteristic.unit || '' : '';

        // Убираем старые классы
        cell.classList.remove('best', 'worst', 'equal');

        // Определяем класс для цветового выделения
        let className = 'equal';
        if (hasValue && bestValue !== null && worstValue !== null && bestValue !== worstValue) {
          if (numValue === bestValue) className = 'best';
          else if (numValue === worstValue) className = 'worst';
        }

        // Добавляем класс
        cell.classList.add(className);

        // Обновляем содержимое
        if (value && value !== '-') {
          cell.innerHTML = `${value} <span class="unit">${unit}</span>`;
        } else {
          cell.innerHTML = '-';
        }
      });
    });
  }

  /**
   * Определяет, лучше ли меньшее значение для данной характеристики
   */
  isLowerBetter(characteristicKey) {
    const lowerBetterCharacteristics = [
      'aimTime',        // Время сведения
      'accuracy',       // Разброс
      'reloadTime',     // Время перезарядки
      'repairCost',     // Стоимость ремонта
      'weight'          // Масса
    ];
    return lowerBetterCharacteristics.includes(characteristicKey);
  }

  findCharacteristicByKey(key) {
    const categories = this.getDetailedCharacteristicsStructure();
    for (const category of categories) {
      const characteristic = category.characteristics.find(char => char.key === key);
      if (characteristic) return characteristic;
    }
    return null;
  }



  createVerticalCharacteristicsList() {
    const list = document.createElement('div');
    list.className = 'vertical-characteristics-list';
    const categories = this.getCharacteristicsStructure();
    categories.forEach(category => {
      const categoryHeader = this.createVerticalCategoryHeader(category);
      list.appendChild(categoryHeader);
      const characteristicsContainer = this.createVerticalCharacteristicsContainer(category);
      list.appendChild(characteristicsContainer);
    });
    return list;
  }

  createVerticalCategoryHeader(category) {
    const header = document.createElement('div');
    header.className = 'vertical-category-header';
    header.dataset.category = category.name;

    // Применяем цвет категории
    header.style.borderLeft = `4px solid ${category.color}`;
    header.style.background = `linear-gradient(90deg, ${category.color}15, transparent)`;

    const isFirepower = category.name === 'Firepower';
    header.innerHTML = `
      <div class="vertical-category-content" onclick="window.newCompareSystem.toggleVerticalCategory('${category.name}')">
        <span class="category-icon" style="color: ${category.color};">${category.icon}</span>
        <span class="category-name" style="color: ${category.color};">${category.displayName}</span>
        <span class="category-arrow">${isFirepower ? '▼' : '▶'}</span>
      </div>
    `;
    return header;
  }

  createVerticalCharacteristicsContainer(category) {
    const container = document.createElement('div');
    container.className = 'vertical-characteristics-container';
    container.dataset.category = category.name;
    const isFirepower = category.name === 'Firepower';
    container.style.display = isFirepower ? 'block' : 'none';
    category.characteristics.forEach(char => {
      if (char.isGroup && char.subCharacteristics) {
        const groupRow = this.createVerticalGroupRow(char);
        container.appendChild(groupRow);
        char.subCharacteristics.forEach(subChar => {
          const subRow = this.createVerticalCharacteristicRow(subChar, true);
          container.appendChild(subRow);
        });
      } else {
        const charRow = this.createVerticalCharacteristicRow(char);
        container.appendChild(charRow);
      }
    });
    return container;
  }

  createVerticalGroupRow(group) {
    const row = document.createElement('div');
    row.className = 'vertical-group-row';
    const nameDiv = document.createElement('div');
    nameDiv.className = 'vertical-char-name group-name';
    nameDiv.innerHTML = `<em><strong>${group.name}</strong></em>`;
    row.appendChild(nameDiv);
    for (let i = 0; i < 12; i++) {
      const valueDiv = document.createElement('div');
      valueDiv.className = 'vertical-char-value';
      row.appendChild(valueDiv);
    }
    return row;
  }

  createVerticalCharacteristicRow(char, isIndented = false) {
    const row = document.createElement('div');
    row.className = 'vertical-characteristic-row';
    const nameDiv = document.createElement('div');
    nameDiv.className = `vertical-char-name ${isIndented ? 'indented' : ''}`;
    nameDiv.textContent = char.name;
    row.appendChild(nameDiv);
    for (let i = 0; i < 12; i++) {
      const valueDiv = document.createElement('div');
      valueDiv.className = 'vertical-char-value';
      const tank = this.tanks[i];
      if (tank) {
        const value = this.getCharacteristicValue(tank, char.key);
        valueDiv.textContent = value !== null ? `${value}${char.unit}` : '—';
      } else {
        valueDiv.textContent = '—';
      }
      row.appendChild(valueDiv);
    }
    return row;
  }

  toggleVerticalCategory(categoryName) {
    const container = document.querySelector(`[data-category="${categoryName}"].vertical-characteristics-container`);
    const arrow = document.querySelector(`[data-category="${categoryName}"] .category-arrow`);
    if (container && arrow) {
      const isVisible = container.style.display !== 'none';
      container.style.display = isVisible ? 'none' : 'block';
      arrow.textContent = isVisible ? '▶' : '▼';
    }
  }

  createTankHeaderContent(tank) {
    const tankIndex = this.tanks.findIndex(t => t && t.name === tank.name);
    return `
      <div class="tank-header-compact">
        <img src="${this.getTankIconPath(tank.name)}" alt="${tank.name}" class="tank-icon-mini" onerror="this.style.display='none'">
        <div class="tank-info-mini">
          <div class="tank-name-mini" title="${tank.name}">${tank.name.length > 10 ? tank.name.substring(0, 10) + '...' : tank.name}</div>
          <div class="tank-details-mini">
            <span class="tank-level-mini">T${tank.tier}</span>
          </div>
        </div>
        <button class="remove-tank-mini" onclick="event.stopPropagation(); window.newCompareSystem.removeTank(${tankIndex})" title="Удалить">×</button>
      </div>
    `;
  }

  createEmptyTankSlot(index) {
    return `
      <div class="empty-tank-slot">
        <div class="empty-slot-icon">+</div>
        <div class="empty-slot-text">Слот ${index + 1}</div>
      </div>
    `;
  }

  updateTable() {
    this.generateTable();
  }

  getCharacteristicsStructure() {
    return [
      {
        name: 'Firepower',
        displayName: 'Firepower',
        color: '#ef4444',
        icon: '🔥',
        characteristics: [
          { name: 'Average Damage', key: 'damage', unit: '' },
          { name: 'Penetration at 50m / 500m', key: 'penetration', unit: ' мм' },
          { name: 'Rate of Fire', key: 'rateOfFire', unit: ' выстр/мин' },
          { name: 'Average Damage per Minute', key: 'dpm', unit: '' },
          { name: 'Gun Loading', key: 'reloadTime', unit: ' с' },
          { name: 'Aiming Time', key: 'aimTime', unit: ' с' },
          {
            name: 'Gun Dispersions',
            key: 'dispersions',
            unit: '',
            isGroup: true,
            subCharacteristics: [
              { name: 'At 100 m', key: 'dispersion', unit: ' м' },
              { name: 'Moving', key: 'dispersionMoving', unit: ' м' },
              { name: 'Tank Traverse', key: 'dispersionTankTraverse', unit: ' м' },
              { name: 'Turret Traverse', key: 'dispersionTurretTraverse', unit: ' м' },
              { name: 'While Damaged', key: 'dispersionDamaged', unit: ' м' },
              { name: 'After Firing', key: 'dispersionAfterFiring', unit: ' м' }
            ]
          },
          { name: 'Damage to Modules', key: 'moduleDamage', unit: '' },
          { name: 'Caliber', key: 'caliber', unit: ' мм' },
          { name: 'Shell Velocity', key: 'shellVelocity', unit: ' м/с' },
          { name: 'Shell Gravity', key: 'shellGravity', unit: '' },
          { name: 'Max Range', key: 'maxRange', unit: ' м' }
        ]
      },
      {
        name: 'Survivability',
        displayName: 'Survivability',
        color: '#10b981',
        icon: '🛡️',
        characteristics: [
          { name: 'Hit Points', key: 'hitPoints', unit: ' HP' },
          { name: 'Hull Armor', key: 'hullArmor', unit: ' мм' },
          { name: 'Turret Armor', key: 'turretArmor', unit: ' мм' },
          { name: 'Gun HP / Repaired', key: 'gunHP', unit: ' HP' },
          { name: 'Engine HP / Repaired', key: 'engineHP', unit: ' HP' },
          { name: 'Track HP / Repaired', key: 'trackHP', unit: ' HP' },
          { name: 'Ammo Rack HP / Repaired', key: 'ammoRackHP', unit: ' HP' },
          { name: 'Fuel Tank HP / Repaired', key: 'fuelTankHP', unit: ' HP' },
          { name: 'Suspension Repair Time', key: 'suspensionRepairTime', unit: ' с' }
        ]
      },
      {
        name: 'Mobility',
        displayName: 'Mobility',
        color: '#f59e0b',
        icon: '⚡',
        characteristics: [
          { name: 'Total Weight', key: 'weight', unit: ' кг' },
          { name: 'Load Limit', key: 'loadLimit', unit: ' кг' },
          { name: 'Engine Power', key: 'enginePower', unit: ' л.с.' },
          { name: 'Specific Power', key: 'powerToWeight', unit: ' л.с./т' },
          { name: 'Top Speed', key: 'speed', unit: ' км/ч' },
          { name: 'Reverse Speed', key: 'reverseSpeed', unit: ' км/ч' },
          { name: 'Traverse Speed', key: 'traverse', unit: ' °/сек' },
          { name: 'Gun Traverse Speed', key: 'gunTraverseSpeed', unit: ' °/сек' },
          {
            name: 'Terrain (hard / medium / soft)',
            key: 'terrain',
            unit: '',
            isGroup: true,
            subCharacteristics: [
              { name: 'Resistance', key: 'terrainResistance', unit: '' },
              { name: 'Effective Speed', key: 'effectiveSpeed', unit: ' км/ч' },
              { name: 'Effective Traverse', key: 'effectiveTraverse', unit: ' °/сек' }
            ]
          },
          { name: 'Gun Depression / Elevation', key: 'gunDepression', unit: ' °' },
          { name: 'Gun Elevation Speed', key: 'gunElevationSpeed', unit: ' °/сек' },
          { name: 'Turret Traverse Angles', key: 'turretTraverseAngles', unit: ' °' }
        ]
      },
      {
        name: 'Spotting',
        displayName: 'Spotting',
        color: '#3b82f6',
        icon: '👁️',
        characteristics: [
          { name: 'View Range', key: 'viewRange', unit: ' м' },
          { name: 'Signal Range', key: 'radioRange', unit: ' м' }
        ]
      },
      {
        name: 'Concealment',
        displayName: 'Concealment',
        color: '#8b5cf6',
        icon: '🫥',
        characteristics: [
          { name: 'Stationary Vehicle', key: 'camouflageStationary', unit: '%' },
          { name: 'Moving Vehicle', key: 'camouflageMoving', unit: '%' }
        ]
      },
      {
        name: 'Miscellaneous',
        displayName: 'Miscellaneous',
        color: '#6b7280',
        icon: '⚙️',
        characteristics: [
          { name: 'Fire Chance', key: 'fireChance', unit: '%' },
          { name: 'Ammo Capacity', key: 'ammoCapacity', unit: '' },
          { name: 'Potential Damage', key: 'potentialDamage', unit: '' },
          { name: 'Shell Cost', key: 'shellCost', unit: ' кредитов' },
          { name: 'Shell Cost per 1000 HP', key: 'shellCostPer1000HP', unit: ' кредитов' },
          { name: 'Tank Cost', key: 'tankCost', unit: ' кредитов' }
        ]
      },
      {
        name: 'Marks of Excellence',
        displayName: 'Marks of Excellence',
        color: '#fbbf24',
        icon: '🏆',
        characteristics: [
          { name: '65%', key: 'moe65', unit: '' },
          { name: '85%', key: 'moe85', unit: '' },
          { name: '95%', key: 'moe95', unit: '' },
          { name: '100%', key: 'moe100', unit: '' }
        ]
      }
    ];
  }

  clearAll() {
    const tankCount = this.tanks.filter(tank => tank !== null).length;
    this.tanks.fill(null);
    this.updateTable();
    this.saveState();

    if (tankCount > 0 && window.notificationManager) {
      window.notificationManager.show(
        'Сравнение очищено',
        `Удалено ${tankCount} танков из сравнения`,
        'info'
      );
    }

    if (import.meta.env?.MODE === 'development') {
      console.log('🧹 All tanks cleared');
    }
  }

  getCharacteristicValue(tank, key) {
    // Проверяем в characteristics
    if (tank.characteristics && tank.characteristics[key] !== undefined) {
      return tank.characteristics[key];
    }

    // Проверяем в корне объекта танка
    if (tank[key] !== undefined) {
      return tank[key];
    }

    // Специальные случаи для вычисляемых значений
    switch (key) {
      case 'dpm':
        if (tank.damage && tank.rateOfFire) {
          return Math.round(tank.damage * tank.rateOfFire);
        }
        break;
      case 'powerToWeight':
        if (tank.enginePower && tank.weight) {
          return Math.round((tank.enginePower / (tank.weight / 1000)) * 10) / 10;
        }
        break;
      case 'penetration':
        if (tank.penetration) {
          return Array.isArray(tank.penetration) ? tank.penetration.join(' / ') : tank.penetration;
        }
        break;
    }

    return null;
  }

  getTankIconPath(tankName) {
    return `/src/assets/images/tanks/${tankName.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '')}.webp`;
  }

  openTankSelector(slotIndex = null) {
    if (window.onMenuSelected) {
      window.onMenuSelected('vehicles');
      this.selectedSlotIndex = slotIndex;
      if (window.notificationManager) {
        window.notificationManager.show(
          'Выберите танки',
          'Кликните на значок сравнения на карточках танков для добавления',
          'info'
        );
      }
    }
  }

  addTank(tank, slotIndex = null) {
    // Используем базовый метод добавления
    const success = super.addTank(tank);

    if (success) {
      // Обновляем постоянную таблицу
      this.updatePermanentTable();

      if (window.notificationManager) {
        window.notificationManager.show(
          'Танк добавлен',
          `${tank.name} добавлен в сравнение`,
          'success'
        );
      }
    } else {
      // Проверяем причину неудачи
      if (this.isTankAdded(tank.name)) {
        if (window.notificationManager) {
          window.notificationManager.show(
            'Танк уже добавлен',
            `${tank.name} уже есть в сравнении`,
            'warning'
          );
        }
      } else if (this.getActiveTankCount() >= this.maxTanks) {
        if (window.notificationManager) {
          window.notificationManager.show(
            'Нет свободных слотов',
            'Максимум 8 танков для сравнения',
            'warning'
          );
        }
      }
    }

    return success;
  }

  removeTank(slotIndex) {
    const tank = this.tanks[slotIndex];
    const success = super.removeTank(slotIndex);

    if (success) {
      // Обновляем постоянную таблицу
      this.updatePermanentTable();

      if (window.notificationManager && tank) {
        window.notificationManager.show(
          'Танк удален',
          `${tank.name} удален из сравнения`,
          'info'
        );
      }
    }

    return success;
  }

  setupEventListeners() {
    // Обработчик кнопки очистки
    const clearButton = document.getElementById('clear-all-compare');
    if (clearButton) {
      clearButton.addEventListener('click', () => {
        this.clearAll();
      });
    }

    // Обработчик для добавления танков из списка
    document.addEventListener('click', (event) => {
      const tankItem = event.target.closest('.tank-item');
      if (tankItem && window.state && window.state.activeMenu === 'compare') {
        const tankName = tankItem.dataset.tankName;
        if (tankName && window.tanksData) {
          const tank = this.findTankByName(tankName);
          if (tank) {
            this.addTank(tank);
          }
        }
      }
    });

    // Обработчик для кнопок сравнения на карточках танков
    document.addEventListener('click', (event) => {
      const compareBtn = event.target.closest('.compare-btn');
      if (compareBtn) {
        event.preventDefault();
        event.stopPropagation();

        const tankItem = compareBtn.closest('.tank-item');
        if (tankItem) {
          const tankName = tankItem.dataset.tankName;
          if (tankName && window.tanksData) {
            const tank = this.findTankByName(tankName);
            if (tank) {
              this.addTank(tank);

              // Переключаемся на вкладку Compare
              if (window.ui && window.ui.switchMenu) {
                window.ui.switchMenu('compare');
              }
            }
          }
        }
      }
    });
  }


}

window.newCompareSystem = new NewCompareSystem();