/**
 * АДАПТИВНАЯ ТАБЛИЦА СРАВНЕНИЯ ДЛЯ 8+ ТАНКОВ
 * Оптимизированная система отображения с горизонтальным скроллингом
 */

export class AdaptiveCompareTable {
  constructor(container, maxTanks = 12) {
    this.container = container;
    this.maxTanks = maxTanks;
    this.tanks = [];
    this.visibleColumns = 8; // Количество видимых колонок одновременно
    this.scrollPosition = 0;
    this.columnWidth = 140; // Ширина колонки в пикселях
    
    // Настройки виртуализации
    this.virtualization = {
      enabled: true,
      rowHeight: 45,
      visibleRows: 20,
      bufferRows: 5
    };
    
    // Кэш для оптимизации
    this.renderCache = new Map();
    this.scrollCache = new Map();
    
    this.initializeTable();
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ ТАБЛИЦЫ
   */
  initializeTable() {
    this.createTableStructure();
    this.setupScrollHandlers();
    this.setupResizeObserver();
    this.calculateVisibleColumns();
  }

  /**
   * СОЗДАНИЕ СТРУКТУРЫ ТАБЛИЦЫ
   */
  createTableStructure() {
    this.container.innerHTML = '';
    this.container.className = 'adaptive-compare-table';
    
    // Создаем контейнер с горизонтальным скроллом
    const scrollContainer = document.createElement('div');
    scrollContainer.className = 'table-scroll-container';
    
    // Создаем заголовок таблицы
    const header = this.createAdaptiveHeader();
    scrollContainer.appendChild(header);
    
    // Создаем тело таблицы
    const body = this.createAdaptiveBody();
    scrollContainer.appendChild(body);
    
    // Создаем индикатор скролла
    const scrollIndicator = this.createScrollIndicator();
    scrollContainer.appendChild(scrollIndicator);
    
    this.container.appendChild(scrollContainer);
    
    // Сохраняем ссылки на элементы
    this.scrollContainer = scrollContainer;
    this.headerElement = header;
    this.bodyElement = body;
    this.scrollIndicator = scrollIndicator;
  }

  /**
   * СОЗДАНИЕ АДАПТИВНОГО ЗАГОЛОВКА
   */
  createAdaptiveHeader() {
    const header = document.createElement('div');
    header.className = 'adaptive-table-header';
    
    // Фиксированная колонка характеристик
    const characteristicsColumn = document.createElement('div');
    characteristicsColumn.className = 'characteristics-column fixed';
    characteristicsColumn.innerHTML = `
      <div class="column-header">
        <span class="header-icon">📊</span>
        <span class="header-text">Characteristics</span>
      </div>
    `;
    header.appendChild(characteristicsColumn);
    
    // Скроллируемые колонки танков
    const tanksContainer = document.createElement('div');
    tanksContainer.className = 'tanks-columns-container';
    
    for (let i = 0; i < this.maxTanks; i++) {
      const tankColumn = this.createTankColumn(i);
      tanksContainer.appendChild(tankColumn);
    }
    
    header.appendChild(tanksContainer);
    
    return header;
  }

  /**
   * СОЗДАНИЕ КОЛОНКИ ТАНКА
   */
  createTankColumn(index) {
    const column = document.createElement('div');
    column.className = 'tank-column';
    column.dataset.tankIndex = index;
    column.style.width = `${this.columnWidth}px`;
    
    const tank = this.tanks[index];
    
    if (tank) {
      column.innerHTML = `
        <div class="tank-column-content filled">
          <div class="tank-avatar">
            <img src="${tank.icon}" alt="${tank.name}" class="tank-icon">
            <button class="remove-tank-btn" data-index="${index}">×</button>
          </div>
          <div class="tank-info">
            <div class="tank-name" title="${tank.name}">${tank.name}</div>
            <div class="tank-meta">${tank.tier} • ${tank.type}</div>
          </div>
        </div>
      `;
    } else {
      column.innerHTML = `
        <div class="tank-column-content empty" data-index="${index}">
          <div class="empty-slot">
            <div class="add-icon">+</div>
            <div class="add-text">Add Tank</div>
          </div>
        </div>
      `;
    }
    
    return column;
  }

  /**
   * СОЗДАНИЕ АДАПТИВНОГО ТЕЛА ТАБЛИЦЫ
   */
  createAdaptiveBody() {
    const body = document.createElement('div');
    body.className = 'adaptive-table-body';
    
    // Создаем виртуализированные строки
    const virtualContainer = document.createElement('div');
    virtualContainer.className = 'virtual-rows-container';
    
    const characteristics = this.getCharacteristics();
    
    characteristics.forEach((category, categoryIndex) => {
      const categorySection = this.createCategorySection(category, categoryIndex);
      virtualContainer.appendChild(categorySection);
    });
    
    body.appendChild(virtualContainer);
    
    return body;
  }

  /**
   * СОЗДАНИЕ СЕКЦИИ КАТЕГОРИИ
   */
  createCategorySection(category, categoryIndex) {
    const section = document.createElement('div');
    section.className = 'category-section';
    section.dataset.categoryIndex = categoryIndex;
    
    // Заголовок категории
    const categoryHeader = document.createElement('div');
    categoryHeader.className = 'category-header-row';
    
    // Фиксированная ячейка категории
    const categoryCell = document.createElement('div');
    categoryCell.className = 'category-cell fixed';
    categoryCell.innerHTML = `
      <span class="category-icon" style="color: ${category.color}">${category.icon}</span>
      <span class="category-name">${category.name}</span>
    `;
    categoryHeader.appendChild(categoryCell);
    
    // Пустые ячейки для танков
    const tankCells = document.createElement('div');
    tankCells.className = 'tank-cells-container';
    
    for (let i = 0; i < this.maxTanks; i++) {
      const cell = document.createElement('div');
      cell.className = 'tank-cell category-spacer';
      cell.style.width = `${this.columnWidth}px`;
      tankCells.appendChild(cell);
    }
    
    categoryHeader.appendChild(tankCells);
    section.appendChild(categoryHeader);
    
    // Строки характеристик
    category.characteristics.forEach((char, charIndex) => {
      const charRow = this.createCharacteristicRow(char, categoryIndex, charIndex);
      section.appendChild(charRow);
    });
    
    return section;
  }

  /**
   * СОЗДАНИЕ СТРОКИ ХАРАКТЕРИСТИКИ
   */
  createCharacteristicRow(characteristic, categoryIndex, charIndex) {
    const row = document.createElement('div');
    row.className = 'characteristic-row';
    row.dataset.categoryIndex = categoryIndex;
    row.dataset.charIndex = charIndex;
    
    // Фиксированная ячейка характеристики
    const charCell = document.createElement('div');
    charCell.className = 'characteristic-cell fixed';
    charCell.textContent = characteristic.name;
    row.appendChild(charCell);
    
    // Ячейки значений для танков
    const valuesContainer = document.createElement('div');
    valuesContainer.className = 'values-container';
    
    for (let i = 0; i < this.maxTanks; i++) {
      const valueCell = this.createValueCell(characteristic, i);
      valuesContainer.appendChild(valueCell);
    }
    
    row.appendChild(valuesContainer);
    
    return row;
  }

  /**
   * СОЗДАНИЕ ЯЧЕЙКИ ЗНАЧЕНИЯ
   */
  createValueCell(characteristic, tankIndex) {
    const cell = document.createElement('div');
    cell.className = 'value-cell';
    cell.style.width = `${this.columnWidth}px`;
    cell.dataset.tankIndex = tankIndex;
    
    const tank = this.tanks[tankIndex];
    
    if (tank) {
      const value = this.getCharacteristicValue(tank, characteristic.key);
      const isOptimal = this.isOptimalValue(characteristic.key, value, tankIndex);
      
      cell.innerHTML = `
        <span class="value ${isOptimal ? 'optimal' : ''}">${value}</span>
        <span class="unit">${characteristic.unit || ''}</span>
      `;
      
      if (isOptimal) {
        cell.classList.add('optimal');
      }
    } else {
      cell.innerHTML = '<span class="empty-value">—</span>';
      cell.classList.add('empty');
    }
    
    return cell;
  }

  /**
   * СОЗДАНИЕ ИНДИКАТОРА СКРОЛЛА
   */
  createScrollIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'scroll-indicator';
    
    indicator.innerHTML = `
      <div class="scroll-track">
        <div class="scroll-thumb"></div>
      </div>
      <div class="scroll-info">
        <span class="current-position">1</span>
        <span class="separator">of</span>
        <span class="total-columns">${this.maxTanks}</span>
      </div>
    `;
    
    return indicator;
  }

  /**
   * НАСТРОЙКА ОБРАБОТЧИКОВ СКРОЛЛА
   */
  setupScrollHandlers() {
    let isScrolling = false;
    
    // Горизонтальный скролл
    this.scrollContainer.addEventListener('scroll', (e) => {
      if (isScrolling) return;
      
      isScrolling = true;
      requestAnimationFrame(() => {
        this.handleHorizontalScroll(e.target.scrollLeft);
        isScrolling = false;
      });
    });
    
    // Скролл колесиком мыши
    this.scrollContainer.addEventListener('wheel', (e) => {
      if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
        return; // Горизонтальный скролл
      }
      
      e.preventDefault();
      const scrollAmount = e.deltaY > 0 ? this.columnWidth : -this.columnWidth;
      this.smoothScrollTo(this.scrollContainer.scrollLeft + scrollAmount);
    });
    
    // Обработчики кнопок
    this.container.addEventListener('click', (e) => {
      if (e.target.closest('.remove-tank-btn')) {
        const index = parseInt(e.target.dataset.index);
        this.removeTank(index);
      } else if (e.target.closest('.empty-slot')) {
        const index = parseInt(e.target.closest('.tank-column-content').dataset.index);
        this.openTankSelector(index);
      }
    });
  }

  /**
   * ОБРАБОТКА ГОРИЗОНТАЛЬНОГО СКРОЛЛА
   */
  handleHorizontalScroll(scrollLeft) {
    this.scrollPosition = scrollLeft;
    
    // Обновляем индикатор скролла
    this.updateScrollIndicator();
    
    // Виртуализация колонок (если нужно)
    if (this.maxTanks > 20) {
      this.virtualizeColumns();
    }
  }

  /**
   * ПЛАВНЫЙ СКРОЛЛ
   */
  smoothScrollTo(targetScrollLeft) {
    const startScrollLeft = this.scrollContainer.scrollLeft;
    const distance = targetScrollLeft - startScrollLeft;
    const duration = 300;
    let startTime = null;
    
    const animate = (currentTime) => {
      if (startTime === null) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      
      // Easing function
      const easeProgress = 1 - Math.pow(1 - progress, 3);
      
      this.scrollContainer.scrollLeft = startScrollLeft + (distance * easeProgress);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  }

  /**
   * ОБНОВЛЕНИЕ ИНДИКАТОРА СКРОЛЛА
   */
  updateScrollIndicator() {
    const thumb = this.scrollIndicator.querySelector('.scroll-thumb');
    const currentPos = this.scrollIndicator.querySelector('.current-position');
    
    const maxScroll = this.scrollContainer.scrollWidth - this.scrollContainer.clientWidth;
    const scrollPercent = maxScroll > 0 ? this.scrollPosition / maxScroll : 0;
    
    // Обновляем позицию ползунка
    thumb.style.left = `${scrollPercent * 100}%`;
    
    // Обновляем текущую позицию
    const currentColumn = Math.floor(this.scrollPosition / this.columnWidth) + 1;
    currentPos.textContent = Math.min(currentColumn, this.maxTanks);
  }

  /**
   * РАСЧЕТ ВИДИМЫХ КОЛОНОК
   */
  calculateVisibleColumns() {
    const containerWidth = this.container.clientWidth;
    const characteristicsWidth = 250; // Ширина колонки характеристик
    const availableWidth = containerWidth - characteristicsWidth;
    
    this.visibleColumns = Math.floor(availableWidth / this.columnWidth);
    this.visibleColumns = Math.max(1, Math.min(this.visibleColumns, this.maxTanks));
  }

  /**
   * НАСТРОЙКА OBSERVER ДЛЯ ИЗМЕНЕНИЯ РАЗМЕРА
   */
  setupResizeObserver() {
    if (window.ResizeObserver) {
      const resizeObserver = new ResizeObserver(() => {
        this.calculateVisibleColumns();
        this.updateLayout();
      });
      
      resizeObserver.observe(this.container);
    }
  }

  /**
   * ОБНОВЛЕНИЕ МАКЕТА
   */
  updateLayout() {
    // Пересчитываем размеры и позиции
    this.calculateVisibleColumns();
    this.updateScrollIndicator();
    
    // Обновляем стили для адаптивности
    const tanksContainer = this.headerElement.querySelector('.tanks-columns-container');
    if (tanksContainer) {
      tanksContainer.style.width = `${this.maxTanks * this.columnWidth}px`;
    }
  }

  /**
   * ПОЛУЧЕНИЕ ХАРАКТЕРИСТИК
   */
  getCharacteristics() {
    return [
      {
        name: 'Firepower',
        icon: '🔥',
        color: '#ef4444',
        characteristics: [
          { name: 'Alpha Damage', key: 'alphaDamage', unit: 'HP' },
          { name: 'DPM', key: 'dpm', unit: 'HP/min' },
          { name: 'Penetration', key: 'penetration', unit: 'mm' },
          { name: 'Accuracy', key: 'accuracy', unit: 'm' },
          { name: 'Aim Time', key: 'aimTime', unit: 's' },
          { name: 'Reload Time', key: 'reloadTime', unit: 's' }
        ]
      },
      {
        name: 'Mobility',
        icon: '⚡',
        color: '#3b82f6',
        characteristics: [
          { name: 'Top Speed', key: 'topSpeed', unit: 'km/h' },
          { name: 'Power/Weight', key: 'powerToWeight', unit: 'hp/t' },
          { name: 'Traverse Speed', key: 'traverseSpeed', unit: '°/s' },
          { name: 'Terrain Resistance', key: 'terrainResistance', unit: '' }
        ]
      },
      {
        name: 'Protection',
        icon: '🛡️',
        color: '#10b981',
        characteristics: [
          { name: 'Hit Points', key: 'hitPoints', unit: 'HP' },
          { name: 'Armor (Front)', key: 'armorFront', unit: 'mm' },
          { name: 'Armor (Side)', key: 'armorSide', unit: 'mm' },
          { name: 'Armor (Rear)', key: 'armorRear', unit: 'mm' }
        ]
      },
      {
        name: 'Detection',
        icon: '👁️',
        color: '#8b5cf6',
        characteristics: [
          { name: 'View Range', key: 'viewRange', unit: 'm' },
          { name: 'Concealment', key: 'concealment', unit: '%' },
          { name: 'Signal Range', key: 'signalRange', unit: 'm' }
        ]
      }
    ];
  }

  /**
   * ПОЛУЧЕНИЕ ЗНАЧЕНИЯ ХАРАКТЕРИСТИКИ
   */
  getCharacteristicValue(tank, key) {
    // Симуляция получения значений характеристик
    const mockValues = {
      alphaDamage: () => Math.floor(Math.random() * 400) + 200,
      dpm: () => Math.floor(Math.random() * 2000) + 1500,
      penetration: () => Math.floor(Math.random() * 100) + 200,
      accuracy: () => (Math.random() * 0.1 + 0.3).toFixed(3),
      aimTime: () => (Math.random() * 1 + 1.5).toFixed(1),
      reloadTime: () => (Math.random() * 5 + 8).toFixed(1),
      topSpeed: () => Math.floor(Math.random() * 30) + 40,
      powerToWeight: () => (Math.random() * 10 + 15).toFixed(1),
      traverseSpeed: () => Math.floor(Math.random() * 20) + 25,
      terrainResistance: () => (Math.random() * 0.5 + 0.8).toFixed(2),
      hitPoints: () => Math.floor(Math.random() * 800) + 1200,
      armorFront: () => Math.floor(Math.random() * 150) + 100,
      armorSide: () => Math.floor(Math.random() * 80) + 60,
      armorRear: () => Math.floor(Math.random() * 40) + 30,
      viewRange: () => Math.floor(Math.random() * 50) + 380,
      concealment: () => Math.floor(Math.random() * 20) + 10,
      signalRange: () => Math.floor(Math.random() * 200) + 400
    };
    
    return mockValues[key] ? mockValues[key]() : '—';
  }

  /**
   * ПРОВЕРКА ОПТИМАЛЬНОГО ЗНАЧЕНИЯ
   */
  isOptimalValue(key, value, tankIndex) {
    const values = this.tanks.map((tank, index) => 
      tank ? this.getCharacteristicValue(tank, key) : null
    ).filter(v => v !== null);
    
    if (values.length <= 1) return false;
    
    // Для большинства характеристик больше = лучше
    const higherIsBetter = [
      'alphaDamage', 'dpm', 'penetration', 'topSpeed', 'powerToWeight',
      'traverseSpeed', 'hitPoints', 'armorFront', 'armorSide', 'armorRear',
      'viewRange', 'concealment', 'signalRange'
    ];
    
    if (higherIsBetter.includes(key)) {
      return value === Math.max(...values.map(v => parseFloat(v)));
    } else {
      return value === Math.min(...values.map(v => parseFloat(v)));
    }
  }

  /**
   * ДОБАВЛЕНИЕ ТАНКА
   */
  addTank(tank, index = null) {
    if (index !== null && index >= 0 && index < this.maxTanks) {
      this.tanks[index] = tank;
    } else {
      const freeIndex = this.tanks.findIndex(t => !t);
      if (freeIndex !== -1) {
        this.tanks[freeIndex] = tank;
      }
    }
    
    this.updateTable();
  }

  /**
   * УДАЛЕНИЕ ТАНКА
   */
  removeTank(index) {
    if (index >= 0 && index < this.maxTanks) {
      this.tanks[index] = null;
      this.updateTable();
    }
  }

  /**
   * ОБНОВЛЕНИЕ ТАБЛИЦЫ
   */
  updateTable() {
    // Обновляем заголовки танков
    const tankColumns = this.headerElement.querySelectorAll('.tank-column');
    tankColumns.forEach((column, index) => {
      const newColumn = this.createTankColumn(index);
      column.replaceWith(newColumn);
    });
    
    // Обновляем значения характеристик
    const valuesCells = this.bodyElement.querySelectorAll('.value-cell');
    valuesCells.forEach(cell => {
      const tankIndex = parseInt(cell.dataset.tankIndex);
      const row = cell.closest('.characteristic-row');
      const categoryIndex = parseInt(row.dataset.categoryIndex);
      const charIndex = parseInt(row.dataset.charIndex);
      
      const characteristics = this.getCharacteristics();
      const characteristic = characteristics[categoryIndex].characteristics[charIndex];
      
      const newCell = this.createValueCell(characteristic, tankIndex);
      cell.replaceWith(newCell);
    });
    
    // Обновляем индикатор
    this.updateScrollIndicator();
  }

  /**
   * ОТКРЫТИЕ СЕЛЕКТОРА ТАНКОВ
   */
  openTankSelector(index) {
    // Интеграция с существующим селектором
    if (window.ui && window.ui.switchMenu) {
      window.ui.switchMenu('vehicles');
      
      // Сохраняем целевой индекс
      this.targetIndex = index;
    }
  }

  /**
   * УСТАНОВКА ТАНКОВ
   */
  setTanks(tanks) {
    this.tanks = tanks.slice(0, this.maxTanks);
    while (this.tanks.length < this.maxTanks) {
      this.tanks.push(null);
    }
    this.updateTable();
  }

  /**
   * ПОЛУЧЕНИЕ ТАНКОВ
   */
  getTanks() {
    return this.tanks.filter(tank => tank !== null);
  }
}
