/* Tank Details Styles - OPTIMIZED (Only unique styles) */

/* Container positioning and layout - КНОПКА У КРАЯ */
#tank-characteristics-container {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0 1rem 1rem 1rem; /* ИСПРАВЛЕНО: убираем верхний отступ для кнопки у края */
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  border: none;
  backdrop-filter: none;
  overflow: visible;
  box-sizing: border-box;
  position: relative;
  z-index: auto;
  opacity: 0;
  transition: opacity 0.3s ease;
  top: 0;
}

#tank-characteristics-container:not(.hidden) {
  opacity: 1;
}

/* Медиа-запросы - КНОПКА У КРАЯ */
@media (min-width: 768px) {
  #tank-characteristics-container {
    padding: 0 1.5rem 1.5rem 1.5rem; /* ИСПРАВЛЕНО: убираем верхний отступ */
  }
}

@media (max-width: 768px) {
  #tank-characteristics-container {
    padding: 0 0.5rem 0.5rem 0.5rem; /* ИСПРАВЛЕНО: убираем верхний отступ */
  }
}

/* Unique styles only - duplicates removed and moved to tank-characteristics.css */



/* Responsive - Mobile only (desktop styles moved to tank-characteristics.css) */
@media (max-width: 768px) {
  #tank-characteristics-container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0 0.5rem 0.5rem 0.5rem; /* ИСПРАВЛЕНО: убираем верхний отступ */
    overflow: visible;
    position: relative;
    top: 0;
  }
}
