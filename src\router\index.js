/**
 * ОПТИМИЗИРОВАННЫЙ РОУТЕР
 * Упрощенная система роутинга с кэшированием и защитой от дублирования
 */

import { updateFilterSelection } from '../components/tank-list/index.js';
import { unifiedStateManager } from '../store/UltraStateManager.js';
import { cacheManager } from '../utils/CacheMigrationAdapter.js';

// Защита от дублирующих вызовов
let lastRouterRestoreTime = 0;
let isNavigating = false;

// Упрощенный кэш роутинга
const routeCache = new Map();
const MAX_CACHE_SIZE = 20;
const CACHE_TTL = 30000; // 30 секунд

// Получаем состояние из унифицированного менеджера
const state = unifiedStateManager.state;

/**
 * УПРОЩЕННАЯ ИНИЦИАЛИЗАЦИЯ РОУТЕРА
 */
export function initRouter({
  applyFiltersAndRenderTankList
} = {}) {
  console.log('🔧 Optimized router initialization started');

  // Упрощенная функция применения фильтров
  const getApplyFiltersFunction = () => {
    if (applyFiltersAndRenderTankList) {
      return applyFiltersAndRenderTankList;
    }

    // Ленивая загрузка TankManager
    return async (preserveSelection = false) => {
      try {
        if (window.tankManager) {
          window.tankManager.applyFiltersAndRenderTankList(preserveSelection);
        } else {
          const { TankManager } = await import('../core/TankManager.js');
          const tankManager = new TankManager();
          tankManager.applyFiltersAndRenderTankList(preserveSelection);
        }
      } catch (error) {
        console.error('Failed to apply filters:', error);
      }
    };
  };

  // Восстанавливаем сохраненное состояние
  loadSavedState(getApplyFiltersFunction());

  // Оптимизированный обработчик popstate
  window.addEventListener('popstate', async event => {
    if (isNavigating) return; // Предотвращаем дублирование

    const pathname = window.location.pathname;
    console.log('🔙 Router: POPSTATE triggered, pathname:', pathname);

    // Проверяем кэш для быстрой навигации
    const cached = routeCache.get(pathname);
    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
      console.log('🚀 Router: Using cached route handler');
      try {
        isNavigating = true;
        await cached.handler();
        return;
      } catch (error) {
        console.warn('Cached route handler failed:', error);
      } finally {
        isNavigating = false;
      }
    }

    isNavigating = true;

    try {
      let routeHandler;

      if (pathname === '/' || pathname === '') {
        routeHandler = () => handleOverviewPage(getApplyFiltersFunction());
      } else if (pathname === '/vehicles') {
        routeHandler = () => handleVehiclesPage(getApplyFiltersFunction());
      } else if (pathname.startsWith('/vehicles/')) {
        routeHandler = () => handleTankPage(pathname, getApplyFiltersFunction());
      } else {
        routeHandler = () => handleOtherSection(pathname);
      }

      // Кэшируем обработчик с ограничением размера кэша
      if (routeCache.size >= MAX_CACHE_SIZE) {
        const oldestKey = routeCache.keys().next().value;
        routeCache.delete(oldestKey);
      }

      routeCache.set(pathname, {
        handler: routeHandler,
        timestamp: Date.now()
      });

      await routeHandler();
      console.log('✅ Router: Route handled successfully');
    } catch (error) {
      console.error('❌ Router: Route handling failed:', error);
    } finally {
      isNavigating = false;
    }
  });
}

/**
 * УПРОЩЕННЫЕ ОБРАБОТЧИКИ МАРШРУТОВ
 */

async function handleOverviewPage(applyFiltersFunc) {
  console.log('🏠 Router: Handling overview page');
  state.selectedTank = null;
  state.currentMenuName = 'overview';
  localStorage.removeItem('selectedTank');
  localStorage.setItem('activeMenuItem', 'overview');

  const { hideAllSections, showSection, updateActiveMenuButton } = await import('../utils/ui.js');
  hideAllSections();
  showSection('overview');
  updateActiveMenuButton('overview');
}

async function handleVehiclesPage(applyFiltersFunc) {
  console.log('🚗 Router: Handling vehicles page');
  state.currentMenuName = 'vehicles';
  localStorage.setItem('activeMenuItem', 'vehicles');

  const { hideAllSections, showSection, updateActiveMenuButton } = await import('../utils/ui.js');
  hideAllSections();
  showSection('vehicles');
  updateActiveMenuButton('vehicles');

  await applyFiltersFunc(true); // Сохраняем выбор
}

async function handleOtherSection(pathname) {
  console.log('📄 Router: Handling other section:', pathname);

  const section = pathname.substring(1) || 'overview';
  state.selectedTank = null;
  state.currentMenuName = section;
  localStorage.removeItem('selectedTank');
  localStorage.setItem('activeMenuItem', section);

  const { hideAllSections, showSection, updateActiveMenuButton } = await import('../utils/ui.js');
  hideAllSections();
  showSection(section);
  updateActiveMenuButton(section);
}

async function handleTankPage(pathname, applyFiltersFunc) {
  console.log('🎯 Router: Handling tank page');

  const shortId = pathname.substring('/vehicles/'.length);

  // Проверяем наличие данных танков
  if (!state.allTanks || !state.allTanks.length) {
    console.log('🔄 Waiting for tank data to load...');

    // Ждем загрузки данных с таймаутом
    const maxWaitTime = 2000; // Уменьшено для быстрого отклика
    let waitTime = 0;
    // Простое ожидание с промисом
    await new Promise(resolve => {
      const checkData = () => {
        if (state.allTanks && state.allTanks.length > 0) {
          resolve();
        } else if (waitTime >= maxWaitTime) {
          resolve();
        } else {
          waitTime += 100;
          setTimeout(checkData, 100);
        }
      };
      checkData();
    });

    if (!state.allTanks || !state.allTanks.length) {
      console.error('❌ Tank data failed to load, redirecting to vehicles');
      history.replaceState(null, '', '/vehicles');
      await handleVehiclesPage(applyFiltersFunc);
      return;
    }
  }

  // Ищем танк
  const { findTankNameByUrlId } = await import('../utils/helpers.js');
  const tankName = findTankNameByUrlId(shortId);
  const tank = tankName ? state.allTanks.find(t => t.name === tankName) : null;

  if (tank) {
    console.log('✅ Tank found:', tank.name);

    // Устанавливаем состояние
    state.selectedTank = tank;
    state.selectedCountry = tank.country;
    state.selectedCategory = tank.type;
    state.currentMenuName = 'vehicles';
    localStorage.setItem('activeMenuItem', 'vehicles');

    // Показываем секцию vehicles и характеристики танка
    const [{ hideAllSections, showSection, updateActiveMenuButton }, { updateFilterSelection }, { showTankCharacteristics }] = await Promise.all([
      import('../utils/ui.js'),
      import('../components/tank-list/index.js'),
      import('../components/TankCharacteristics.js')
    ]);

    hideAllSections();
    showSection('vehicles');
    updateActiveMenuButton('vehicles');

    // Обновляем фильтры
    const countryForUI = tank.country === 'USSR' ? 'ussr' : tank.country.toLowerCase();
    updateFilterSelection('country', countryForUI);
    updateFilterSelection('category', tank.type);

    // Показываем характеристики
    showTankCharacteristics(tank.name, false);
  } else {
    console.warn('⚠️ Tank not found for shortId:', shortId);
    history.replaceState(null, '', '/vehicles');
    await handleVehiclesPage(applyFiltersFunc);
  }
}

/**
 * УПРОЩЕННАЯ ФУНКЦИЯ ЗАГРУЗКИ СОХРАНЕННОГО СОСТОЯНИЯ
 */
export async function loadSavedState(applyFiltersFunc) {
  const pathname = window.location.pathname;

  // Защита от дублирующих вызовов
  const now = Date.now();
  if (lastRouterRestoreTime && (now - lastRouterRestoreTime) < 200) {
    return;
  }
  lastRouterRestoreTime = now;

  console.log('🔧 Loading saved state for:', pathname);

  if (pathname.startsWith('/vehicles/')) {
    await handleTankPage(pathname, applyFiltersFunc);
  } else if (pathname === '/vehicles') {
    await handleVehiclesPage(applyFiltersFunc);
  } else {
    await handleOtherSection(pathname);
  }
}

/**
 * УТИЛИТЫ ДЛЯ РОУТЕРА
 */

// Очистка кэша роутинга
export function clearRouteCache() {
  routeCache.clear();
}

// Получение метрик роутера
export function getRouterMetrics() {
  return {
    cacheSize: routeCache.size,
    isNavigating,
    lastRestoreTime: lastRouterRestoreTime
  };
}
