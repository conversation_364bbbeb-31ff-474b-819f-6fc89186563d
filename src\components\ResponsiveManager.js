/**
 * МЕНЕДЖЕР АДАПТИВНОГО ДИЗАЙНА
 * Управление адаптивностью и мобильной версией
 */

export class ResponsiveManager {
  constructor() {
    this.breakpoints = {
      mobile: 480,
      tablet: 768,
      desktop: 1024,
      large: 1200,
      xlarge: 1400
    };
    
    this.currentBreakpoint = 'desktop';
    this.orientation = 'landscape';
    this.touchDevice = false;
    this.pixelRatio = window.devicePixelRatio || 1;
    
    // Настройки адаптивности
    this.settings = {
      enableTouchOptimizations: true,
      enableMobileMenu: true,
      enableSwipeGestures: true,
      enablePinchZoom: false,
      adaptiveImages: true,
      responsiveTables: true
    };
    
    // Кэш для оптимизации
    this.layoutCache = new Map();
    this.mediaQueries = new Map();
    
    this.initializeResponsive();
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ АДАПТИВНОЙ СИСТЕМЫ
   */
  initializeResponsive() {
    this.detectDevice();
    this.setupMediaQueries();
    this.setupEventListeners();
    this.setupTouchHandlers();
    this.setupSwipeGestures();
    this.optimizeForDevice();
    
    console.log('📱 Responsive Manager initialized');
    console.log(`Device: ${this.currentBreakpoint}, Touch: ${this.touchDevice}, Orientation: ${this.orientation}`);
  }

  /**
   * ОПРЕДЕЛЕНИЕ УСТРОЙСТВА
   */
  detectDevice() {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    // Определяем breakpoint
    if (width <= this.breakpoints.mobile) {
      this.currentBreakpoint = 'mobile';
    } else if (width <= this.breakpoints.tablet) {
      this.currentBreakpoint = 'tablet';
    } else if (width <= this.breakpoints.desktop) {
      this.currentBreakpoint = 'desktop';
    } else if (width <= this.breakpoints.large) {
      this.currentBreakpoint = 'large';
    } else {
      this.currentBreakpoint = 'xlarge';
    }
    
    // Определяем ориентацию
    this.orientation = width > height ? 'landscape' : 'portrait';
    
    // Определяем touch устройство
    this.touchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    // Обновляем CSS классы
    this.updateBodyClasses();
  }

  /**
   * ОБНОВЛЕНИЕ КЛАССОВ BODY
   */
  updateBodyClasses() {
    const body = document.body;
    
    // Удаляем старые классы
    body.classList.remove('mobile', 'tablet', 'desktop', 'large', 'xlarge');
    body.classList.remove('portrait', 'landscape');
    body.classList.remove('touch', 'no-touch');
    
    // Добавляем новые классы
    body.classList.add(this.currentBreakpoint);
    body.classList.add(this.orientation);
    body.classList.add(this.touchDevice ? 'touch' : 'no-touch');
    
    // Добавляем класс для высокого DPI
    if (this.pixelRatio > 1) {
      body.classList.add('high-dpi');
    }
  }

  /**
   * НАСТРОЙКА MEDIA QUERIES
   */
  setupMediaQueries() {
    Object.keys(this.breakpoints).forEach(breakpoint => {
      const query = window.matchMedia(`(max-width: ${this.breakpoints[breakpoint]}px)`);
      
      query.addListener((e) => {
        this.handleBreakpointChange(breakpoint, e.matches);
      });
      
      this.mediaQueries.set(breakpoint, query);
    });
    
    // Ориентация
    const orientationQuery = window.matchMedia('(orientation: portrait)');
    orientationQuery.addListener((e) => {
      this.handleOrientationChange(e.matches ? 'portrait' : 'landscape');
    });
  }

  /**
   * НАСТРОЙКА ОБРАБОТЧИКОВ СОБЫТИЙ
   */
  setupEventListeners() {
    // Изменение размера окна
    window.addEventListener('resize', this.debounce(() => {
      this.handleResize();
    }, 100));
    
    // Изменение ориентации
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.handleOrientationChange();
      }, 100);
    });
    
    // Изменение DPI
    if (window.matchMedia) {
      const dpiQuery = window.matchMedia(`(resolution: ${this.pixelRatio}dppx)`);
      dpiQuery.addListener(() => {
        this.handleDPIChange();
      });
    }
  }

  /**
   * НАСТРОЙКА TOUCH ОБРАБОТЧИКОВ
   */
  setupTouchHandlers() {
    if (!this.touchDevice || !this.settings.enableTouchOptimizations) return;
    
    // Увеличиваем размеры кликабельных элементов
    this.optimizeTouchTargets();
    
    // Настраиваем touch события
    document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
    document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
    document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
    
    console.log('👆 Touch optimizations enabled');
  }

  /**
   * НАСТРОЙКА SWIPE ЖЕСТОВ
   */
  setupSwipeGestures() {
    if (!this.touchDevice || !this.settings.enableSwipeGestures) return;
    
    this.swipeHandler = new SwipeHandler();
    
    // Swipe для навигации по таблице сравнения
    const compareTable = document.querySelector('.adaptive-compare-table');
    if (compareTable) {
      this.swipeHandler.addSwipeTarget(compareTable, {
        onSwipeLeft: () => this.scrollTableHorizontally(100),
        onSwipeRight: () => this.scrollTableHorizontally(-100)
      });
    }
    
    console.log('👈 Swipe gestures enabled');
  }

  /**
   * ОПТИМИЗАЦИЯ ДЛЯ УСТРОЙСТВА
   */
  optimizeForDevice() {
    if (this.isMobile()) {
      this.optimizeForMobile();
    } else if (this.isTablet()) {
      this.optimizeForTablet();
    } else {
      this.optimizeForDesktop();
    }
  }

  /**
   * ОПТИМИЗАЦИЯ ДЛЯ МОБИЛЬНЫХ
   */
  optimizeForMobile() {
    // Создаем мобильное меню
    if (this.settings.enableMobileMenu) {
      this.createMobileMenu();
    }
    
    // Адаптируем таблицы
    if (this.settings.responsiveTables) {
      this.adaptTablesForMobile();
    }
    
    // Оптимизируем изображения
    if (this.settings.adaptiveImages) {
      this.optimizeImagesForMobile();
    }
    
    // Настраиваем viewport
    this.setupMobileViewport();
    
    console.log('📱 Mobile optimizations applied');
  }

  /**
   * ОПТИМИЗАЦИЯ ДЛЯ ПЛАНШЕТОВ
   */
  optimizeForTablet() {
    // Адаптируем интерфейс для планшетов
    this.adaptInterfaceForTablet();
    
    // Оптимизируем touch targets
    this.optimizeTouchTargets();
    
    console.log('📱 Tablet optimizations applied');
  }

  /**
   * ОПТИМИЗАЦИЯ ДЛЯ ДЕСКТОПА
   */
  optimizeForDesktop() {
    // Включаем hover эффекты
    this.enableHoverEffects();
    
    // Оптимизируем для мыши
    this.optimizeForMouse();
    
    console.log('🖥️ Desktop optimizations applied');
  }

  /**
   * СОЗДАНИЕ МОБИЛЬНОГО МЕНЮ
   */
  createMobileMenu() {
    const existingMenu = document.querySelector('.mobile-menu');
    if (existingMenu) return;
    
    const mobileMenu = document.createElement('div');
    mobileMenu.className = 'mobile-menu';
    mobileMenu.innerHTML = `
      <div class="mobile-menu-header">
        <h3 class="menu-title">Tank Compare</h3>
        <button class="menu-close-btn" id="mobile-menu-close">×</button>
      </div>
      <div class="mobile-menu-content">
        <nav class="mobile-nav">
          <a href="#vehicles" class="mobile-nav-item">
            <span class="nav-icon">🚗</span>
            <span class="nav-text">Vehicles</span>
          </a>
          <a href="#compare" class="mobile-nav-item">
            <span class="nav-icon">⚖️</span>
            <span class="nav-text">Compare</span>
          </a>
          <a href="#filters" class="mobile-nav-item">
            <span class="nav-icon">🔍</span>
            <span class="nav-text">Filters</span>
          </a>
        </nav>
      </div>
    `;
    
    document.body.appendChild(mobileMenu);
    
    // Создаем кнопку меню
    const menuButton = document.createElement('button');
    menuButton.className = 'mobile-menu-button';
    menuButton.innerHTML = '☰';
    menuButton.addEventListener('click', () => this.toggleMobileMenu());
    
    const header = document.querySelector('.app-header') || document.body;
    header.appendChild(menuButton);
    
    // Обработчики
    document.getElementById('mobile-menu-close').addEventListener('click', () => {
      this.closeMobileMenu();
    });
  }

  /**
   * ПЕРЕКЛЮЧЕНИЕ МОБИЛЬНОГО МЕНЮ
   */
  toggleMobileMenu() {
    const menu = document.querySelector('.mobile-menu');
    if (menu) {
      menu.classList.toggle('open');
      document.body.classList.toggle('menu-open');
    }
  }

  /**
   * ЗАКРЫТИЕ МОБИЛЬНОГО МЕНЮ
   */
  closeMobileMenu() {
    const menu = document.querySelector('.mobile-menu');
    if (menu) {
      menu.classList.remove('open');
      document.body.classList.remove('menu-open');
    }
  }

  /**
   * АДАПТАЦИЯ ТАБЛИЦ ДЛЯ МОБИЛЬНЫХ
   */
  adaptTablesForMobile() {
    const tables = document.querySelectorAll('.comparison-table, .adaptive-compare-table');
    
    tables.forEach(table => {
      if (!table.classList.contains('mobile-adapted')) {
        this.convertTableToCards(table);
        table.classList.add('mobile-adapted');
      }
    });
  }

  /**
   * КОНВЕРТАЦИЯ ТАБЛИЦЫ В КАРТОЧКИ
   */
  convertTableToCards(table) {
    if (!this.isMobile()) return;
    
    const rows = table.querySelectorAll('.characteristic-row, .quantum-characteristic-row');
    const cardsContainer = document.createElement('div');
    cardsContainer.className = 'mobile-cards-container';
    
    rows.forEach(row => {
      const card = this.createMobileCard(row);
      cardsContainer.appendChild(card);
    });
    
    // Заменяем таблицу на карточки только на мобильных
    table.style.display = 'none';
    table.parentNode.insertBefore(cardsContainer, table.nextSibling);
  }

  /**
   * СОЗДАНИЕ МОБИЛЬНОЙ КАРТОЧКИ
   */
  createMobileCard(row) {
    const card = document.createElement('div');
    card.className = 'mobile-characteristic-card';
    
    const characteristic = row.querySelector('.characteristic-cell, .quantum-characteristic-name');
    const values = row.querySelectorAll('.value-cell, .quantum-characteristic-value');
    
    card.innerHTML = `
      <div class="card-header">
        <h4 class="characteristic-name">${characteristic?.textContent || 'Unknown'}</h4>
      </div>
      <div class="card-values">
        ${Array.from(values).map((value, index) => `
          <div class="value-item ${value.classList.contains('optimal') ? 'optimal' : ''}">
            <span class="tank-index">Tank ${index + 1}</span>
            <span class="value">${value.textContent}</span>
          </div>
        `).join('')}
      </div>
    `;
    
    return card;
  }

  /**
   * ОПТИМИЗАЦИЯ ИЗОБРАЖЕНИЙ ДЛЯ МОБИЛЬНЫХ
   */
  optimizeImagesForMobile() {
    const images = document.querySelectorAll('img');
    
    images.forEach(img => {
      if (!img.hasAttribute('data-mobile-optimized')) {
        // Добавляем lazy loading
        if ('loading' in HTMLImageElement.prototype) {
          img.loading = 'lazy';
        }
        
        // Уменьшаем качество для мобильных
        if (this.isMobile() && img.src.includes('http')) {
          const url = new URL(img.src);
          url.searchParams.set('w', '400');
          url.searchParams.set('q', '75');
          img.src = url.toString();
        }
        
        img.setAttribute('data-mobile-optimized', 'true');
      }
    });
  }

  /**
   * НАСТРОЙКА МОБИЛЬНОГО VIEWPORT
   */
  setupMobileViewport() {
    let viewport = document.querySelector('meta[name="viewport"]');
    
    if (!viewport) {
      viewport = document.createElement('meta');
      viewport.name = 'viewport';
      document.head.appendChild(viewport);
    }
    
    if (this.isMobile()) {
      viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
    } else {
      viewport.content = 'width=device-width, initial-scale=1.0';
    }
  }

  /**
   * ОПТИМИЗАЦИЯ TOUCH TARGETS
   */
  optimizeTouchTargets() {
    const touchTargets = document.querySelectorAll('button, a, input, select, .clickable');
    
    touchTargets.forEach(target => {
      if (!target.classList.contains('touch-optimized')) {
        const rect = target.getBoundingClientRect();
        
        // Минимальный размер touch target 44px
        if (rect.width < 44 || rect.height < 44) {
          target.style.minWidth = '44px';
          target.style.minHeight = '44px';
          target.style.padding = '8px';
        }
        
        target.classList.add('touch-optimized');
      }
    });
  }

  /**
   * ОБРАБОТКА ИЗМЕНЕНИЯ РАЗМЕРА
   */
  handleResize() {
    const oldBreakpoint = this.currentBreakpoint;
    this.detectDevice();
    
    if (oldBreakpoint !== this.currentBreakpoint) {
      this.handleBreakpointChange(this.currentBreakpoint, true);
      this.optimizeForDevice();
    }
    
    // Очищаем кэш размеров
    this.layoutCache.clear();
    
    // Эмитируем событие
    this.emitEvent('responsive:resize', {
      breakpoint: this.currentBreakpoint,
      orientation: this.orientation,
      width: window.innerWidth,
      height: window.innerHeight
    });
  }

  /**
   * ОБРАБОТКА ИЗМЕНЕНИЯ BREAKPOINT
   */
  handleBreakpointChange(breakpoint, matches) {
    if (matches) {
      console.log(`📱 Breakpoint changed to: ${breakpoint}`);
      
      // Эмитируем событие
      this.emitEvent('responsive:breakpoint', {
        breakpoint,
        oldBreakpoint: this.currentBreakpoint
      });
    }
  }

  /**
   * ОБРАБОТКА ИЗМЕНЕНИЯ ОРИЕНТАЦИИ
   */
  handleOrientationChange(newOrientation = null) {
    const oldOrientation = this.orientation;
    
    if (newOrientation) {
      this.orientation = newOrientation;
    } else {
      this.detectDevice();
    }
    
    if (oldOrientation !== this.orientation) {
      console.log(`📱 Orientation changed to: ${this.orientation}`);
      
      // Обновляем layout
      this.updateLayoutForOrientation();
      
      // Эмитируем событие
      this.emitEvent('responsive:orientation', {
        orientation: this.orientation,
        oldOrientation
      });
    }
  }

  /**
   * ОБНОВЛЕНИЕ LAYOUT ДЛЯ ОРИЕНТАЦИИ
   */
  updateLayoutForOrientation() {
    if (this.isMobile()) {
      const tables = document.querySelectorAll('.adaptive-compare-table');
      
      tables.forEach(table => {
        if (this.orientation === 'landscape') {
          table.classList.add('landscape-mode');
        } else {
          table.classList.remove('landscape-mode');
        }
      });
    }
  }

  /**
   * ГОРИЗОНТАЛЬНАЯ ПРОКРУТКА ТАБЛИЦЫ
   */
  scrollTableHorizontally(delta) {
    const table = document.querySelector('.table-scroll-container');
    if (table) {
      table.scrollLeft += delta;
    }
  }

  /**
   * ОБРАБОТКА TOUCH СОБЫТИЙ
   */
  handleTouchStart(event) {
    this.touchStartX = event.touches[0].clientX;
    this.touchStartY = event.touches[0].clientY;
    this.touchStartTime = Date.now();
  }

  handleTouchMove(event) {
    // Предотвращаем скролл страницы при горизонтальном свайпе в таблице
    const target = event.target.closest('.adaptive-compare-table');
    if (target) {
      const deltaX = Math.abs(event.touches[0].clientX - this.touchStartX);
      const deltaY = Math.abs(event.touches[0].clientY - this.touchStartY);
      
      if (deltaX > deltaY) {
        event.preventDefault();
      }
    }
  }

  handleTouchEnd(event) {
    const touchEndTime = Date.now();
    const touchDuration = touchEndTime - this.touchStartTime;
    
    // Обрабатываем быстрые тапы
    if (touchDuration < 200) {
      this.handleQuickTap(event);
    }
  }

  /**
   * ОБРАБОТКА БЫСТРОГО ТАПА
   */
  handleQuickTap(event) {
    const target = event.target;
    
    // Добавляем визуальную обратную связь
    if (target.classList.contains('tank-item') || target.classList.contains('ultra-button')) {
      target.classList.add('tap-feedback');
      setTimeout(() => {
        target.classList.remove('tap-feedback');
      }, 150);
    }
  }

  /**
   * ПРОВЕРКИ УСТРОЙСТВ
   */
  isMobile() {
    return this.currentBreakpoint === 'mobile';
  }

  isTablet() {
    return this.currentBreakpoint === 'tablet';
  }

  isDesktop() {
    return ['desktop', 'large', 'xlarge'].includes(this.currentBreakpoint);
  }

  isTouchDevice() {
    return this.touchDevice;
  }

  isPortrait() {
    return this.orientation === 'portrait';
  }

  isLandscape() {
    return this.orientation === 'landscape';
  }

  /**
   * ПОЛУЧЕНИЕ ТЕКУЩЕГО BREAKPOINT
   */
  getCurrentBreakpoint() {
    return this.currentBreakpoint;
  }

  /**
   * ПОЛУЧЕНИЕ РАЗМЕРОВ VIEWPORT
   */
  getViewportSize() {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      ratio: window.innerWidth / window.innerHeight
    };
  }

  /**
   * ДЕБАУНСИНГ
   */
  debounce(func, delay) {
    let timeoutId;
    return (...args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * ЭМИССИЯ СОБЫТИЙ
   */
  emitEvent(eventName, data) {
    const event = new CustomEvent(eventName, { detail: data });
    document.dispatchEvent(event);
  }

  /**
   * ПОДПИСКА НА СОБЫТИЯ
   */
  on(eventName, callback) {
    document.addEventListener(eventName, callback);
  }

  /**
   * ОТПИСКА ОТ СОБЫТИЙ
   */
  off(eventName, callback) {
    document.removeEventListener(eventName, callback);
  }

  /**
   * УНИЧТОЖЕНИЕ МЕНЕДЖЕРА
   */
  destroy() {
    // Удаляем обработчики
    this.mediaQueries.forEach(query => {
      query.removeListener();
    });
    
    // Очищаем кэш
    this.layoutCache.clear();
    
    // Удаляем мобильное меню
    const mobileMenu = document.querySelector('.mobile-menu');
    if (mobileMenu) {
      mobileMenu.remove();
    }
    
    const menuButton = document.querySelector('.mobile-menu-button');
    if (menuButton) {
      menuButton.remove();
    }
    
    console.log('📱 Responsive Manager destroyed');
  }
}

/**
 * ОБРАБОТЧИК SWIPE ЖЕСТОВ
 */
class SwipeHandler {
  constructor() {
    this.swipeTargets = new Map();
    this.minSwipeDistance = 50;
    this.maxSwipeTime = 300;
  }

  addSwipeTarget(element, handlers) {
    const swipeData = {
      handlers,
      startX: 0,
      startY: 0,
      startTime: 0
    };
    
    element.addEventListener('touchstart', (e) => {
      swipeData.startX = e.touches[0].clientX;
      swipeData.startY = e.touches[0].clientY;
      swipeData.startTime = Date.now();
    });
    
    element.addEventListener('touchend', (e) => {
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      const endTime = Date.now();
      
      const deltaX = endX - swipeData.startX;
      const deltaY = endY - swipeData.startY;
      const deltaTime = endTime - swipeData.startTime;
      
      if (deltaTime <= this.maxSwipeTime && Math.abs(deltaX) >= this.minSwipeDistance) {
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
          if (deltaX > 0 && handlers.onSwipeRight) {
            handlers.onSwipeRight();
          } else if (deltaX < 0 && handlers.onSwipeLeft) {
            handlers.onSwipeLeft();
          }
        }
      }
    });
    
    this.swipeTargets.set(element, swipeData);
  }

  removeSwipeTarget(element) {
    this.swipeTargets.delete(element);
  }
}

// Создаем глобальный экземпляр
export const responsiveManager = new ResponsiveManager();
