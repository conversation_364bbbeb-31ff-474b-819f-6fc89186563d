/**
 * ULTRA SERVICE WORKER
 * Advanced caching strategies with intelligent cache management
 */

const CACHE_VERSION = 'v3.0.13-nuclear-fix';
const STATIC_CACHE = `static-${CACHE_VERSION}`;
const DYNAMIC_CACHE = `dynamic-${CACHE_VERSION}`;
const IMAGE_CACHE = `images-${CACHE_VERSION}`;
const API_CACHE = `api-${CACHE_VERSION}`;

// Cache configuration
const CACHE_CONFIG = {
  maxAge: {
    static: 30 * 24 * 60 * 60 * 1000,    // 30 days
    dynamic: 7 * 24 * 60 * 60 * 1000,    // 7 days
    images: 14 * 24 * 60 * 60 * 1000,    // 14 days
    api: 60 * 60 * 1000                  // 1 hour
  },
  maxEntries: {
    static: 100,
    dynamic: 50,
    images: 200,
    api: 30
  }
};

// Static assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico'
];

// Install event - cache static assets
self.addEventListener('install', event => {
  console.log('🔧 Service Worker installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('📦 Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('✅ Static assets cached');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('❌ Failed to cache static assets:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('🚀 Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (!cacheName.includes(CACHE_VERSION)) {
              console.log('🗑️ Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ Service Worker activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }
  
  event.respondWith(handleRequest(request));
});

/**
 * HANDLE REQUEST WITH APPROPRIATE CACHING STRATEGY
 */
async function handleRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  try {
    // Static assets - Cache First
    if (isStaticAsset(pathname)) {
      return await cacheFirst(request, STATIC_CACHE);
    }
    
    // Images - Cache First with fallback
    if (isImage(pathname)) {
      return await cacheFirst(request, IMAGE_CACHE);
    }
    
    // API requests - Network First
    if (isAPIRequest(pathname)) {
      return await networkFirst(request, API_CACHE);
    }
    
    // HTML pages - Network First with cache fallback
    if (isHTMLRequest(request)) {
      return await networkFirst(request, DYNAMIC_CACHE);
    }
    
    // Default - Network First
    return await networkFirst(request, DYNAMIC_CACHE);
    
  } catch (error) {
    console.warn('🔄 Request handling failed, providing fallback:', error.message);

    // For development mode, provide a more graceful fallback
    if (request.url.includes('localhost') || request.url.includes('127.0.0.1')) {
      // Try to get from cache first
      try {
        const cache = await caches.open(DYNAMIC_CACHE);
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
          return cachedResponse;
        }
      } catch (cacheError) {
        console.warn('Cache access failed:', cacheError.message);
      }

      // Return a simple response for development
      return new Response('Development mode - resource not cached', {
        status: 200,
        statusText: 'OK',
        headers: { 'Content-Type': 'text/plain' }
      });
    }

    return await getOfflineFallback(request);
  }
}

/**
 * CACHE FIRST STRATEGY
 */
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    // Check if cache is still valid
    if (await isCacheValid(cachedResponse, cacheName)) {
      return cachedResponse;
    }
  }
  
  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Clone response before caching
      const responseClone = networkResponse.clone();
      await cache.put(request, responseClone);

      // Manage cache size
      await manageCacheSize(cacheName);
    }

    return networkResponse;
  } catch (error) {
    console.warn('🔄 Network fetch failed, using cache fallback:', request.url);
    // Return cached version if network fails
    if (cachedResponse) {
      return cachedResponse;
    }

    // For development mode, return a simple response instead of throwing
    if (request.url.includes('localhost') || request.url.includes('127.0.0.1')) {
      return new Response('Resource not available in development mode', {
        status: 200,
        statusText: 'OK',
        headers: { 'Content-Type': 'text/plain' }
      });
    }

    throw error;
  }
}

/**
 * NETWORK FIRST STRATEGY
 */
async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      const responseClone = networkResponse.clone();
      await cache.put(request, responseClone);
      
      // Manage cache size
      await manageCacheSize(cacheName);
    }
    
    return networkResponse;
  } catch (error) {
    // Fallback to cache
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    throw error;
  }
}

/**
 * CHECK IF CACHE IS STILL VALID
 */
async function isCacheValid(response, cacheName) {
  const cacheType = getCacheType(cacheName);
  const maxAge = CACHE_CONFIG.maxAge[cacheType] || CACHE_CONFIG.maxAge.dynamic;
  
  const dateHeader = response.headers.get('date');
  if (!dateHeader) return true; // No date header, assume valid
  
  const cacheDate = new Date(dateHeader);
  const now = new Date();
  const age = now.getTime() - cacheDate.getTime();
  
  return age < maxAge;
}

/**
 * MANAGE CACHE SIZE
 */
async function manageCacheSize(cacheName) {
  const cache = await caches.open(cacheName);
  const keys = await cache.keys();
  const cacheType = getCacheType(cacheName);
  const maxEntries = CACHE_CONFIG.maxEntries[cacheType] || CACHE_CONFIG.maxEntries.dynamic;
  
  if (keys.length > maxEntries) {
    // Remove oldest entries
    const entriesToRemove = keys.length - maxEntries;
    for (let i = 0; i < entriesToRemove; i++) {
      await cache.delete(keys[i]);
    }
    
    console.log(`🗑️ Removed ${entriesToRemove} old entries from ${cacheName}`);
  }
}

/**
 * GET CACHE TYPE FROM CACHE NAME
 */
function getCacheType(cacheName) {
  if (cacheName.includes('static')) return 'static';
  if (cacheName.includes('images')) return 'images';
  if (cacheName.includes('api')) return 'api';
  return 'dynamic';
}

/**
 * CHECK IF REQUEST IS FOR STATIC ASSET
 */
function isStaticAsset(pathname) {
  return /\.(js|css|woff2?|ttf|eot)$/.test(pathname);
}

/**
 * CHECK IF REQUEST IS FOR IMAGE
 */
function isImage(pathname) {
  return /\.(png|jpg|jpeg|gif|webp|avif|svg|ico)$/.test(pathname);
}

/**
 * CHECK IF REQUEST IS FOR API
 */
function isAPIRequest(pathname) {
  return pathname.startsWith('/api/') || pathname.includes('api');
}

/**
 * CHECK IF REQUEST IS FOR HTML
 */
function isHTMLRequest(request) {
  return request.headers.get('accept')?.includes('text/html');
}

/**
 * GET OFFLINE FALLBACK
 */
async function getOfflineFallback(request) {
  const cache = await caches.open(STATIC_CACHE);
  
  // Try to serve cached index.html for navigation requests
  if (isHTMLRequest(request)) {
    const fallback = await cache.match('/index.html');
    if (fallback) {
      return fallback;
    }
  }
  
  // Return a basic offline response
  return new Response(
    JSON.stringify({
      error: 'Offline',
      message: 'This content is not available offline'
    }),
    {
      status: 503,
      statusText: 'Service Unavailable',
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

// Message handling for cache management
self.addEventListener('message', event => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'CLEAR_CACHE':
      clearAllCaches().then(() => {
        event.ports[0].postMessage({ success: true });
      });
      break;
      
    case 'GET_CACHE_SIZE':
      getCacheSize().then(size => {
        event.ports[0].postMessage({ size });
      });
      break;
      
    default:
      console.log('Unknown message type:', type);
  }
});

/**
 * CLEAR ALL CACHES
 */
async function clearAllCaches() {
  const cacheNames = await caches.keys();
  await Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
  console.log('🗑️ All caches cleared');
}

/**
 * GET TOTAL CACHE SIZE
 */
async function getCacheSize() {
  const cacheNames = await caches.keys();
  let totalSize = 0;
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    totalSize += keys.length;
  }
  
  return totalSize;
}

console.log('🚀 Ultra Service Worker loaded');
