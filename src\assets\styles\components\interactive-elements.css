/* ========================================
   ИНТЕРАКТИВНЫЕ ЭЛЕМЕНТЫ
   ======================================== */

/* ПОДСКАЗКИ */
.interactive-tooltip {
  position: absolute;
  background: linear-gradient(135deg, 
    rgba(26, 26, 46, 0.95) 0%,
    rgba(22, 33, 62, 0.9) 100%);
  border-radius: 12px;
  border: 1px solid rgba(114, 84, 228, 0.3);
  backdrop-filter: blur(20px);
  padding: 1rem;
  max-width: 300px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(114, 84, 228, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 10000;
}

.interactive-tooltip.visible {
  opacity: 1;
  transform: scale(1);
}

.interactive-tooltip::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid rgba(114, 84, 228, 0.3);
}

/* ПОДСКАЗКА ТАНКА */
.tank-tooltip {
  min-width: 250px;
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip-tank-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tooltip-tank-info {
  flex: 1;
}

.tooltip-tank-name {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
}

.tooltip-tank-meta {
  font-size: 0.8rem;
  color: #9ca3af;
}

.tooltip-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tooltip-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.stat-label {
  color: #d1d5db;
}

.stat-value {
  color: #10b981;
  font-weight: 600;
}

/* ПОДСКАЗКА ХАРАКТЕРИСТИКИ */
.characteristic-tooltip {
  min-width: 200px;
}

.tooltip-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.tooltip-description {
  font-size: 0.85rem;
  color: #d1d5db;
  line-height: 1.4;
  margin-bottom: 0.75rem;
}

.tooltip-formula {
  font-size: 0.8rem;
  color: #9ca3af;
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.5rem;
  border-radius: 6px;
  margin-bottom: 0.75rem;
}

.tooltip-tips {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 0.75rem;
}

.tooltip-tip {
  font-size: 0.8rem;
  color: #fbbf24;
  line-height: 1.3;
}

/* ЭФФЕКТЫ СВЕЧЕНИЯ */
.interactive-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-glow:hover {
  box-shadow: 0 0 20px rgba(114, 84, 228, 0.4);
  border-color: rgba(114, 84, 228, 0.5);
}

/* АНИМАЦИЯ КЛИКА */
.click-ripple {
  animation: rippleEffect 0.6s ease-out;
}

@keyframes rippleEffect {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* ЭФФЕКТЫ КНОПОК */
.button-clicked {
  background: linear-gradient(135deg, #8b5cf6, #a855f7) !important;
  box-shadow: 0 0 30px rgba(139, 92, 246, 0.6) !important;
}

/* ЧАСТИЦЫ */
.interactive-particle {
  animation: particleFloat 1.5s ease-out forwards;
}

@keyframes particleFloat {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -150%) scale(0);
    opacity: 0;
  }
}

.background-particle {
  animation: backgroundFloat linear forwards;
}

@keyframes backgroundFloat {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.3;
  }
  90% {
    opacity: 0.3;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

/* ВЫДЕЛЕНИЕ ЗНАЧЕНИЙ */
.highlight-best {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.15)) !important;
  border: 1px solid rgba(16, 185, 129, 0.4) !important;
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3) !important;
  animation: highlightPulse 2s ease-in-out infinite;
}

.highlight-worst {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.15)) !important;
  border: 1px solid rgba(239, 68, 68, 0.4) !important;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3) !important;
  animation: highlightPulse 2s ease-in-out infinite;
}

@keyframes highlightPulse {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.02);
    filter: brightness(1.1);
  }
}

/* АНИМАЦИИ ПОЯВЛЕНИЯ */
[data-animate="fade-in"] {
  opacity: 0;
}

[data-animate="slide-up"] {
  opacity: 0;
  transform: translateY(30px);
}

[data-animate="scale-in"] {
  opacity: 0;
  transform: scale(0.8);
}

[data-animate="stagger"] > * {
  opacity: 0;
  transform: translateY(20px);
}

/* ИНТЕРАКТИВНЫЕ СОСТОЯНИЯ */
.tank-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tank-item:hover {
  transform: translateY(-5px) scale(1.02);
  filter: brightness(1.1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.tank-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tank-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.1), 
    transparent);
  transition: left 0.5s ease;
}

.tank-card:hover::before {
  left: 100%;
}

.tank-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* ХАРАКТЕРИСТИКИ */
.characteristic-value,
.quantum-characteristic-value,
.value-cell {
  transition: all 0.2s ease;
  position: relative;
}

.characteristic-value:hover,
.quantum-characteristic-value:hover,
.value-cell:hover {
  background: rgba(255, 255, 255, 0.08) !important;
  transform: scale(1.05);
  z-index: 10;
}

.characteristic-value.optimal,
.quantum-characteristic-value.optimal,
.value-cell.optimal {
  position: relative;
}

.characteristic-value.optimal::after,
.quantum-characteristic-value.optimal::after,
.value-cell.optimal::after {
  content: '👑';
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 0.8rem;
  animation: crownFloat 2s ease-in-out infinite;
}

@keyframes crownFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-3px) rotate(5deg);
  }
}

/* КНОПКИ */
.ultra-button,
.compare-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ultra-button::before,
.compare-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  pointer-events: none;
}

.ultra-button:hover::before,
.compare-btn:hover::before {
  width: 200px;
  height: 200px;
}

.ultra-button:hover,
.compare-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.ultra-button:active,
.compare-btn:active {
  transform: translateY(0) scale(0.98);
}

/* КАТЕГОРИИ */
.category-header {
  transition: all 0.3s ease;
}

.category-header:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
}

/* КОЛОНКИ ТАНКОВ */
.tank-column {
  transition: all 0.3s ease;
}

.tank-column:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

/* ЭФФЕКТЫ ЗАГРУЗКИ */
.loading-shimmer {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* ПУЛЬСАЦИЯ */
.pulse-effect {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(114, 84, 228, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(114, 84, 228, 0.6);
  }
}

/* АДАПТИВНОСТЬ */
@media (max-width: 768px) {
  .interactive-tooltip {
    max-width: 250px;
    padding: 0.75rem;
  }
  
  .tooltip-tank-icon {
    width: 32px;
    height: 32px;
  }
  
  .tooltip-tank-name {
    font-size: 0.9rem;
  }
  
  .tank-item:hover {
    transform: translateY(-3px) scale(1.01);
  }
  
  .tank-card:hover {
    transform: translateY(-2px);
  }
}

@media (max-width: 480px) {
  .interactive-tooltip {
    max-width: 200px;
    padding: 0.5rem;
  }
  
  .tooltip-header {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .tooltip-stats {
    gap: 0.25rem;
  }
  
  .tooltip-stat {
    font-size: 0.8rem;
  }
}
