/**
 * Красивая система уведомлений в столбик
 * Уведомления появляются сверху, толкают друг друга вниз, и улетают вправо
 */

class NotificationManager {
  constructor() {
    this.notifications = [];
    this.container = null;
    this.maxNotifications = 5;
    this.defaultDuration = 4000; // 4 секунды - оптимальное время
    this.animationDuration = 400;
    this.soundEnabled = true;
    this.notificationHeight = 80; // Примерная высота уведомления + отступ
    this.globalRemovalTimer = null; // Глобальный таймер удаления
    this.isSequentialRemovalActive = false; // Флаг активного удаления

    this.init();
  }

  init() {
    this.createContainer();
    this.addStyles();
  }

  createContainer() {
    if (this.container) return;

    this.container = document.createElement('div');
    this.container.id = 'notification-container';
    this.container.className = 'notification-container';
    document.body.appendChild(this.container);
  }

  addStyles() {
    if (document.getElementById('notification-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'notification-styles';
    styles.textContent = `
      .notification-container {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 10000;
        pointer-events: none;
        max-width: 400px;
        width: 400px;
        height: auto;
        display: flex;
        flex-direction: column-reverse; /* Новые снизу, старые сверху */
        align-items: flex-end;
        gap: 0;
      }

      .notification {
        background: linear-gradient(135deg, rgba(139, 92, 246, 0.95), rgba(168, 85, 247, 0.95));
        border: 1px solid rgba(139, 92, 246, 0.3);
        border-radius: 12px;
        padding: 1rem 1.25rem;
        color: white;
        backdrop-filter: blur(20px);
        box-shadow:
          0 20px 40px rgba(139, 92, 246, 0.2),
          0 8px 16px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        width: 100%;
        min-height: 60px;
        transform: translateX(0) translateZ(0);
        opacity: 1;
        transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important, opacity 0.4s ease-out !important;
        pointer-events: auto;
        cursor: default;
        overflow: visible;
        box-sizing: border-box;
        margin-bottom: 12px;
        z-index: 999999;
        animation: none !important;
      }

      .notification.show {
        transform: translateX(0) translateZ(0);
        opacity: 1;
        will-change: auto;
      }

      /* Удаляем старый класс hide - используем только swipe-out */



      .notification.success {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.95), rgba(5, 150, 105, 0.95));
        border-color: rgba(16, 185, 129, 0.3);
        box-shadow:
          0 20px 40px rgba(16, 185, 129, 0.2),
          0 8px 16px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }

      .notification.error {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.95), rgba(220, 38, 38, 0.95));
        border-color: rgba(239, 68, 68, 0.3);
        box-shadow:
          0 20px 40px rgba(239, 68, 68, 0.2),
          0 8px 16px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }

      .notification.warning {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.95), rgba(217, 119, 6, 0.95));
        border-color: rgba(245, 158, 11, 0.3);
        box-shadow:
          0 20px 40px rgba(245, 158, 11, 0.2),
          0 8px 16px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }

      .notification.info {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.95), rgba(37, 99, 235, 0.95));
        border-color: rgba(59, 130, 246, 0.3);
        box-shadow:
          0 20px 40px rgba(59, 130, 246, 0.2),
          0 8px 16px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }

      .notification-content {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
      }

      .notification-icon {
        font-size: 1.5rem;
        flex-shrink: 0;
        margin-top: 0.125rem;
      }

      .notification-text {
        flex: 1;
        min-width: 0;
      }

      .notification-title {
        font-size: 1rem;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
        line-height: 1.3;
      }

      /* Золотой цвет для Coming Soon */
      .notification-title.coming-soon {
        background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
      }

      .notification-message {
        font-size: 0.875rem;
        margin: 0;
        line-height: 1.4;
        opacity: 0.9;
      }

      .notification-close {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        transition: background-color 0.2s ease;
      }

      .notification-close:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      .notification-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 0 0 12px 12px;
        transform-origin: left;
        animation: progress linear;
      }

      @keyframes progress {
        from { transform: scaleX(1); }
        to { transform: scaleX(0); }
      }

      /* Убираем эффекты при наведении для более чистого UX */

      /* ПЛАВНАЯ АНИМАЦИЯ ИСЧЕЗНОВЕНИЯ ВПРАВО */
      .notification.swipe-out {
        transform: translateX(100%) translateZ(0) !important;
        opacity: 0 !important;
        transition: transform 0.4s ease-in !important, opacity 0.4s ease-in !important;
        pointer-events: none;
        animation: none !important;
      }

      /* ПЛАВНАЯ АНИМАЦИЯ ЗАТУХАНИЯ */
      .notification.fade-out {
        opacity: 0 !important;
        transform: translateX(100%) translateZ(0) !important;
        transition: transform 0.4s ease-in !important, opacity 0.4s ease-in !important;
        pointer-events: none;
        animation: none !important;
      }

      /* Адаптивность */
      @media (max-width: 480px) {
        .notification-container {
          left: 1rem;
          right: 1rem;
          bottom: 1rem;
          max-width: none;
        }
        
        .notification {
          padding: 0.875rem 1rem;
        }
        
        .notification-title {
          font-size: 0.9rem;
        }
        
        .notification-message {
          font-size: 0.8rem;
        }
      }
    `;

    document.head.appendChild(styles);
  }

  show(message, type = 'info', options = {}) {
    console.log('🔔 NotificationManager.show called:', { message, type, options });
    console.log('🔔 Container exists:', !!this.container);
    console.log('🔔 Container in DOM:', !!this.container?.parentElement);

    // Воспроизводим звук
    this.playSound(type);

    const {
      title = this.getDefaultTitle(type),
      duration = this.defaultDuration,
      icon = this.getDefaultIcon(type),
      closable = true
    } = options;

    // Удаляем старые уведомления если их слишком много
    if (this.notifications.length >= this.maxNotifications) {
      this.removeOldest();
    }

    const notification = this.createNotification(title, message, type, icon, closable);

    console.log(`📥 Creating notification. Total will be: ${this.notifications.length + 1}`);

    // Принудительно устанавливаем рабочее состояние (без красных линий)
    notification.style.transform = 'translateX(0) translateZ(0)'; // Видимое положение
    notification.style.opacity = '1'; // Видимое
    notification.style.zIndex = '999999'; // Высокий z-index

    // Добавляем в конец DOM (новые снизу)
    this.container.appendChild(notification);

    // Добавляем в начало массива (новые в начале, старые в конце)
    this.notifications.unshift(notification);

    console.log(`📥 Added notification. Total: ${this.notifications.length}`);

    // Анимация появления
    requestAnimationFrame(() => {
      notification.classList.add('show');
      console.log('🎬 Show animation started for notification');
    });

    // Прогресс-бар
    if (duration > 0) {
      const progressBar = notification.querySelector('.notification-progress');
      if (progressBar) {
        progressBar.style.animationDuration = `${duration}ms`;
      }

      // Автоматическое удаление через глобальную очередь
      this.scheduleRemoval(notification, duration);
    }

    return notification;
  }

  // Flexbox автоматически позиционирует уведомления

  // Глобальный таймер удален - используем индивидуальные таймеры

  createNotification(title, message, type, icon, closable) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Проверяем, является ли это Coming Soon уведомлением
    const isComingSoon = title.toLowerCase().includes('coming soon') || title.toLowerCase().includes('скоро');
    const titleClass = isComingSoon ? 'notification-title coming-soon' : 'notification-title';

    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-icon">${icon}</div>
        <div class="notification-text">
          <h4 class="${titleClass}">${title}</h4>
          <p class="notification-message">${message}</p>
        </div>
      </div>
      ${closable ? '<button class="notification-close">×</button>' : ''}
      <div class="notification-progress"></div>
    `;

    // Обработчик закрытия
    if (closable) {
      const closeBtn = notification.querySelector('.notification-close');
      closeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        notification.dataset.manualRemoval = 'true';
        this.remove(notification);
      });
    }

    // Клик по уведомлению для закрытия
    notification.addEventListener('click', () => {
      notification.dataset.manualRemoval = 'true';
      this.remove(notification);
    });

    // Добавляем поддержку свайпа для мобильных устройств
    this.addSwipeSupport(notification);

    return notification;
  }



  /**
   * Планирует последовательное удаление уведомлений сверху вниз
   */
  scheduleRemoval(notification, duration) {
    console.log(`⏰ Scheduling removal in ${duration}ms. Current notifications: ${this.notifications.length}`);

    // Отменяем предыдущий глобальный таймер
    if (this.globalRemovalTimer) {
      clearTimeout(this.globalRemovalTimer);
      console.log('🔄 Previous removal timer cancelled');
    }

    // Сбрасываем флаг активности при новом планировании
    this.isSequentialRemovalActive = false;

    // Устанавливаем новый таймер для начала последовательного удаления
    this.globalRemovalTimer = setTimeout(() => {
      console.log('⏰ Removal timer triggered, starting sequential removal');
      this.startSequentialRemoval();
    }, duration);
  }

  /**
   * Запускает последовательное удаление уведомлений сверху вниз
   */
  startSequentialRemoval() {
    // Проверяем, не запущен ли уже процесс удаления
    if (this.isSequentialRemovalActive) {
      console.log('⚠️ Sequential removal already active, skipping');
      return;
    }

    console.log(`🔄 Sequential removal started. Notifications count: ${this.notifications.length}`);

    if (this.notifications.length === 0) {
      console.log('✅ Sequential removal completed - no notifications left');
      this.isSequentialRemovalActive = false;
      return;
    }

    // Устанавливаем флаг активности
    this.isSequentialRemovalActive = true;

    // Создаем копию массива для безопасного удаления
    const notificationsToRemove = [...this.notifications];

    // Удаляем уведомления последовательно с задержкой
    this.removeNotificationsSequentially(notificationsToRemove);
  }

  /**
   * Удаляет уведомления последовательно без рекурсии
   */
  removeNotificationsSequentially(notificationsToRemove) {
    let currentIndex = 0; // Начинаем с первого в массиве (самое новое, но визуально верхнее из-за column-reverse)

    const removeNext = () => {
      if (currentIndex >= notificationsToRemove.length) {
        console.log('✅ Sequential removal completed');
        this.isSequentialRemovalActive = false;
        return;
      }

      const notification = notificationsToRemove[currentIndex];
      console.log(`🎯 Removing notification at index ${currentIndex} (visually top)`);

      if (notification && notification.parentElement && !notification.dataset.removing) {
        this.remove(notification);
        currentIndex++;
        setTimeout(removeNext, 200); // 200мс между удалениями
      } else {
        console.log('⚠️ Notification already removed or not found, skipping to next');
        currentIndex++;
        setTimeout(removeNext, 50); // Быстрее переходим к следующему
      }
    };

    removeNext();
  }

  remove(notification) {
    if (!notification || !notification.parentElement) {
      console.log('⚠️ Cannot remove: notification not found or not in DOM');
      return;
    }

    // Проверяем, не удаляется ли уже это уведомление
    if (notification.classList.contains('swipe-out') ||
        notification.classList.contains('fade-out') ||
        notification.dataset.removing === 'true') {
      console.log('⚠️ Notification already being removed, skipping');
      return;
    }

    // Проверяем, есть ли уведомление в массиве
    const index = this.notifications.indexOf(notification);
    if (index === -1) {
      console.log('⚠️ Notification not found in array, skipping');
      return;
    }

    // Помечаем как удаляемое
    notification.dataset.removing = 'true';
    console.log(`🗑️ Starting removal process for notification at index ${index}`);

    // Останавливаем прогресс-бар
    const progressBar = notification.querySelector('.notification-progress');
    if (progressBar) {
      progressBar.style.animationPlayState = 'paused';
    }

    // Определяем тип анимации: fade-out для автоматического удаления, swipe-out для ручного
    const isManualRemoval = notification.dataset.manualRemoval === 'true';
    const animationType = isManualRemoval ? 'swipe-out' : 'fade-out';

    console.log(`🔄 Starting ${animationType} animation for notification:`, notification.textContent?.substring(0, 30));

    // ЭТАП 1: Запускаем анимацию
    notification.style.willChange = 'transform, opacity';
    notification.classList.remove('show');
    notification.classList.add(animationType);

    // Принудительно применяем стили анимации через JavaScript
    notification.style.setProperty('transition', 'transform 0.4s ease-in, opacity 0.4s ease-in', 'important');
    notification.style.setProperty('transform', 'translateX(100%) translateZ(0)', 'important');
    notification.style.setProperty('opacity', '0', 'important');

    // ЭТАП 2: После завершения анимации - убираем из массива и DOM
    const animationDuration = 400; // Плавная анимация

    setTimeout(() => {
      const index = this.notifications.indexOf(notification);
      if (index > -1) {
        this.notifications.splice(index, 1);
        console.log(`📤 Notification removed from array. Remaining: ${this.notifications.length}`);
      } else {
        console.log('⚠️ Notification already removed from array');
      }

      if (notification.parentElement) {
        notification.remove();
        console.log('🗑️ Notification DOM element removed');
      }

      // Очищаем will-change для экономии памяти
      notification.style.willChange = 'auto';

      // Очищаем флаг удаления
      notification.dataset.removing = 'false';
    }, animationDuration);
  }

  removeOldest() {
    if (this.notifications.length > 0) {
      // С column-reverse: первый в массиве = верхний визуально (самый новый, но удаляем его как "самый старый")
      this.remove(this.notifications[0]);
    }
  }

  clear() {
    // Последовательное удаление сверху вниз (самые новые первыми визуально)
    const removeNext = () => {
      if (this.notifications.length > 0) {
        // С column-reverse: первый в массиве = верхний визуально
        this.remove(this.notifications[0]);
        setTimeout(removeNext, 100); // Быстрая задержка
      }
    };

    removeNext();
  }

  /**
   * ДОБАВЛЕНИЕ ПОДДЕРЖКИ СВАЙПА ДЛЯ МОБИЛЬНЫХ УСТРОЙСТВ
   */
  addSwipeSupport(notification) {
    let startX = 0;
    let currentX = 0;
    let isDragging = false;

    const handleTouchStart = (e) => {
      startX = e.touches[0].clientX;
      isDragging = true;
      notification.style.transition = 'none';
    };

    const handleTouchMove = (e) => {
      if (!isDragging) return;

      currentX = e.touches[0].clientX;
      const deltaX = currentX - startX;

      // Разрешаем только свайп вправо
      if (deltaX > 0) {
        const opacity = Math.max(0.3, 1 - (deltaX / 200));
        notification.style.transform = `translateX(${deltaX}px) translateZ(0)`;
        notification.style.opacity = opacity;
      }
    };

    const handleTouchEnd = (e) => {
      if (!isDragging) return;

      isDragging = false;
      const deltaX = currentX - startX;

      // Если свайп больше 100px - удаляем уведомление
      if (deltaX > 100) {
        notification.dataset.manualRemoval = 'true';
        this.remove(notification);
      } else {
        // Возвращаем на место
        notification.style.transition = 'transform 0.3s ease-out, opacity 0.3s ease-out';
        notification.style.transform = 'translateX(0) translateZ(0)';
        notification.style.opacity = '1';
      }
    };

    // Добавляем обработчики только для touch устройств
    if ('ontouchstart' in window) {
      notification.addEventListener('touchstart', handleTouchStart, { passive: true });
      notification.addEventListener('touchmove', handleTouchMove, { passive: true });
      notification.addEventListener('touchend', handleTouchEnd, { passive: true });
    }
  }

  getDefaultTitle(type) {
    const titles = {
      success: 'Успешно!',
      error: 'Ошибка!',
      warning: 'Внимание!',
      info: 'Информация'
    };
    return titles[type] || 'Уведомление';
  }

  getDefaultIcon(type) {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    return icons[type] || '📢';
  }

  // Публичные методы для удобства
  success(message, options = {}) {
    console.log('🔔 NotificationManager.success called:', message, options);
    return this.show(message, 'success', options);
  }

  error(message, options = {}) {
    return this.show(message, 'error', options);
  }

  warning(message, options = {}) {
    return this.show(message, 'warning', options);
  }

  info(message, options = {}) {
    return this.show(message, 'info', options);
  }

  // Метод для тестирования множественных уведомлений
  createTestNotifications(count = 5) {
    for (let i = 1; i <= count; i++) {
      setTimeout(() => {
        const types = ['success', 'info', 'warning', 'error'];
        const type = types[(i - 1) % types.length];
        this.show(`Тестовое уведомление ${i}`, type, {
          title: `Тест ${i}`,
          duration: 8000
        });
      }, (i - 1) * 300);
    }
  }

  // Тест последовательного clear с видимыми свайпами
  testSequentialClear() {
    console.log('🎬 Тест последовательного удаления');

    // Создаем 4 уведомления
    this.success('Уведомление 1', {title: 'Первое', duration: 20000});
    this.info('Уведомление 2', {title: 'Второе', duration: 20000});
    this.warning('Уведомление 3', {title: 'Третье', duration: 20000});
    this.error('Уведомление 4', {title: 'Четвертое', duration: 20000});

    console.log('✅ Создано 4 уведомления.');
    console.log('🎬 Через 2 секунды начнется последовательное удаление...');

    // Через 2 секунды запускаем clear
    setTimeout(() => {
      console.log('🎬 Запускаем clear() - смотрите как каждое улетает вправо!');
      this.clear();
    }, 2000);
  }

  // Тест видимого свайпа
  testVisibleSwipe() {
    console.log('🎬 Тест видимого свайпа');

    // Создаем 3 уведомления
    this.success('Первое уведомление - будет улетать', {title: 'Верхнее', duration: 15000});
    this.info('Второе уведомление - поднимется', {title: 'Среднее', duration: 15000});
    this.warning('Третье уведомление - тоже поднимется', {title: 'Нижнее', duration: 15000});

    console.log('✅ Создано 3 уведомления.');
    console.log('🎬 Через 2 секунды верхнее улетит вправо, а остальные поднимутся...');

    // Через 2 секунды удаляем верхнее
    setTimeout(() => {
      console.log('🎬 Смотрите! Верхнее уведомление улетает вправо!');
      if (this.notifications.length > 0) {
        this.remove(this.notifications[0]); // Первый в массиве = верхний визуально
      }
    }, 2000);

    // Через 5 секунд удаляем следующее
    setTimeout(() => {
      console.log('🎬 Теперь следующее улетает!');
      if (this.notifications.length > 0) {
        this.remove(this.notifications[0]); // Первый в массиве = верхний визуально
      }
    }, 5000);

    // Через 8 секунд последнее
    setTimeout(() => {
      console.log('🎬 И последнее!');
      if (this.notifications.length > 0) {
        this.remove(this.notifications[0]); // Первый в массиве = верхний визуально
      }
    }, 8000);
  }

  // Методы управления звуком
  setSoundEnabled(enabled) {
    this.soundEnabled = enabled;
    console.log(`🔊 Notification sounds ${enabled ? 'enabled' : 'disabled'}`);
  }

  playSound(type) {
    if (!this.soundEnabled) return;

    try {
      // Создаем звуковые сигналы с помощью Web Audio API
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      // Настройки звука в зависимости от типа уведомления
      const soundConfig = {
        success: { frequency: 800, duration: 150 },
        error: { frequency: 300, duration: 200 },
        warning: { frequency: 600, duration: 180 },
        info: { frequency: 500, duration: 120 }
      };

      const config = soundConfig[type] || soundConfig.info;

      oscillator.frequency.setValueAtTime(config.frequency, audioContext.currentTime);
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + config.duration / 1000);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + config.duration / 1000);
    } catch (error) {
      console.warn('🔇 Could not play notification sound:', error);
    }
  }
}

// Создаем глобальный экземпляр
export const notificationManager = new NotificationManager();

// Экспортируем для обратной совместимости
export default notificationManager;
