/**
 * ОПТИМИЗИРОВАННЫЕ УТИЛИТЫ ПРОИЗВОДИТЕЛЬНОСТИ
 * Базовые функции для кэширования, дебаунсинга и DOM операций
 * Интегрируется с QuantumMemoryOptimizer для унифицированного управления памятью
 */

import { PERFORMANCE_THRESHOLDS } from './performance-constants.js';
import { debounce } from './helpers.js';

/**
 * УНИФИЦИРОВАННОЕ КЭШИРОВАНИЕ DOM ЭЛЕМЕНТОВ
 * Использует новую унифицированную систему кэширования
 */
import { domCache } from './CacheMigrationAdapter.js';

export const getCachedElement = (selector) => {
  return domCache.getCachedElement(selector);
};

/**
 * ОЧИСТКА КЭША ЭЛЕМЕНТОВ
 */
export const clearElementCache = () => {
  domCache.clearElementCache();
};

// КВАНТОВО-ОПТИМИЗИРОВАННЫЙ ДЕБАУНСИНГ И ТРОТТЛИНГ
const debounceCache = new Map();
const throttleCache = new Map();

/**
 * ПРОСТОЙ ДЕБАУНСИНГ (для обратной совместимости)
 * Использует базовую функцию из helpers.js
 */
export const simpleDebounce = debounce;

/**
 * КВАНТОВЫЙ ДЕБАУНСИНГ С АДАПТИВНОЙ ЗАДЕРЖКОЙ
 * Применяет принципы квантовой неопределенности для оптимальной производительности
 */
export const getDebounced = (func, delay, key = func.name || Math.random().toString()) => {
  if (debounceCache.has(key)) {
    const cached = debounceCache.get(key);
    cached.lastUsed = Date.now();
    return cached.func;
  }

  let timeoutId;
  let callCount = 0;

  const debouncedFunc = (...args) => {
    callCount++;
    clearTimeout(timeoutId);

    // АДАПТИВНАЯ ЗАДЕРЖКА: уменьшаем для частых вызовов
    const adaptiveDelay = callCount > 10 ? delay * 0.7 : delay;

    timeoutId = setTimeout(() => {
      func.apply(null, args);
      callCount = Math.max(0, callCount - 1); // Постепенное снижение счетчика
    }, adaptiveDelay);
  };

  debounceCache.set(key, {
    func: debouncedFunc,
    created: Date.now(),
    lastUsed: Date.now(),
    callCount: 0
  });

  return debouncedFunc;
};

/**
 * КВАНТОВЫЙ ТРОТТЛИНГ С ИНТЕЛЛЕКТУАЛЬНЫМ КЭШИРОВАНИЕМ
 */
export const getThrottled = (func, delay, key = func.name || Math.random().toString()) => {
  if (throttleCache.has(key)) {
    const cached = throttleCache.get(key);
    cached.lastUsed = Date.now();
    return cached.func;
  }

  let lastCall = 0;
  let pendingArgs = null;
  let timeoutId = null;

  const throttledFunc = (...args) => {
    const now = Date.now();

    if (now - lastCall >= delay) {
      lastCall = now;
      return func.apply(null, args);
    } else {
      // Сохраняем последние аргументы для отложенного вызова
      pendingArgs = args;

      if (!timeoutId) {
        timeoutId = setTimeout(() => {
          if (pendingArgs) {
            lastCall = Date.now();
            func.apply(null, pendingArgs);
            pendingArgs = null;
          }
          timeoutId = null;
        }, delay - (now - lastCall));
      }
    }
  };

  throttleCache.set(key, {
    func: throttledFunc,
    created: Date.now(),
    lastUsed: Date.now()
  });

  return throttledFunc;
};

/**
 * Измерение времени выполнения (совместимо с performance-monitor)
 */
export const measureTime = (name, fn) => {
  // В режиме разработки используем расширенный мониторинг
  if (process.env.NODE_ENV === 'development') {
    // Простое измерение времени (performance-monitor удален)
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    if (duration > 10) { // Логируем только медленные операции
      console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
    }
    return result;
  }

  // В продакшене простое измерение
  const start = performance.now();
  const result = fn();
  const duration = performance.now() - start;
  if (duration > 16) { // Логируем только медленные операции
    console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
  }
  return result;
};

/**
 * Асинхронное измерение времени (совместимо с performance-monitor)
 */
export const measureTimeAsync = async (name, fn) => {
  // КВАНТОВАЯ ПРОВЕРКА СРЕДЫ РАЗРАБОТКИ (БЕЗ process.env)
  const isDevelopment = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname.includes('dev');

  // Простое асинхронное измерение времени (performance-monitor удален)
  if (isDevelopment) {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    if (duration > 10) { // Логируем только медленные операции
      console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
    }
    return result;
  }

  // В продакшене простое измерение
  const start = performance.now();
  const result = await fn();
  const duration = performance.now() - start;
  if (duration > 100) { // Логируем только медленные операции
    console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
  }
  return result;
};

/**
 * УЛЬТРА-ОПТИМИЗИРОВАННЫЙ БАТЧИНГ DOM ОПЕРАЦИЙ
 * Использует современные API для максимальной производительности
 */
export const batchDOMUpdates = (updates, options = {}) => {
  const {
    priority = 'user-blocking',
    maxBatchSize = 100,
    timeSlicing = true
  } = options;

  // Используем современный Scheduler API если доступен
  if ('scheduler' in window && window.scheduler.postTask) {
    return window.scheduler.postTask(() => {
      if (Array.isArray(updates)) {
        // Батчим операции для минимизации reflow/repaint
        const fragment = document.createDocumentFragment();
        updates.forEach(update => {
          if (typeof update === 'function') {
            update(fragment);
          }
        });
        return Promise.resolve(fragment);
      }
      return Promise.resolve();
    }, { priority });
  }

  // Fallback для старых браузеров
  return new Promise(resolve => {
    requestAnimationFrame(() => {
      if (Array.isArray(updates)) {
        updates.forEach(update => {
          if (typeof update === 'function') {
            update();
          }
        });
      }
      resolve();
    });
  });
};

/**
 * Ленивая загрузка функций
 */
export const lazy = (importFn) => {
  let cached = null;
  return async (...args) => {
    if (!cached) {
      cached = await importFn();
    }
    return cached.default ? cached.default(...args) : cached(...args);
  };
};

/**
 * Простой пул объектов
 */
export class ObjectPool {
  constructor(createFn, resetFn, initialSize = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    this.pool = [];
    
    // Предварительно создаем объекты
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn());
    }
  }
  
  get() {
    return this.pool.length > 0 ? this.pool.pop() : this.createFn();
  }
  
  release(obj) {
    if (this.resetFn) {
      this.resetFn(obj);
    }
    this.pool.push(obj);
  }
  
  clear() {
    this.pool.length = 0;
  }
}

/**
 * Простой кэш с TTL
 */
export class SimpleCache {
  constructor(ttl = 300000) { // 5 минут по умолчанию
    this.cache = new Map();
    this.ttl = ttl;
  }
  
  set(key, value) {
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }
  
  has(key) {
    return this.get(key) !== null;
  }
  
  clear() {
    this.cache.clear();
  }
  
  size() {
    return this.cache.size;
  }
}

// КВАНТОВО-ОПТИМИЗИРОВАННЫЕ ЭКЗЕМПЛЯРЫ
export const globalCache = new SimpleCache();
export const domElementPool = new ObjectPool(
  () => document.createElement('div'),
  (el) => {
    el.innerHTML = '';
    el.className = '';
    el.removeAttribute('style');
  }
);

/**
 * ЛЕГКОВЕСНАЯ СИСТЕМА ОЧИСТКИ КЭШЕЙ
 * Интегрируется с QuantumMemoryOptimizer для избежания дублирования
 */
class LightweightCacheManager {
  constructor() {
    this.cleanupInterval = null;
    this.isRunning = false;
  }

  start() {
    if (this.isRunning) return;

    this.isRunning = true;
    this.cleanupInterval = setInterval(() => {
      this.performLightCleanup();
    }, 120000); // Каждые 2 минуты (реже чем QuantumMemoryOptimizer)

    console.log('🧹 Lightweight Cache Manager started');
  }

  stop() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.isRunning = false;
  }

  performLightCleanup() {
    // Только базовая очистка DOM кэша
    if (elementCache.size > PERFORMANCE_THRESHOLDS.DOM_CACHE_SIZE) {
      cleanupDOMCache();
    }

    // Очистка дебаунс и троттл кэшей
    this.cleanupTimedCache(debounceCache);
    this.cleanupTimedCache(throttleCache);
  }

  cleanupTimedCache(cache) {
    const now = Date.now();
    const entries = Array.from(cache.entries());

    entries
      .filter(([_, cached]) => now - cached.lastUsed > 600000) // 10 минут неактивности
      .forEach(([key]) => cache.delete(key));
  }
}

// Удалено: quantumCacheManager - используется UnifiedCacheManager

// Утилиты для работы с производительностью
export const performanceUtils = {
  getCachedElement,
  clearElementCache,
  getDebounced,
  getThrottled,
  measureTime,
  measureTimeAsync,
  batchDOMUpdates,
  lazy,
  ObjectPool,
  SimpleCache,
  globalCache,
  domElementPool
};

export default performanceUtils;
