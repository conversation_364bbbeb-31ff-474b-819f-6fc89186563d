import { defineConfig } from 'vite';
import { visualizer } from 'rollup-plugin-visualizer';
import { fileURLToPath, URL } from 'node:url';

export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';
  const isAnalyze = mode === 'analyze';
  const isDevelopment = mode === 'development';

  return {
    plugins: [
      // Bundle analyzer - только в режиме анализа
      ...(isAnalyze ? [
        visualizer({
          open: true,
          gzipSize: true,
          brotliSize: true,
          filename: 'dist/stats.html',
          template: 'treemap',
          sourcemap: true,
        }),
      ] : []),
    ],
    server: {
      port: 3000,
      host: true,
      open: true,
      strictPort: true,
      cors: true,
      hmr: {
        overlay: false,
        // HMR использует тот же порт 3000 согласно требованиям пользователя
      },
      // Оптимизация для разработки
      fs: {
        strict: false,
      },
      // Обслуживание статических файлов
      middlewareMode: false,
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: !isProduction,
      minify: isProduction ? 'esbuild' : false,
      target: ['es2022', 'edge90', 'firefox85', 'chrome90', 'safari14.1'],
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // КВАНТОВО-ОПТИМИЗИРОВАННОЕ РАЗДЕЛЕНИЕ ЧАНКОВ
            if (id.includes('node_modules')) {
              // Критические UI фреймворки
              if (id.includes('daisyui') || id.includes('tailwindcss')) {
                return 'ui-framework';
              }
              // Тестовые утилиты (исключаем из продакшена)
              if (id.includes('vitest') || id.includes('jsdom')) {
                return isProduction ? undefined : 'test-utils';
              }
              // Большие библиотеки получают отдельные чанки
              if (id.includes('lodash') || id.includes('moment') || id.includes('date-fns')) {
                return 'heavy-libs';
              }
              // Полифиллы и утилиты
              if (id.includes('core-js') || id.includes('regenerator-runtime')) {
                return 'polyfills';
              }
              return 'vendor';
            }

            // Game data - separate chunk for better caching
            if (id.includes('/data/tanks') || id.includes('/data/equipment')) {
              return 'game-data';
            }

            // UI components - frequently updated
            if (id.includes('/components/')) {
              // Split large component groups
              if (id.includes('/tank-list/') || id.includes('/tank-details/')) {
                return 'tank-components';
              }
              if (id.includes('/equipment/') || id.includes('/builds/')) {
                return 'equipment-components';
              }
              return 'ui-components';
            }

            // Core services - stable, cacheable
            if (id.includes('/services/') || id.includes('/core/')) {
              return 'core-services';
            }

            // Utilities - frequently used
            if (id.includes('/utils/')) {
              // Performance utilities get priority
              if (id.includes('performance') || id.includes('cache') || id.includes('quantum')) {
                return 'performance-utils';
              }
              return 'utilities';
            }
          },
          entryFileNames: isProduction ? 'assets/[name]-[hash].js' : 'assets/[name].js',
          chunkFileNames: isProduction ? 'assets/[name]-[hash].js' : 'assets/[name].js',
          assetFileNames: isProduction ? 'assets/[name]-[hash].[ext]' : 'assets/[name].[ext]',
        },
        treeshake: {
          preset: 'recommended',
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          tryCatchDeoptimization: false,
          // Enhanced tree-shaking
          annotations: true,
          unknownGlobalSideEffects: false,
        },
        external: isProduction ? [] : ['vitest', 'jsdom'], // Exclude test deps in production
      },
      chunkSizeWarningLimit: 500, // Stricter limit for better performance
      cssCodeSplit: true,
      reportCompressedSize: isProduction,
      assetsInlineLimit: 8192, // Increased for better performance (8KB)

      // Дополнительные оптимизации
      emptyOutDir: true,
      copyPublicDir: true,

      // Оптимизация для современных браузеров
      polyfillModulePreload: false,

      // ULTRA-OPTIMIZED PRODUCTION SETTINGS
      ...(isProduction && {
        minify: 'esbuild',
        cssMinify: 'esbuild',
        modulePreload: {
          polyfill: false // Modern browsers only
        },
        // Advanced compression
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
            pure_funcs: ['console.log', 'console.info', 'console.debug'],
          },
        },
        // Optimize for modern browsers
        target: 'es2022',
        cssTarget: 'chrome90',
      }),

      // DEVELOPMENT OPTIMIZATIONS
      ...(isDevelopment && {
        minify: false,
        sourcemap: true,
        // Faster rebuilds
        watch: {
          exclude: ['node_modules/**', 'dist/**'],
        },
      }),
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@components': fileURLToPath(new URL('./src/components', import.meta.url)),
        '@utils': fileURLToPath(new URL('./src/utils', import.meta.url)),
        '@assets': fileURLToPath(new URL('./src/assets', import.meta.url)),
        '@data': fileURLToPath(new URL('./src/data', import.meta.url)),
        '@services': fileURLToPath(new URL('./src/services', import.meta.url)),
        '@store': fileURLToPath(new URL('./src/store', import.meta.url)),
        '@core': fileURLToPath(new URL('./src/core', import.meta.url)),
      },
    },
    optimizeDeps: {
      // ULTRA-OPTIMIZED DEPENDENCY PRE-BUNDLING
      exclude: [
        '@data/tanks',
        '@data/equipment',
        // Exclude test dependencies in production
        ...(isProduction ? ['vitest', 'jsdom', '@vitest/ui'] : [])
      ],
      include: [
        // Критические зависимости для предварительной сборки
        'daisyui',
        './src/utils/helpers.js',
        './src/services/FilterService.js',
        './src/utils/constants.js',
        './src/utils/performance.js',
        './src/utils/performance-constants.js',
        './src/utils/UltraResourceManager.js',
        './src/utils/NotificationManager.js',
        './src/core/QuantumPerformanceManager.js'
      ],
      esbuildOptions: {
        target: 'es2022',
        drop: isProduction ? ['console', 'debugger'] : [],
        minify: isProduction,
        format: 'esm',
        treeShaking: true,
        keepNames: !isProduction,
        legalComments: 'none',
        charset: 'utf8',
        // Enhanced optimization
        pure: ['console.log', 'console.info', 'console.debug'],
        ignoreAnnotations: false,
        platform: 'browser',
      },
      force: false, // Cache for better performance
      // Optimize dependency discovery
      entries: [
        './src/main.js',
        './src/core/AppInitializer.js',
      ],
    },
    css: {
      devSourcemap: !isProduction,
      modules: {
        localsConvention: 'camelCase',
      },
    },
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      __DEV__: !isProduction,
    },

    preview: {
      port: 3000,
      host: true,
      open: true,
    },
  };
});
