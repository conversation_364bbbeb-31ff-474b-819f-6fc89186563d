/**
 * МОДУЛЬ ОТЛАДОЧНЫХ КОМАНД
 * Выделен из main.js для улучшения структуры кода
 */

/**
 * ИНИЦИАЛИЗАЦИЯ ОТЛАДОЧНЫХ КОМАНД
 */
export function initializeDebugCommands(totalTime) {
  if (typeof window === 'undefined') return;

  // Базовые команды
  window.getMemoryStats = () => ({ status: 'basic', memory: 'optimized' });
  window.getPerformanceReport = () => ({ status: 'good', loadTime: totalTime });

  // Упрощенные команды оптимизации
  window.getStableMetrics = () => ({
    memory: { status: 'optimized' },
    performance: { loadTime: totalTime },
    stable: { status: 'active' }
  });

  window.optimizeNow = () => {
    console.log('🚀 Running basic optimizations...');
    // Базовая очистка
    if (window.gc) window.gc();
    console.log('✅ Basic optimizations complete');
  };

  window.showTankList = async () => {
    const { tankManager } = await import('./TankManager.js');
    tankManager.applyFiltersAndRenderTankList(false);
  };

  window.checkSystem = () => {
    const tankList = document.getElementById('tank-list');
    const tankContainer = document.getElementById('tank-characteristics-container');
    const tanks = tankList?.querySelectorAll('.tank-card') || [];

    return {
      status: 'OPTIMIZED',
      tankListFound: !!tankList,
      tankContainerFound: !!tankContainer,
      tankCount: tanks.length,
      memoryStats: { status: 'good' },
      performanceReport: { loadTime: 'fast' },
      systemStatus: { status: 'active' },
      timestamp: new Date().toISOString()
    };
  };

  window.restoreTankList = async () => {
    try {
      const { tankManager } = await import('./TankManager.js');
      await tankManager.applyFiltersAndRenderTankList(false);

      const tankList = document.getElementById('tank-list');
      if (tankList) {
        tankList.style.display = 'grid';
        tankList.style.visibility = 'visible';
        tankList.style.opacity = '1';
        tankList.classList.remove('hidden');
        tankList.classList.add('tank-list-visible');
      }

      return 'Tank list restored successfully';
    } catch (error) {
      return `Error: ${error.message}`;
    }
  };

  console.log('🧪 Debug commands initialized');
}

/**
 * ИНИЦИАЛИЗАЦИЯ ТЕСТОВ ПРОИЗВОДИТЕЛЬНОСТИ
 */
export async function initializePerformanceTests() {
  if (!import.meta.env.DEV) return;

  try {
    const { ultraPerformanceTests } = await import('../tests/UltraPerformanceTests.js');
    setTimeout(() => {
      ultraPerformanceTests.runAllTests();
    }, 2000); // Запускаем через 2 секунды после загрузки
  } catch (error) {
    console.warn('⚠️ Performance tests not available:', error);
  }
}

/**
 * ИНИЦИАЛИЗАЦИЯ ОБРАБОТЧИКОВ СОХРАНЕНИЯ СОСТОЯНИЯ
 */
export function initializeStateHandlers() {
  // Инициализируем сохранение состояния сравнения при закрытии страницы
  window.addEventListener('beforeunload', () => {
    if (window.unifiedCompareSystem) {
      window.unifiedCompareSystem.saveState();
    }
    if (window.newCompareSystem) {
      window.newCompareSystem.saveState();
    }
  });

  console.log('💾 State handlers initialized');
}
