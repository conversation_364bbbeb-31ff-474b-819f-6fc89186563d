/**
 * МЕНЕДЖЕР ИНТЕГРАЦИИ ВСЕХ СИСТЕМ
 * Объединяет все компоненты в единую систему сравнения танков
 */

import { ultraCompareSystem } from './UltraCompareSystem.js';
import { filterSortSystem } from './FilterSortSystem.js';
import { interactiveElements } from './InteractiveElements.js';
import { performanceOptimizer } from './PerformanceOptimizer.js';
import { responsiveManager } from './ResponsiveManager.js';
import { AdaptiveCompareTable } from './AdaptiveCompareTable.js';

export class IntegrationManager {
  constructor() {
    this.systems = new Map();
    this.isInitialized = false;
    this.currentMode = 'ultra'; // ultra, adaptive, classic
    this.debugMode = import.meta.env?.MODE === 'development';
    
    // Настройки интеграции
    this.settings = {
      enableUltraMode: true,
      enableAdaptiveTable: true,
      enableFilters: true,
      enableInteractivity: true,
      enablePerformanceOptimization: true,
      enableResponsiveDesign: true,
      autoSwitchMode: true // Автоматическое переключение режимов
    };
    
    // Состояние системы
    this.state = {
      tanks: [],
      filters: {},
      sortCriteria: [],
      selectedTanks: new Set(),
      viewMode: 'table', // table, cards, list
      isLoading: false,
      errors: []
    };
    
    this.initializeIntegration();
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ ИНТЕГРАЦИИ
   */
  async initializeIntegration() {
    try {
      console.log('🚀 Starting Integration Manager...');
      
      // Инициализируем системы в правильном порядке
      await this.initializeSystems();
      
      // Настраиваем взаимодействие между системами
      this.setupSystemInteractions();
      
      // Настраиваем обработчики событий
      this.setupEventHandlers();
      
      // Загружаем данные
      await this.loadInitialData();
      
      // Настраиваем автоматическое переключение режимов
      if (this.settings.autoSwitchMode) {
        this.setupAutoModeSwitch();
      }
      
      // Создаем интерфейс управления
      this.createControlInterface();
      
      this.isInitialized = true;
      
      console.log('✅ Integration Manager initialized successfully');
      this.logSystemStatus();
      
    } catch (error) {
      console.error('❌ Failed to initialize Integration Manager:', error);
      this.handleInitializationError(error);
    }
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ СИСТЕМ
   */
  async initializeSystems() {
    const initPromises = [];
    
    // Производительность (первым делом)
    if (this.settings.enablePerformanceOptimization) {
      this.systems.set('performance', performanceOptimizer);
      console.log('⚡ Performance Optimizer ready');
    }
    
    // Адаптивность
    if (this.settings.enableResponsiveDesign) {
      this.systems.set('responsive', responsiveManager);
      console.log('📱 Responsive Manager ready');
    }
    
    // Интерактивность
    if (this.settings.enableInteractivity) {
      this.systems.set('interactive', interactiveElements);
      console.log('✨ Interactive Elements ready');
    }
    
    // Фильтрация
    if (this.settings.enableFilters) {
      this.systems.set('filters', filterSortSystem);
      console.log('🔍 Filter System ready');
    }
    
    // Ультра-система сравнения
    if (this.settings.enableUltraMode) {
      this.systems.set('ultra', ultraCompareSystem);
      console.log('🌟 Ultra Compare System ready');
    }
    
    // Адаптивная таблица
    if (this.settings.enableAdaptiveTable) {
      const container = document.getElementById('compare-content');
      if (container) {
        this.systems.set('adaptive', new AdaptiveCompareTable(container));
        console.log('📊 Adaptive Table ready');
      }
    }
    
    await Promise.all(initPromises);
  }

  /**
   * НАСТРОЙКА ВЗАИМОДЕЙСТВИЯ СИСТЕМ
   */
  setupSystemInteractions() {
    // Связываем фильтры с таблицами
    if (this.systems.has('filters') && this.systems.has('adaptive')) {
      document.addEventListener('filtersChanged', (event) => {
        this.handleFiltersChanged(event.detail);
      });
    }
    
    // Связываем адаптивность с интерфейсом
    if (this.systems.has('responsive')) {
      responsiveManager.on('responsive:breakpoint', (event) => {
        this.handleBreakpointChange(event.detail);
      });
    }
    
    // Связываем производительность с рендерингом
    if (this.systems.has('performance')) {
      this.optimizeRenderingPipeline();
    }
    
    console.log('🔗 System interactions configured');
  }

  /**
   * НАСТРОЙКА ОБРАБОТЧИКОВ СОБЫТИЙ
   */
  setupEventHandlers() {
    // Глобальные обработчики
    document.addEventListener('tankSelected', (event) => {
      this.handleTankSelection(event.detail);
    });
    
    document.addEventListener('tankRemoved', (event) => {
      this.handleTankRemoval(event.detail);
    });
    
    document.addEventListener('compareMode', (event) => {
      this.switchMode(event.detail.mode);
    });
    
    // Обработчики ошибок
    window.addEventListener('error', (event) => {
      this.handleError(event.error);
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason);
    });
    
    console.log('📡 Event handlers configured');
  }

  /**
   * ЗАГРУЗКА НАЧАЛЬНЫХ ДАННЫХ
   */
  async loadInitialData() {
    this.state.isLoading = true;
    
    try {
      // Загружаем данные танков (симуляция)
      const tanks = await this.loadTanksData();
      this.state.tanks = tanks;
      
      // Восстанавливаем сохраненное состояние
      this.restoreState();
      
      // Обновляем интерфейс
      this.updateInterface();
      
    } catch (error) {
      console.error('Failed to load initial data:', error);
      this.state.errors.push(error);
    } finally {
      this.state.isLoading = false;
    }
  }

  /**
   * ЗАГРУЗКА ДАННЫХ ТАНКОВ
   */
  async loadTanksData() {
    // Симуляция загрузки данных
    return new Promise(resolve => {
      setTimeout(() => {
        resolve([
          { id: 1, name: 'T-34', country: 'ussr', type: 'medium', tier: 5 },
          { id: 2, name: 'Tiger I', country: 'germany', type: 'heavy', tier: 7 },
          { id: 3, name: 'Sherman', country: 'usa', type: 'medium', tier: 5 },
          { id: 4, name: 'IS-3', country: 'ussr', type: 'heavy', tier: 8 },
          { id: 5, name: 'Panther', country: 'germany', type: 'medium', tier: 7 }
        ]);
      }, 500);
    });
  }

  /**
   * НАСТРОЙКА АВТОМАТИЧЕСКОГО ПЕРЕКЛЮЧЕНИЯ РЕЖИМОВ
   */
  setupAutoModeSwitch() {
    if (this.systems.has('responsive')) {
      responsiveManager.on('responsive:breakpoint', (event) => {
        const { breakpoint } = event.detail;
        
        if (breakpoint === 'mobile') {
          this.switchMode('cards');
        } else if (breakpoint === 'tablet') {
          this.switchMode('adaptive');
        } else {
          this.switchMode('ultra');
        }
      });
    }
  }

  /**
   * СОЗДАНИЕ ИНТЕРФЕЙСА УПРАВЛЕНИЯ
   */
  createControlInterface() {
    const controlPanel = document.createElement('div');
    controlPanel.className = 'integration-control-panel';
    controlPanel.innerHTML = `
      <div class="control-header">
        <h3>System Control</h3>
        <div class="system-status">
          <span class="status-indicator ${this.isInitialized ? 'active' : 'inactive'}"></span>
          <span class="status-text">${this.isInitialized ? 'Active' : 'Inactive'}</span>
        </div>
      </div>
      
      <div class="control-sections">
        <div class="mode-controls">
          <label>View Mode:</label>
          <select id="view-mode-select">
            <option value="ultra">Ultra Mode</option>
            <option value="adaptive">Adaptive Table</option>
            <option value="cards">Card View</option>
          </select>
        </div>
        
        <div class="system-toggles">
          <label class="toggle-item">
            <input type="checkbox" id="toggle-filters" ${this.settings.enableFilters ? 'checked' : ''}>
            <span>Filters</span>
          </label>
          <label class="toggle-item">
            <input type="checkbox" id="toggle-interactive" ${this.settings.enableInteractivity ? 'checked' : ''}>
            <span>Interactive Elements</span>
          </label>
          <label class="toggle-item">
            <input type="checkbox" id="toggle-performance" ${this.settings.enablePerformanceOptimization ? 'checked' : ''}>
            <span>Performance Optimization</span>
          </label>
        </div>
        
        <div class="debug-controls" ${this.debugMode ? '' : 'style="display: none;"'}>
          <button id="debug-performance">Performance Report</button>
          <button id="debug-state">System State</button>
          <button id="debug-clear">Clear Cache</button>
        </div>
      </div>
    `;
    
    // Добавляем в интерфейс
    const container = document.getElementById('compare-content');
    if (container) {
      container.insertBefore(controlPanel, container.firstChild);
    }
    
    // Настраиваем обработчики
    this.setupControlHandlers(controlPanel);
  }

  /**
   * НАСТРОЙКА ОБРАБОТЧИКОВ УПРАВЛЕНИЯ
   */
  setupControlHandlers(panel) {
    // Переключение режимов
    const modeSelect = panel.querySelector('#view-mode-select');
    modeSelect.addEventListener('change', (e) => {
      this.switchMode(e.target.value);
    });
    
    // Переключатели систем
    panel.addEventListener('change', (e) => {
      if (e.target.type === 'checkbox') {
        this.handleSystemToggle(e.target.id, e.target.checked);
      }
    });
    
    // Отладочные кнопки
    if (this.debugMode) {
      panel.querySelector('#debug-performance')?.addEventListener('click', () => {
        this.showPerformanceReport();
      });
      
      panel.querySelector('#debug-state')?.addEventListener('click', () => {
        this.showSystemState();
      });
      
      panel.querySelector('#debug-clear')?.addEventListener('click', () => {
        this.clearAllCaches();
      });
    }
  }

  /**
   * ПЕРЕКЛЮЧЕНИЕ РЕЖИМОВ
   */
  switchMode(mode) {
    if (this.currentMode === mode) return;
    
    console.log(`🔄 Switching to ${mode} mode`);
    
    // Скрываем текущий режим
    this.hideCurrentMode();
    
    // Показываем новый режим
    this.showMode(mode);
    
    this.currentMode = mode;
    
    // Обновляем интерфейс
    this.updateModeInterface();
    
    // Сохраняем состояние
    this.saveState();
  }

  /**
   * СКРЫТИЕ ТЕКУЩЕГО РЕЖИМА
   */
  hideCurrentMode() {
    const containers = document.querySelectorAll(
      '.ultra-compare-container, .adaptive-compare-table, .mobile-cards-container'
    );
    
    containers.forEach(container => {
      container.style.display = 'none';
    });
  }

  /**
   * ПОКАЗ РЕЖИМА
   */
  showMode(mode) {
    switch (mode) {
      case 'ultra':
        this.showUltraMode();
        break;
      case 'adaptive':
        this.showAdaptiveMode();
        break;
      case 'cards':
        this.showCardsMode();
        break;
    }
  }

  /**
   * ПОКАЗ УЛЬТРА-РЕЖИМА
   */
  showUltraMode() {
    if (this.systems.has('ultra')) {
      const container = document.querySelector('.ultra-compare-container');
      if (container) {
        container.style.display = 'block';
      } else {
        // Создаем ультра-интерфейс
        ultraCompareSystem.createUltraPremiumInterface();
      }
    }
  }

  /**
   * ПОКАЗ АДАПТИВНОГО РЕЖИМА
   */
  showAdaptiveMode() {
    if (this.systems.has('adaptive')) {
      const container = document.querySelector('.adaptive-compare-table');
      if (container) {
        container.style.display = 'block';
        this.systems.get('adaptive').updateTable();
      }
    }
  }

  /**
   * ПОКАЗ РЕЖИМА КАРТОЧЕК
   */
  showCardsMode() {
    const container = document.querySelector('.mobile-cards-container');
    if (container) {
      container.style.display = 'flex';
    } else {
      // Создаем карточки
      this.createCardsInterface();
    }
  }

  /**
   * ОБРАБОТКА ИЗМЕНЕНИЯ ФИЛЬТРОВ
   */
  handleFiltersChanged(filterData) {
    console.log('🔍 Filters changed:', filterData);
    
    // Применяем фильтры к данным
    const filteredTanks = this.applyFilters(this.state.tanks, filterData);
    
    // Обновляем активные системы
    this.updateSystemsWithData(filteredTanks);
  }

  /**
   * ПРИМЕНЕНИЕ ФИЛЬТРОВ
   */
  applyFilters(tanks, filterData) {
    let filtered = [...tanks];
    
    // Поиск
    if (filterData.search) {
      filtered = filtered.filter(tank => 
        tank.name.toLowerCase().includes(filterData.search.toLowerCase())
      );
    }
    
    // Фильтры
    Object.keys(filterData.filters).forEach(key => {
      const filter = filterData.filters[key];
      if (filter.active) {
        if (filter.values && filter.values.length > 0) {
          filtered = filtered.filter(tank => filter.values.includes(tank[key]));
        }
      }
    });
    
    // Сортировка
    if (filterData.sort && filterData.sort.length > 0) {
      filtered.sort((a, b) => {
        for (const criteria of filterData.sort) {
          const aValue = a[criteria.key] || 0;
          const bValue = b[criteria.key] || 0;
          
          let comparison = 0;
          if (typeof aValue === 'string') {
            comparison = aValue.localeCompare(bValue);
          } else {
            comparison = aValue - bValue;
          }
          
          if (criteria.direction === 'desc') {
            comparison = -comparison;
          }
          
          if (comparison !== 0) return comparison;
        }
        return 0;
      });
    }
    
    return filtered;
  }

  /**
   * ОБНОВЛЕНИЕ СИСТЕМ С ДАННЫМИ
   */
  updateSystemsWithData(tanks) {
    // Обновляем ультра-систему
    if (this.systems.has('ultra') && this.currentMode === 'ultra') {
      // ultraCompareSystem.updateTanks(tanks);
    }
    
    // Обновляем адаптивную таблицу
    if (this.systems.has('adaptive') && this.currentMode === 'adaptive') {
      this.systems.get('adaptive').setTanks(tanks);
    }
    
    // Обновляем карточки
    if (this.currentMode === 'cards') {
      this.updateCardsInterface(tanks);
    }
  }

  /**
   * ОБРАБОТКА ОШИБОК
   */
  handleError(error) {
    console.error('System error:', error);
    this.state.errors.push({
      error,
      timestamp: Date.now(),
      context: this.currentMode
    });
    
    // Показываем уведомление пользователю
    this.showErrorNotification(error);
  }

  /**
   * ПОКАЗ УВЕДОМЛЕНИЯ ОБ ОШИБКЕ
   */
  showErrorNotification(error) {
    const notification = document.createElement('div');
    notification.className = 'error-notification';
    notification.innerHTML = `
      <div class="notification-content">
        <span class="error-icon">⚠️</span>
        <span class="error-message">System error occurred. Please refresh the page.</span>
        <button class="close-notification">×</button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Автоматическое скрытие через 5 секунд
    setTimeout(() => {
      notification.remove();
    }, 5000);
    
    // Обработчик закрытия
    notification.querySelector('.close-notification').addEventListener('click', () => {
      notification.remove();
    });
  }

  /**
   * ОТЧЕТ О ПРОИЗВОДИТЕЛЬНОСТИ
   */
  showPerformanceReport() {
    if (this.systems.has('performance')) {
      const metrics = performanceOptimizer.getPerformanceMetrics();
      console.table(metrics);
      
      // Показываем в интерфейсе
      const report = document.createElement('div');
      report.className = 'performance-report-modal';
      report.innerHTML = `
        <div class="modal-content">
          <h3>Performance Report</h3>
          <div class="metrics-grid">
            ${Object.entries(metrics).map(([key, value]) => `
              <div class="metric-item">
                <span class="metric-label">${key}:</span>
                <span class="metric-value">${JSON.stringify(value)}</span>
              </div>
            `).join('')}
          </div>
          <button class="close-modal">Close</button>
        </div>
      `;
      
      document.body.appendChild(report);
      
      report.querySelector('.close-modal').addEventListener('click', () => {
        report.remove();
      });
    }
  }

  /**
   * ПОКАЗ СОСТОЯНИЯ СИСТЕМЫ
   */
  showSystemState() {
    console.log('System State:', {
      currentMode: this.currentMode,
      systems: Array.from(this.systems.keys()),
      state: this.state,
      settings: this.settings
    });
  }

  /**
   * ОЧИСТКА ВСЕХ КЭШЕЙ
   */
  clearAllCaches() {
    this.systems.forEach(system => {
      if (system.clearCache) {
        system.clearCache();
      }
    });
    
    localStorage.removeItem('integrationManagerState');
    console.log('🧹 All caches cleared');
  }

  /**
   * СОХРАНЕНИЕ СОСТОЯНИЯ
   */
  saveState() {
    const stateToSave = {
      currentMode: this.currentMode,
      settings: this.settings,
      selectedTanks: Array.from(this.state.selectedTanks),
      timestamp: Date.now()
    };
    
    localStorage.setItem('integrationManagerState', JSON.stringify(stateToSave));
  }

  /**
   * ВОССТАНОВЛЕНИЕ СОСТОЯНИЯ
   */
  restoreState() {
    try {
      const savedState = localStorage.getItem('integrationManagerState');
      if (savedState) {
        const state = JSON.parse(savedState);
        
        this.currentMode = state.currentMode || 'ultra';
        this.settings = { ...this.settings, ...state.settings };
        this.state.selectedTanks = new Set(state.selectedTanks || []);
        
        console.log('🔄 State restored');
      }
    } catch (error) {
      console.error('Failed to restore state:', error);
    }
  }

  /**
   * ЛОГИРОВАНИЕ СТАТУСА СИСТЕМЫ
   */
  logSystemStatus() {
    console.log('📊 System Status:');
    console.log(`Mode: ${this.currentMode}`);
    console.log(`Systems: ${Array.from(this.systems.keys()).join(', ')}`);
    console.log(`Tanks: ${this.state.tanks.length}`);
    console.log(`Errors: ${this.state.errors.length}`);
  }

  /**
   * УНИЧТОЖЕНИЕ МЕНЕДЖЕРА
   */
  destroy() {
    // Уничтожаем все системы
    this.systems.forEach(system => {
      if (system.destroy) {
        system.destroy();
      }
    });
    
    // Сохраняем состояние
    this.saveState();
    
    // Очищаем обработчики
    document.removeEventListener('tankSelected', this.handleTankSelection);
    document.removeEventListener('tankRemoved', this.handleTankRemoval);
    
    console.log('🔄 Integration Manager destroyed');
  }
}

// Создаем и экспортируем глобальный экземпляр
export const integrationManager = new IntegrationManager();
