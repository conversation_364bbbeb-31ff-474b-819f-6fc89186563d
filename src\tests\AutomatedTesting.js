/**
 * АВТОМАТИЗИРОВАННАЯ СИСТЕМА ТЕСТИРОВАНИЯ
 * Запускает тесты автоматически и отслеживает регрессии
 */

import { testSuite } from './TestSuite.js';

export class AutomatedTesting {
  constructor() {
    this.isRunning = false;
    this.testHistory = [];
    this.regressionThreshold = 0.1; // 10% ухудшение считается регрессией
    this.autoRunInterval = null;
    this.observers = [];
  }

  /**
   * ЗАПУСК АВТОМАТИЧЕСКОГО ТЕСТИРОВАНИЯ
   */
  startAutomatedTesting(intervalMinutes = 30) {
    if (this.isRunning) {
      console.log('⚠️ Automated testing already running');
      return;
    }

    this.isRunning = true;
    console.log(`🤖 Starting automated testing (every ${intervalMinutes} minutes)`);

    // Запускаем первый тест сразу
    this.runTestCycle();

    // Настраиваем периодический запуск
    this.autoRunInterval = setInterval(() => {
      this.runTestCycle();
    }, intervalMinutes * 60 * 1000);
  }

  /**
   * ОСТАНОВКА АВТОМАТИЧЕСКОГО ТЕСТИРОВАНИЯ
   */
  stopAutomatedTesting() {
    if (!this.isRunning) return;

    this.isRunning = false;
    if (this.autoRunInterval) {
      clearInterval(this.autoRunInterval);
      this.autoRunInterval = null;
    }
    console.log('🛑 Automated testing stopped');
  }

  /**
   * ЗАПУСК ЦИКЛА ТЕСТИРОВАНИЯ
   */
  async runTestCycle() {
    console.log('🔄 Running automated test cycle...');
    
    try {
      const results = await testSuite.runAllTests();
      const testRun = {
        timestamp: Date.now(),
        results: results,
        metrics: this.collectMetrics(),
        environment: this.getEnvironmentInfo()
      };

      this.testHistory.push(testRun);
      this.analyzeResults(testRun);
      this.notifyObservers(testRun);

      // Ограничиваем историю последними 100 запусками
      if (this.testHistory.length > 100) {
        this.testHistory = this.testHistory.slice(-100);
      }

    } catch (error) {
      console.error('❌ Automated test cycle failed:', error);
    }
  }

  /**
   * АНАЛИЗ РЕЗУЛЬТАТОВ НА РЕГРЕССИИ
   */
  analyzeResults(currentRun) {
    if (this.testHistory.length < 2) return;

    const previousRun = this.testHistory[this.testHistory.length - 2];
    const regressions = this.detectRegressions(previousRun, currentRun);

    if (regressions.length > 0) {
      console.warn('⚠️ Performance regressions detected:');
      regressions.forEach(regression => {
        console.warn(`  - ${regression.test}: ${regression.change}% slower`);
      });
      
      this.handleRegressions(regressions);
    }
  }

  /**
   * ОБНАРУЖЕНИЕ РЕГРЕССИЙ
   */
  detectRegressions(previousRun, currentRun) {
    const regressions = [];

    for (const [testName, currentResult] of currentRun.results) {
      const previousResult = previousRun.results.get(testName);
      
      if (previousResult && currentResult.duration && previousResult.duration) {
        const change = (currentResult.duration - previousResult.duration) / previousResult.duration;
        
        if (change > this.regressionThreshold) {
          regressions.push({
            test: testName,
            change: (change * 100).toFixed(1),
            previous: previousResult.duration,
            current: currentResult.duration
          });
        }
      }
    }

    return regressions;
  }

  /**
   * ОБРАБОТКА РЕГРЕССИЙ
   */
  handleRegressions(regressions) {
    // Логируем в консоль
    console.group('🚨 PERFORMANCE REGRESSION ALERT');
    regressions.forEach(regression => {
      console.warn(`${regression.test}: ${regression.previous.toFixed(2)}ms → ${regression.current.toFixed(2)}ms (+${regression.change}%)`);
    });
    console.groupEnd();

    // Можно добавить отправку уведомлений, сохранение в файл и т.д.
  }

  /**
   * СБОР МЕТРИК СИСТЕМЫ
   */
  collectMetrics() {
    const metrics = {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };

    // Добавляем метрики памяти если доступны
    if (performance.memory) {
      metrics.memory = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }

    // Добавляем метрики производительности
    if (performance.getEntriesByType) {
      const navigationEntries = performance.getEntriesByType('navigation');
      if (navigationEntries.length > 0) {
        const nav = navigationEntries[0];
        metrics.navigation = {
          domContentLoaded: nav.domContentLoadedEventEnd - nav.domContentLoadedEventStart,
          loadComplete: nav.loadEventEnd - nav.loadEventStart
        };
      }
    }

    return metrics;
  }

  /**
   * ПОЛУЧЕНИЕ ИНФОРМАЦИИ ОБ ОКРУЖЕНИИ
   */
  getEnvironmentInfo() {
    return {
      url: window.location.href,
      timestamp: new Date().toISOString(),
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      },
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink
      } : null
    };
  }

  /**
   * ПОДПИСКА НА РЕЗУЛЬТАТЫ ТЕСТОВ
   */
  subscribe(callback) {
    this.observers.push(callback);
  }

  /**
   * ОТПИСКА ОТ РЕЗУЛЬТАТОВ ТЕСТОВ
   */
  unsubscribe(callback) {
    this.observers = this.observers.filter(obs => obs !== callback);
  }

  /**
   * УВЕДОМЛЕНИЕ НАБЛЮДАТЕЛЕЙ
   */
  notifyObservers(testRun) {
    this.observers.forEach(callback => {
      try {
        callback(testRun);
      } catch (error) {
        console.error('Error in test observer:', error);
      }
    });
  }

  /**
   * ПОЛУЧЕНИЕ СТАТИСТИКИ ТЕСТИРОВАНИЯ
   */
  getTestingStats() {
    if (this.testHistory.length === 0) {
      return { message: 'No test history available' };
    }

    const recentRuns = this.testHistory.slice(-10);
    const totalTests = recentRuns.reduce((sum, run) => sum + run.results.size, 0);
    const passedTests = recentRuns.reduce((sum, run) => {
      return sum + Array.from(run.results.values()).filter(r => r.passed).length;
    }, 0);

    return {
      totalRuns: this.testHistory.length,
      recentRuns: recentRuns.length,
      successRate: ((passedTests / totalTests) * 100).toFixed(1),
      isRunning: this.isRunning,
      lastRun: this.testHistory[this.testHistory.length - 1]?.timestamp
    };
  }

  /**
   * ЭКСПОРТ РЕЗУЛЬТАТОВ ТЕСТИРОВАНИЯ
   */
  exportResults() {
    const data = {
      exportTime: new Date().toISOString(),
      testHistory: this.testHistory,
      stats: this.getTestingStats()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `test-results-${Date.now()}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
    console.log('📁 Test results exported');
  }

  /**
   * БЫСТРЫЙ ТЕСТ ЗДОРОВЬЯ СИСТЕМЫ
   */
  async quickHealthCheck() {
    console.log('🏥 Running quick health check...');
    
    const healthTests = [
      () => this.checkStateManager(),
      () => this.checkCacheManager(),
      () => this.checkDOMElements(),
      () => this.checkMemoryUsage()
    ];

    const results = [];
    for (const test of healthTests) {
      try {
        const result = await test();
        results.push({ test: test.name, passed: true, result });
      } catch (error) {
        results.push({ test: test.name, passed: false, error: error.message });
      }
    }

    const healthScore = (results.filter(r => r.passed).length / results.length) * 100;
    console.log(`🏥 Health check complete: ${healthScore.toFixed(1)}% healthy`);
    
    return { healthScore, results };
  }

  // Простые проверки здоровья
  checkStateManager() {
    return typeof window.unifiedStateManager !== 'undefined';
  }

  checkCacheManager() {
    return typeof window.cacheManager !== 'undefined';
  }

  checkDOMElements() {
    const criticalElements = ['#app', '#tank-list', '#sidebar'];
    return criticalElements.every(selector => document.querySelector(selector));
  }

  checkMemoryUsage() {
    if (performance.memory) {
      const usage = performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit;
      return usage < 0.8; // Менее 80% от лимита
    }
    return true;
  }
}

// Создаем глобальный экземпляр
export const automatedTesting = new AutomatedTesting();

// Глобальный доступ для отладки
if (typeof window !== 'undefined') {
  window.automatedTesting = automatedTesting;
}
