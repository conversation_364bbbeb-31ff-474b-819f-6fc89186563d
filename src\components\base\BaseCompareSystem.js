/**
 * БАЗОВЫЙ КЛАСС ДЛЯ СИСТЕМ СРАВНЕНИЯ ТАНКОВ
 * Содержит общую функциональность для всех систем сравнения
 */

export class BaseCompareSystem {
  constructor(storageKey, maxTanks = 8) {
    this.tanks = new Array(maxTanks).fill(null);
    this.maxTanks = maxTanks;
    this.storageKey = storageKey;
  }

  /**
   * СОХРАНЕНИЕ СОСТОЯНИЯ В LOCALSTORAGE
   */
  saveState() {
    try {
      const state = {
        tanks: this.tanks.map(tank => tank ? {
          id: tank.id,
          name: tank.name,
          country: tank.country,
          type: tank.type,
          tier: tank.tier,
          shortId: tank.shortId
        } : null),
        timestamp: Date.now()
      };

      localStorage.setItem(this.storageKey, JSON.stringify(state));

      if (import.meta.env?.MODE === 'development') {
        console.log(`💾 ${this.constructor.name} state saved:`, state);
      }
    } catch (error) {
      console.error(`❌ Failed to save ${this.constructor.name} state:`, error);
    }
  }

  /**
   * ВОССТАНОВЛЕНИЕ СОСТОЯНИЯ ИЗ LOCALSTORAGE
   */
  restoreState() {
    try {
      const savedState = localStorage.getItem(this.storageKey);
      if (!savedState) return;

      const state = JSON.parse(savedState);

      // Проверяем, не слишком ли старое состояние (24 часа)
      const maxAge = 24 * 60 * 60 * 1000;
      if (Date.now() - state.timestamp > maxAge) {
        localStorage.removeItem(this.storageKey);
        return;
      }

      // Восстанавливаем танки
      if (state.tanks && Array.isArray(state.tanks)) {
        this.tanks = state.tanks.map(tankData => {
          if (!tankData) return null;

          // Пытаемся найти полные данные танка
          const fullTankData = this.findTankByName(tankData.name);
          if (fullTankData) {
            return { ...fullTankData, id: tankData.id };
          }

          // Если не нашли полные данные, используем сохраненные
          return tankData;
        });

        if (import.meta.env?.MODE === 'development') {
          const restoredCount = this.tanks.filter(tank => tank !== null).length;
          console.log(`🔄 ${this.constructor.name} state restored: ${restoredCount} tanks`);
        }
      }
    } catch (error) {
      console.error(`❌ Failed to restore ${this.constructor.name} state:`, error);
      localStorage.removeItem(this.storageKey);
    }
  }

  /**
   * ПОИСК ТАНКА ПО ИМЕНИ
   */
  findTankByName(tankName) {
    if (!window.tanksData || !Array.isArray(window.tanksData)) {
      console.warn('Tank data not available');
      return null;
    }

    return window.tanksData.find(tank => tank.name === tankName);
  }

  /**
   * ДОБАВЛЕНИЕ ТАНКА В СРАВНЕНИЕ
   */
  addTank(tank) {
    if (!tank) {
      console.warn('❌ Cannot add null tank');
      return false;
    }

    // Проверяем, не добавлен ли уже этот танк
    const existingIndex = this.tanks.findIndex(t => t && t.name === tank.name);
    if (existingIndex !== -1) {
      console.log(`⚠️ Tank ${tank.name} already in comparison`);
      return false;
    }

    // Находим первый свободный слот
    const freeSlotIndex = this.tanks.findIndex(slot => slot === null);
    if (freeSlotIndex === -1) {
      console.log('⚠️ No free slots available');
      return false;
    }

    // Добавляем танк
    this.tanks[freeSlotIndex] = { ...tank, id: Date.now() + Math.random() };
    this.saveState();

    if (import.meta.env?.MODE === 'development') {
      console.log(`✅ Tank ${tank.name} added to slot ${freeSlotIndex}`);
    }

    return true;
  }

  /**
   * УДАЛЕНИЕ ТАНКА ИЗ СРАВНЕНИЯ
   */
  removeTank(index) {
    if (index < 0 || index >= this.maxTanks) {
      console.warn('❌ Invalid tank index:', index);
      return false;
    }

    const removedTank = this.tanks[index];
    if (!removedTank) {
      console.warn('❌ No tank at index:', index);
      return false;
    }

    this.tanks[index] = null;
    this.saveState();

    if (import.meta.env?.MODE === 'development') {
      console.log(`🗑️ Tank ${removedTank.name} removed from slot ${index}`);
    }

    return true;
  }

  /**
   * ОЧИСТКА ВСЕХ ТАНКОВ
   */
  clearAll() {
    this.tanks.fill(null);
    this.saveState();
    
    if (import.meta.env?.MODE === 'development') {
      console.log('🧹 All tanks cleared from comparison');
    }
  }

  /**
   * ПОЛУЧЕНИЕ АКТИВНЫХ ТАНКОВ
   */
  getActiveTanks() {
    return this.tanks.filter(tank => tank !== null);
  }

  /**
   * ПОЛУЧЕНИЕ КОЛИЧЕСТВА АКТИВНЫХ ТАНКОВ
   */
  getActiveTankCount() {
    return this.tanks.filter(tank => tank !== null).length;
  }

  /**
   * ПРОВЕРКА, ДОБАВЛЕН ЛИ ТАНК
   */
  isTankAdded(tankName) {
    return this.tanks.some(tank => tank && tank.name === tankName);
  }

  /**
   * ДУБЛИРОВАНИЕ ТАНКА
   */
  duplicateTank(sourceIndex) {
    if (sourceIndex < 0 || sourceIndex >= this.maxTanks) {
      console.warn('❌ Invalid source index:', sourceIndex);
      return false;
    }

    const sourceTank = this.tanks[sourceIndex];
    if (!sourceTank) {
      console.warn('❌ No tank in source slot:', sourceIndex);
      return false;
    }

    const freeSlotIndex = this.tanks.findIndex(slot => slot === null);
    if (freeSlotIndex === -1) {
      console.warn('⚠️ No free slots available for duplication');
      return false;
    }

    this.tanks[freeSlotIndex] = { ...sourceTank, id: Date.now() + Math.random() };
    this.saveState();

    if (import.meta.env?.MODE === 'development') {
      console.log(`🔄 Tank ${sourceTank.name} duplicated to slot ${freeSlotIndex}`);
    }

    return true;
  }

  /**
   * СИНХРОНИЗАЦИЯ С ДРУГОЙ СИСТЕМОЙ СРАВНЕНИЯ
   */
  syncWithSystem(otherSystem) {
    if (!otherSystem || !otherSystem.tanks) return;

    const otherTanks = otherSystem.tanks.filter(tank => tank !== null);
    if (otherTanks.length === 0) return;

    otherTanks.forEach((tank, index) => {
      if (index < this.maxTanks && !this.tanks[index]) {
        this.tanks[index] = { ...tank };
      }
    });

    this.saveState();

    if (import.meta.env?.MODE === 'development') {
      console.log(`🔄 Synced ${otherTanks.length} tanks from other system`);
    }
  }
}
