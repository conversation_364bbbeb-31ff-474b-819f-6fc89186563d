/**
 * УЛЬТРА-ОПТИМИЗИРОВАННЫЙ БАЗОВЫЙ КЛАСС ДЛЯ СИСТЕМ СРАВНЕНИЯ ТАНКОВ
 * Современная архитектура с производительностью стоимостью 10 миллионов долларов
 */

export class BaseCompareSystem {
  constructor(storageKey, maxTanks = 12) {
    this.tanks = new Array(maxTanks).fill(null);
    this.maxTanks = maxTanks;
    this.storageKey = storageKey;

    // Кэш для оптимизации производительности
    this.cache = new Map();
    this.domCache = new Map();
    this.eventListeners = new Map();

    // Дебаунс для оптимизации обновлений
    this.updateDebounce = null;
    this.renderDebounce = null;

    // Флаги состояния
    this.isUpdating = false;
    this.isRendering = false;

    // Метрики производительности
    this.performanceMetrics = {
      renderTime: 0,
      updateTime: 0,
      cacheHits: 0,
      cacheMisses: 0
    };

    // Инициализация системы событий
    this.initializeEventSystem();
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ СИСТЕМЫ СОБЫТИЙ
   */
  initializeEventSystem() {
    // Создаем кастомные события для системы сравнения
    this.events = {
      tankAdded: new CustomEvent('tankAdded'),
      tankRemoved: new CustomEvent('tankRemoved'),
      tanksCleared: new CustomEvent('tanksCleared'),
      comparisonUpdated: new CustomEvent('comparisonUpdated'),
      stateChanged: new CustomEvent('stateChanged')
    };
  }

  /**
   * ЭМИССИЯ СОБЫТИЙ
   */
  emit(eventName, data = {}) {
    const event = new CustomEvent(eventName, { detail: data });
    document.dispatchEvent(event);
  }

  /**
   * ПОДПИСКА НА СОБЫТИЯ
   */
  on(eventName, callback) {
    const listener = (event) => callback(event.detail);
    document.addEventListener(eventName, listener);

    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, []);
    }
    this.eventListeners.get(eventName).push(listener);

    return () => document.removeEventListener(eventName, listener);
  }

  /**
   * ОПТИМИЗИРОВАННОЕ СОХРАНЕНИЕ СОСТОЯНИЯ В LOCALSTORAGE
   */
  saveState() {
    if (this.isUpdating) return; // Предотвращаем множественные сохранения

    try {
      const startTime = performance.now();

      const state = {
        tanks: this.tanks.map(tank => tank ? {
          id: tank.id,
          name: tank.name,
          country: tank.country,
          type: tank.type,
          tier: tank.tier,
          shortId: tank.shortId,
          level: tank.level,
          role: tank.role
        } : null),
        timestamp: Date.now(),
        version: '2.0'
      };

      // Сжимаем данные для экономии места
      const compressedState = this.compressState(state);
      localStorage.setItem(this.storageKey, JSON.stringify(compressedState));

      const saveTime = performance.now() - startTime;
      this.performanceMetrics.updateTime = saveTime;

      // Эмитируем событие изменения состояния
      this.emit('stateChanged', { tanks: this.getActiveTanks() });

      if (import.meta.env?.MODE === 'development') {
        console.log(`💾 ${this.constructor.name} state saved in ${saveTime.toFixed(2)}ms:`, state);
      }
    } catch (error) {
      console.error(`❌ Failed to save ${this.constructor.name} state:`, error);
    }
  }

  /**
   * СЖАТИЕ СОСТОЯНИЯ ДЛЯ ОПТИМИЗАЦИИ ХРАНЕНИЯ
   */
  compressState(state) {
    // Удаляем пустые слоты и дублирующиеся данные
    return {
      ...state,
      tanks: state.tanks.filter(tank => tank !== null)
    };
  }

  /**
   * ОПТИМИЗИРОВАННОЕ ВОССТАНОВЛЕНИЕ СОСТОЯНИЯ ИЗ LOCALSTORAGE
   */
  restoreState() {
    try {
      const startTime = performance.now();
      const savedState = localStorage.getItem(this.storageKey);

      if (!savedState) {
        this.initializeEmptyState();
        return;
      }

      const state = JSON.parse(savedState);

      // Проверяем, не слишком ли старое состояние (24 часа)
      const maxAge = 24 * 60 * 60 * 1000;
      if (Date.now() - state.timestamp > maxAge) {
        localStorage.removeItem(this.storageKey);
        this.initializeEmptyState();
        return;
      }

      // Проверяем версию состояния для обратной совместимости
      if (state.version === '2.0') {
        this.restoreCompressedState(state);
      } else {
        this.restoreLegacyState(state);
      }

      const restoreTime = performance.now() - startTime;
      this.performanceMetrics.updateTime = restoreTime;

      if (import.meta.env?.MODE === 'development') {
        const restoredCount = this.tanks.filter(tank => tank !== null).length;
        console.log(`🔄 ${this.constructor.name} state restored in ${restoreTime.toFixed(2)}ms: ${restoredCount} tanks`);
      }

      // Эмитируем событие восстановления состояния
      this.emit('stateRestored', { tanks: this.getActiveTanks() });

    } catch (error) {
      console.error(`❌ Failed to restore ${this.constructor.name} state:`, error);
      localStorage.removeItem(this.storageKey);
      this.initializeEmptyState();
    }
  }

  /**
   * ВОССТАНОВЛЕНИЕ СЖАТОГО СОСТОЯНИЯ
   */
  restoreCompressedState(state) {
    this.tanks = new Array(this.maxTanks).fill(null);

    state.tanks.forEach((tankData, index) => {
      if (index < this.maxTanks && tankData) {
        const fullTankData = this.findTankByName(tankData.name);
        this.tanks[index] = fullTankData ?
          { ...fullTankData, id: tankData.id } :
          tankData;
      }
    });
  }

  /**
   * ВОССТАНОВЛЕНИЕ УСТАРЕВШЕГО СОСТОЯНИЯ
   */
  restoreLegacyState(state) {
    if (state.tanks && Array.isArray(state.tanks)) {
      this.tanks = state.tanks.map(tankData => {
        if (!tankData) return null;

        const fullTankData = this.findTankByName(tankData.name);
        return fullTankData ?
          { ...fullTankData, id: tankData.id } :
          tankData;
      });

      // Дополняем массив до нужного размера
      while (this.tanks.length < this.maxTanks) {
        this.tanks.push(null);
      }
    }
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ ПУСТОГО СОСТОЯНИЯ
   */
  initializeEmptyState() {
    this.tanks = new Array(this.maxTanks).fill(null);
    this.cache.clear();
    this.domCache.clear();
  }

  /**
   * ПОИСК ТАНКА ПО ИМЕНИ
   */
  findTankByName(tankName) {
    if (!window.tanksData || !Array.isArray(window.tanksData)) {
      console.warn('Tank data not available');
      return null;
    }

    return window.tanksData.find(tank => tank.name === tankName);
  }

  /**
   * ОПТИМИЗИРОВАННОЕ ДОБАВЛЕНИЕ ТАНКА В СРАВНЕНИЕ
   */
  addTank(tank, slotIndex = null) {
    if (!tank) {
      console.warn('❌ Cannot add null tank');
      return false;
    }

    const startTime = performance.now();

    // Проверяем кэш для быстрого поиска
    const cacheKey = `tank_${tank.name}`;
    if (this.cache.has(cacheKey)) {
      this.performanceMetrics.cacheHits++;
      console.log(`⚠️ Tank ${tank.name} already in comparison (cached)`);
      return false;
    }
    this.performanceMetrics.cacheMisses++;

    // Проверяем, не добавлен ли уже этот танк
    const existingIndex = this.tanks.findIndex(t => t && t.name === tank.name);
    if (existingIndex !== -1) {
      this.cache.set(cacheKey, existingIndex);
      console.log(`⚠️ Tank ${tank.name} already in comparison`);
      return false;
    }

    // Находим слот для добавления
    let targetSlotIndex;
    if (slotIndex !== null && slotIndex >= 0 && slotIndex < this.maxTanks && !this.tanks[slotIndex]) {
      targetSlotIndex = slotIndex;
    } else {
      targetSlotIndex = this.tanks.findIndex(slot => slot === null);
    }

    if (targetSlotIndex === -1) {
      console.log('⚠️ No free slots available');
      return false;
    }

    // Добавляем танк с уникальным ID
    const tankWithId = {
      ...tank,
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      addedAt: Date.now()
    };

    this.tanks[targetSlotIndex] = tankWithId;

    // Обновляем кэш
    this.cache.set(cacheKey, targetSlotIndex);

    // Дебаунс сохранения состояния
    this.debouncedSaveState();

    const addTime = performance.now() - startTime;
    this.performanceMetrics.updateTime = addTime;

    // Эмитируем событие добавления танка
    this.emit('tankAdded', {
      tank: tankWithId,
      slotIndex: targetSlotIndex,
      totalTanks: this.getActiveTankCount()
    });

    if (import.meta.env?.MODE === 'development') {
      console.log(`✅ Tank ${tank.name} added to slot ${targetSlotIndex} in ${addTime.toFixed(2)}ms`);
    }

    return true;
  }

  /**
   * ДЕБАУНС ДЛЯ СОХРАНЕНИЯ СОСТОЯНИЯ
   */
  debouncedSaveState() {
    if (this.updateDebounce) {
      clearTimeout(this.updateDebounce);
    }

    this.updateDebounce = setTimeout(() => {
      this.saveState();
    }, 100);
  }

  /**
   * ОПТИМИЗИРОВАННОЕ УДАЛЕНИЕ ТАНКА ИЗ СРАВНЕНИЯ
   */
  removeTank(index) {
    if (index < 0 || index >= this.maxTanks) {
      console.warn('❌ Invalid tank index:', index);
      return false;
    }

    const removedTank = this.tanks[index];
    if (!removedTank) {
      console.warn('❌ No tank at index:', index);
      return false;
    }

    const startTime = performance.now();

    // Удаляем танк
    this.tanks[index] = null;

    // Очищаем кэш
    const cacheKey = `tank_${removedTank.name}`;
    this.cache.delete(cacheKey);

    // Очищаем DOM кэш для этого танка
    this.clearDomCacheForTank(removedTank.name);

    // Дебаунс сохранения состояния
    this.debouncedSaveState();

    const removeTime = performance.now() - startTime;
    this.performanceMetrics.updateTime = removeTime;

    // Эмитируем событие удаления танка
    this.emit('tankRemoved', {
      tank: removedTank,
      slotIndex: index,
      totalTanks: this.getActiveTankCount()
    });

    if (import.meta.env?.MODE === 'development') {
      console.log(`🗑️ Tank ${removedTank.name} removed from slot ${index} in ${removeTime.toFixed(2)}ms`);
    }

    return true;
  }

  /**
   * ОЧИСТКА DOM КЭША ДЛЯ ТАНКА
   */
  clearDomCacheForTank(tankName) {
    const keysToDelete = [];
    for (const [key] of this.domCache) {
      if (key.includes(tankName)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.domCache.delete(key));
  }

  /**
   * ОПТИМИЗИРОВАННАЯ ОЧИСТКА ВСЕХ ТАНКОВ
   */
  clearAll() {
    const startTime = performance.now();
    const tankCount = this.getActiveTankCount();

    this.tanks.fill(null);

    // Очищаем все кэши
    this.cache.clear();
    this.domCache.clear();

    // Дебаунс сохранения состояния
    this.debouncedSaveState();

    const clearTime = performance.now() - startTime;
    this.performanceMetrics.updateTime = clearTime;

    // Эмитируем событие очистки
    this.emit('tanksCleared', {
      clearedCount: tankCount,
      timestamp: Date.now()
    });

    if (import.meta.env?.MODE === 'development') {
      console.log(`🧹 All ${tankCount} tanks cleared from comparison in ${clearTime.toFixed(2)}ms`);
    }
  }

  /**
   * ПОЛУЧЕНИЕ АКТИВНЫХ ТАНКОВ С КЭШИРОВАНИЕМ
   */
  getActiveTanks() {
    const cacheKey = 'activeTanks';

    if (this.cache.has(cacheKey)) {
      this.performanceMetrics.cacheHits++;
      return this.cache.get(cacheKey);
    }

    this.performanceMetrics.cacheMisses++;
    const activeTanks = this.tanks.filter(tank => tank !== null);
    this.cache.set(cacheKey, activeTanks);

    return activeTanks;
  }

  /**
   * ПОЛУЧЕНИЕ КОЛИЧЕСТВА АКТИВНЫХ ТАНКОВ
   */
  getActiveTankCount() {
    return this.getActiveTanks().length;
  }

  /**
   * ПРОВЕРКА, ДОБАВЛЕН ЛИ ТАНК С КЭШИРОВАНИЕМ
   */
  isTankAdded(tankName) {
    const cacheKey = `tank_${tankName}`;

    if (this.cache.has(cacheKey)) {
      this.performanceMetrics.cacheHits++;
      return true;
    }

    this.performanceMetrics.cacheMisses++;
    const isAdded = this.tanks.some(tank => tank && tank.name === tankName);

    if (isAdded) {
      const index = this.tanks.findIndex(tank => tank && tank.name === tankName);
      this.cache.set(cacheKey, index);
    }

    return isAdded;
  }

  /**
   * ПОЛУЧЕНИЕ МЕТРИК ПРОИЗВОДИТЕЛЬНОСТИ
   */
  getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      cacheSize: this.cache.size,
      domCacheSize: this.domCache.size,
      activeTanks: this.getActiveTankCount(),
      maxTanks: this.maxTanks
    };
  }

  /**
   * ОЧИСТКА КЭША
   */
  clearCache() {
    this.cache.clear();
    this.domCache.clear();

    if (import.meta.env?.MODE === 'development') {
      console.log('🧹 Cache cleared');
    }
  }

  /**
   * ДУБЛИРОВАНИЕ ТАНКА
   */
  duplicateTank(sourceIndex) {
    if (sourceIndex < 0 || sourceIndex >= this.maxTanks) {
      console.warn('❌ Invalid source index:', sourceIndex);
      return false;
    }

    const sourceTank = this.tanks[sourceIndex];
    if (!sourceTank) {
      console.warn('❌ No tank in source slot:', sourceIndex);
      return false;
    }

    const freeSlotIndex = this.tanks.findIndex(slot => slot === null);
    if (freeSlotIndex === -1) {
      console.warn('⚠️ No free slots available for duplication');
      return false;
    }

    this.tanks[freeSlotIndex] = { ...sourceTank, id: Date.now() + Math.random() };
    this.saveState();

    if (import.meta.env?.MODE === 'development') {
      console.log(`🔄 Tank ${sourceTank.name} duplicated to slot ${freeSlotIndex}`);
    }

    return true;
  }

  /**
   * СИНХРОНИЗАЦИЯ С ДРУГОЙ СИСТЕМОЙ СРАВНЕНИЯ
   */
  syncWithSystem(otherSystem) {
    if (!otherSystem || !otherSystem.tanks) return;

    const otherTanks = otherSystem.tanks.filter(tank => tank !== null);
    if (otherTanks.length === 0) return;

    otherTanks.forEach((tank, index) => {
      if (index < this.maxTanks && !this.tanks[index]) {
        this.tanks[index] = { ...tank };
      }
    });

    this.saveState();

    if (import.meta.env?.MODE === 'development') {
      console.log(`🔄 Synced ${otherTanks.length} tanks from other system`);
    }
  }
}
