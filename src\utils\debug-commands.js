/**
 * ОТЛАДОЧНЫЕ КОМАНДЫ ДЛЯ РАЗРАБОТКИ
 */

// Команды для тестирования системы сравнения
window.debugCompare = {
  // Проверить состояние системы
  status() {
    console.log('🔍 Compare System Status:');
    console.log('Advanced System:', !!window.advancedCompareSystem);
    console.log('Old System:', !!window.compareSystem);
    console.log('Floating Window:', !!document.getElementById('compare-floating-window'));
    
    if (window.advancedCompareSystem) {
      const activeTanks = window.advancedCompareSystem.tanks.filter(t => t !== null);
      console.log('Active Tanks:', activeTanks.length);
      console.log('Tanks:', activeTanks.map(t => t?.name || 'null'));
    }
  },

  // Добавить тестовый танк
  addTestTank(name = 'Test Tank') {
    if (!window.advancedCompareSystem) {
      console.error('❌ Advanced compare system not available');
      return;
    }

    const testTank = {
      name: name,
      level: 10,
      type: 'ТТ',
      country: 'USSR',
      characteristics: {
        hp: 2000,
        damage: 400,
        penetration: 250,
        rateOfFire: 5.5,
        aimTime: 2.3,
        accuracy: 0.35,
        maxSpeed: 50,
        enginePower: 750,
        powerToWeight: 15.5,
        traverse: 28
      }
    };

    const result = window.advancedCompareSystem.addTank(testTank);
    console.log(`✅ Test tank "${name}" added:`, result);
    return result;
  },

  // Очистить все танки
  clear() {
    if (window.advancedCompareSystem) {
      window.advancedCompareSystem.clearAll();
      console.log('✅ All tanks cleared');
    }
  },

  // Показать всплывающее окно
  showWindow() {
    if (window.advancedCompareSystem) {
      window.advancedCompareSystem.showFloatingWindow();
      console.log('✅ Floating window shown');
    }
  },

  // Скрыть всплывающее окно
  hideWindow() {
    if (window.advancedCompareSystem) {
      window.advancedCompareSystem.hideFloatingWindow();
      console.log('✅ Floating window hidden');
    }
  },

  // Запустить тесты
  async test() {
    if (window.testCompareSystem) {
      console.log('🧪 Running compare system tests...');
      return await window.testCompareSystem();
    } else {
      console.error('❌ Test function not available');
    }
  },

  // Добавить несколько тестовых танков
  addMultiple(count = 3) {
    for (let i = 1; i <= count; i++) {
      this.addTestTank(`Test Tank ${i}`);
    }
    console.log(`✅ Added ${count} test tanks`);
  },

  // Показать результаты сравнения
  showResults() {
    if (window.advancedCompareSystem) {
      window.advancedCompareSystem.showResults();
      console.log('✅ Comparison results shown');
    }
  },

  // Принудительно добавить бейджики
  addBadges() {
    if (window.advancedCompareSystem) {
      window.advancedCompareSystem.addBadgesToExistingCards();
      console.log('✅ Compare badges added to all tank cards');
    }
  },

  // Проверить бейджики и их обработчики
  checkBadges() {
    const badges = document.querySelectorAll('.tank-compare-badge');
    console.log(`🔍 Found ${badges.length} compare badges`);

    const withListeners = Array.from(badges).filter(b => b.hasAttribute('data-listener-attached')).length;
    console.log(`🔗 Badges with listeners: ${withListeners}/${badges.length}`);

    // Показываем первые 3 для примера
    badges.forEach((badge, index) => {
      if (index < 3) {
        const tankCard = badge.closest('.tank-item');
        const tankName = tankCard?.dataset.tankName || 'Unknown';
        const hasListener = badge.hasAttribute('data-listener-attached');
        console.log(`Badge ${index + 1}: ${tankName}, Listener: ${hasListener}`);
      }
    });
  },

  // Тестовый клик по первому бейджику
  testClick() {
    const firstBadge = document.querySelector('.tank-compare-badge');
    if (firstBadge) {
      console.log('🧪 Testing click on first badge...');
      firstBadge.click();
    } else {
      console.log('❌ No badges found');
    }
  },

  // Тестовое уведомление
  testNotification() {
    if (window.notificationManager) {
      window.notificationManager.success('Тестовое уведомление работает!', {
        title: 'Тест',
        duration: 3000
      });
      console.log('✅ Test notification sent');
    } else {
      console.error('❌ NotificationManager not available');
    }
  },

  // Дублирование удалено

  // Помощь
  help() {
    console.log(`
🔧 Debug Commands for Compare System:

debugCompare.status()           - Check system status
debugCompare.addTestTank(name)  - Add a test tank
debugCompare.addMultiple(count) - Add multiple test tanks
debugCompare.addBadges()        - Add compare badges to all cards
debugCompare.clear()            - Clear all tanks
debugCompare.showWindow()       - Show floating window
debugCompare.hideWindow()       - Hide floating window
debugCompare.showResults()      - Show comparison results
debugCompare.test()             - Run automated tests
debugCompare.checkBadges()      - Check badges and their listeners
debugCompare.testNotification() - Test notification system
debugCompare.help()             - Show this help

Example usage:
debugCompare.addBadges();
debugCompare.testNotification();
debugCompare.checkBadges();
debugCompare.addMultiple(3);
debugCompare.showResults();
    `);
  }
};

// Команды для общей отладки
window.debugApp = {
  // Проверить состояние приложения
  status() {
    console.log('🔍 App Status:');
    console.log('Tank Manager:', !!window.tankManager);
    console.log('Filter Manager:', !!window.filterManager);
    console.log('Notification Manager:', !!window.notificationManager);
    console.log('Advanced Compare System:', !!window.advancedCompareSystem);
    console.log('Compare System:', !!window.compareSystem);
  },

  // Перезагрузить CSS
  reloadCSS() {
    const links = document.querySelectorAll('link[rel="stylesheet"]');
    links.forEach(link => {
      const href = link.href;
      link.href = href + '?t=' + Date.now();
    });
    console.log('✅ CSS reloaded');
  },

  // Показать все танки
  showTanks() {
    if (window.tankManager && window.tankManager.allTanks) {
      console.log('🚗 Available tanks:', window.tankManager.allTanks.length);
      console.table(window.tankManager.allTanks.slice(0, 10));
    }
  },

  // Помощь
  help() {
    console.log(`
🔧 Debug Commands for App:

debugApp.status()      - Check app status
debugApp.reloadCSS()   - Reload all CSS files
debugApp.showTanks()   - Show available tanks
debugApp.help()        - Show this help

debugCompare.help()    - Show compare system commands
    `);
  }
};

// Автоматически показать помощь при загрузке в dev режиме
try {
  // Показываем сообщение только в режиме разработки
  if (import.meta.env?.MODE === 'development') {
    console.log('🔧 Debug commands loaded! Type debugApp.help() or debugCompare.help() for available commands.');
  }
} catch (e) {
  // Игнорируем ошибки в production
}

// Экспортируем объекты
export const debugCompareExport = window.debugCompare;
export const debugAppExport = window.debugApp;
