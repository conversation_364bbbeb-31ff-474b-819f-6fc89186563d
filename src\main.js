/**
 * ОПТИМИЗИРОВАННАЯ ТОЧКА ВХОДА ПРИЛОЖЕНИЯ
 * Упрощенная инициализация с модульной архитектурой
 */

// Service Worker management for development
if ('serviceWorker' in navigator && window.location.hostname === 'localhost') {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(let registration of registrations) {
      registration.unregister();
    }
  });
}

window.MAIN_JS_PRIORITY = true;
window.MAIN_JS_LOADING = true;
document.title = 'Efficiency - Loading...';

// Импортируем модули инициализации
import { simplifiedInitializer } from './core/SimplifiedInitializer.js';
import './utils/GlobalCleanupManager.js'; // Инициализируем глобальный менеджер очистки
import {
  initializeCoreServices,
  loadApplicationData,
  initializeUIComponents,
  determineInitialSection,
  initializeFiltersAndComponents,
  initializeAdditionalSystems,
  finalizeInitialization
} from './core/AppInitializer.js';
import {
  initializeDebugCommands,
  initializePerformanceTests,
  initializeStateHandlers
} from './core/DebugCommands.js';

// Настраиваем прогресс
simplifiedInitializer.setupProgressTracking();
simplifiedInitializer.updateProgress(5, 'Main.js загружен');

// МОДУЛЬНАЯ ИНИЦИАЛИЗАЦИЯ
(async () => {
  try {
    const startTime = performance.now();

    // ОПТИМИЗИРОВАННАЯ ИНИЦИАЛИЗАЦИЯ - ТОЛЬКО НЕОБХОДИМЫЕ КОМПОНЕНТЫ
    console.log('🚀 Starting modular initialization...');

    // Проверяем URL для танков
    const pathname = window.location.pathname;
    const isTankPage = pathname.startsWith('/vehicles/') && pathname !== '/vehicles';
    const isVehiclesPage = pathname === '/vehicles' || isTankPage;

    if (isTankPage) {
      document.body.setAttribute('data-tank-page', 'true');
    } else {
      document.body.removeAttribute('data-tank-page');
    }

    if (isVehiclesPage) {
      localStorage.setItem('activeMenuItem', 'vehicles');
      document.body.setAttribute('data-active-section', 'vehicles');

      // Предотвращаем мигание списка танков
      if (pathname === '/vehicles') {
        const tankListElement = document.getElementById('tank-list');
        if (tankListElement) {
          tankListElement.style.display = 'grid';
          tankListElement.style.visibility = 'visible';
          tankListElement.style.opacity = '1';
          tankListElement.style.transform = 'translateY(0)';
          tankListElement.classList.remove('hidden', 'tank-list-hidden');
          tankListElement.classList.add('tank-list-visible', 'instant-show');
        }
      }

      if (isTankPage) {
        sessionStorage.setItem('tankPageRefresh', 'true');
      }
    }

    // Очистка старых данных
    const savedTank = localStorage.getItem('selectedTank');
    const isNewPageLoad = !savedTank && (
      !window.performance.getEntriesByType ||
      window.performance.getEntriesByType('navigation')[0]?.type === 'navigate'
    );
    if (isNewPageLoad) {
      localStorage.removeItem('filtersBeforeSelection');
    }

    // Инициализация основных сервисов
    await initializeCoreServices();

    // Загрузка данных приложения
    await loadApplicationData();

    // Инициализация UI компонентов
    const currentPath = window.location.pathname;
    const { onMenuSelected, initializeOverviewHandlers } = await initializeUIComponents(currentPath);

    // Определение начальной секции
    const initialSection = determineInitialSection(currentPath);

    // Активация начальной секции
    const validSectionName = initialSection.startsWith('vehicles/') ? 'vehicles' : initialSection;
    onMenuSelected(validSectionName, true);

    // Инициализация фильтров и компонентов
    await initializeFiltersAndComponents(initialSection);

    // Initialize Overview handlers
    initializeOverviewHandlers();
    console.log('✅ Overview handlers initialized');

    // Инициализация дополнительных систем
    await initializeAdditionalSystems();

    // Завершение инициализации
    const totalTime = await finalizeInitialization(startTime, initialSection);

    // Инициализация отладочных команд
    initializeDebugCommands(totalTime);

    // Инициализация обработчиков состояния
    initializeStateHandlers();

    // Запуск тестов производительности
    await initializePerformanceTests();

    console.log('🎉 MODULAR APPLICATION FULLY LOADED!');
  } catch (error) {
    console.error('[Init] ❌ Error during initialization:', error);
    console.error('[Init] ❌ Error stack:', error.stack);
    console.error('[Init] ❌ Error name:', error.name);
    console.error('[Init] ❌ Error message:', error.message);

    // Показываем сообщение об ошибке
    const errorHTML = `
      <div id="app-error-overlay" class="error-overlay">
        <div class="error-container">
          <div class="error-icon">⚠️</div>
          <div class="error-text">Ошибка инициализации приложения</div>
          <button onclick="location.reload()" class="error-button">Обновить страницу</button>
        </div>
      </div>
    `;
    document.body.insertAdjacentHTML('beforeend', errorHTML);
  }
})();
