/**
 * КОМПЛЕКСНАЯ СИСТЕМА ТЕСТИРОВАНИЯ
 * Объединяет unit-тесты, интеграционные тесты и тесты производительности
 */

import { unifiedStateManager } from '../store/UltraStateManager.js';
import { cacheManager } from '../utils/CacheMigrationAdapter.js';

export class TestSuite {
  constructor() {
    this.results = new Map();
    this.startTime = 0;
    this.thresholds = {
      unitTestTime: 10,       // 10мс на unit-тест
      integrationTime: 100,   // 100мс на интеграционный тест
      performanceTime: 50,    // 50мс на тест производительности
      memoryUsage: 50,        // 50MB
      cacheHitRate: 0.8       // 80%
    };
  }

  /**
   * ЗАПУСК ВСЕХ ТЕСТОВ
   */
  async runAllTests() {
    console.log('🧪 Starting comprehensive test suite...');
    this.startTime = performance.now();

    try {
      await this.runUnitTests();
      await this.runIntegrationTests();
      await this.runPerformanceTests();
      
      this.generateReport();
      return this.results;
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      return { error: error.message };
    }
  }

  /**
   * UNIT-ТЕСТЫ
   */
  async runUnitTests() {
    console.log('📋 Running unit tests...');

    const unitTests = [
      () => this.testStateManager(),
      () => this.testCacheManager(),
      () => this.testUtilityFunctions(),
      () => this.testDataValidation()
    ];

    for (const test of unitTests) {
      await this.runTest(test, 'unit');
    }
  }

  /**
   * ИНТЕГРАЦИОННЫЕ ТЕСТЫ
   */
  async runIntegrationTests() {
    console.log('🔗 Running integration tests...');

    const integrationTests = [
      () => this.testStateAndCacheIntegration(),
      () => this.testUIComponentIntegration(),
      () => this.testRouterIntegration(),
      () => this.testFilterSystemIntegration()
    ];

    for (const test of integrationTests) {
      await this.runTest(test, 'integration');
    }
  }

  /**
   * ТЕСТЫ ПРОИЗВОДИТЕЛЬНОСТИ
   */
  async runPerformanceTests() {
    console.log('⚡ Running performance tests...');

    const performanceTests = [
      () => this.testRenderPerformance(),
      () => this.testMemoryUsage(),
      () => this.testCachePerformance(),
      () => this.testSearchPerformance()
    ];

    for (const test of performanceTests) {
      await this.runTest(test, 'performance');
    }
  }

  /**
   * ВЫПОЛНЕНИЕ ОТДЕЛЬНОГО ТЕСТА
   */
  async runTest(testFunction, category) {
    const testName = testFunction.name;
    const startTime = performance.now();

    try {
      const result = await testFunction();
      const duration = performance.now() - startTime;
      const threshold = this.thresholds[`${category}Time`];
      const passed = duration < threshold && (result === undefined || result === true);

      this.results.set(testName, {
        category,
        passed,
        duration,
        threshold,
        result,
        score: this.calculateScore(duration, threshold)
      });

      console.log(`${passed ? '✅' : '❌'} ${testName}: ${duration.toFixed(2)}ms`);
    } catch (error) {
      this.results.set(testName, {
        category,
        passed: false,
        error: error.message,
        duration: performance.now() - startTime
      });
      console.error(`❌ ${testName} failed:`, error.message);
    }
  }

  /**
   * ТЕСТ МЕНЕДЖЕРА СОСТОЯНИЯ
   */
  testStateManager() {
    // Тестируем базовые операции
    unifiedStateManager.set('testKey', 'testValue');
    const value = unifiedStateManager.get('testKey');
    
    if (value !== 'testValue') {
      throw new Error('State manager get/set failed');
    }

    // Тестируем computed значения
    const computed = unifiedStateManager.computed('test', () => 'computed', []);
    if (computed !== 'computed') {
      throw new Error('Computed values failed');
    }

    // Очищаем тестовые данные
    unifiedStateManager.set('testKey', undefined);
    
    return true;
  }

  /**
   * ТЕСТ МЕНЕДЖЕРА КЭША
   */
  testCacheManager() {
    // Тестируем DOM кэш
    const testElement = document.createElement('div');
    testElement.id = 'test-element';
    document.body.appendChild(testElement);

    const cached = cacheManager.dom.getCachedElement('#test-element');
    if (!cached || cached !== testElement) {
      throw new Error('DOM cache failed');
    }

    // Тестируем data кэш
    cacheManager.data.cacheTankData('test-tank', { name: 'Test Tank' });
    const tankData = cacheManager.data.getTankData('test-tank');
    
    if (!tankData || tankData.name !== 'Test Tank') {
      throw new Error('Data cache failed');
    }

    // Очищаем тестовые данные
    document.body.removeChild(testElement);
    cacheManager.data.clearAll();
    
    return true;
  }

  /**
   * ТЕСТ УТИЛИТАРНЫХ ФУНКЦИЙ
   */
  testUtilityFunctions() {
    // Тестируем создание ID танка
    const testName = 'Test Tank Name';
    const id1 = this.createTestId(testName);
    const id2 = this.createTestId(testName);
    
    if (id1 !== id2) {
      throw new Error('Utility function consistency failed');
    }

    return true;
  }

  /**
   * ТЕСТ ВАЛИДАЦИИ ДАННЫХ
   */
  testDataValidation() {
    const validTank = {
      name: 'T-34',
      country: 'USSR',
      type: 'mediumTank',
      tier: 'V'
    };

    const invalidTank = {
      name: '',
      country: null
    };

    if (!this.validateTankData(validTank)) {
      throw new Error('Valid tank data validation failed');
    }

    if (this.validateTankData(invalidTank)) {
      throw new Error('Invalid tank data validation failed');
    }

    return true;
  }

  /**
   * ТЕСТ ИНТЕГРАЦИИ СОСТОЯНИЯ И КЭША
   */
  testStateAndCacheIntegration() {
    // Тестируем синхронизацию между состоянием и кэшем
    const testData = { test: 'integration' };
    
    unifiedStateManager.set('cacheTest', testData);
    cacheManager.data.cacheComputed('stateTest', testData);
    
    const stateData = unifiedStateManager.get('cacheTest');
    const cacheData = cacheManager.data.getComputed('stateTest');
    
    if (JSON.stringify(stateData) !== JSON.stringify(cacheData)) {
      throw new Error('State and cache integration failed');
    }

    return true;
  }

  /**
   * ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ РЕНДЕРИНГА
   */
  testRenderPerformance() {
    const startTime = performance.now();
    
    // Симулируем рендеринг множества элементов
    const fragment = document.createDocumentFragment();
    for (let i = 0; i < 100; i++) {
      const div = document.createElement('div');
      div.textContent = `Item ${i}`;
      fragment.appendChild(div);
    }
    
    const renderTime = performance.now() - startTime;
    
    if (renderTime > 10) { // 10мс для 100 элементов
      throw new Error(`Render performance too slow: ${renderTime}ms`);
    }

    return true;
  }

  /**
   * ТЕСТ ИСПОЛЬЗОВАНИЯ ПАМЯТИ
   */
  testMemoryUsage() {
    if (performance.memory) {
      const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024; // MB
      
      if (memoryUsage > this.thresholds.memoryUsage) {
        throw new Error(`Memory usage too high: ${memoryUsage.toFixed(2)}MB`);
      }
    }

    return true;
  }

  /**
   * ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
   */
  createTestId(name) {
    return name.toLowerCase().replace(/\s+/g, '-');
  }

  validateTankData(tank) {
    return tank && 
           typeof tank.name === 'string' && 
           tank.name.length > 0 &&
           typeof tank.country === 'string' &&
           typeof tank.type === 'string';
  }

  calculateScore(actual, threshold) {
    return Math.max(0, Math.min(100, (threshold - actual) / threshold * 100));
  }

  /**
   * ГЕНЕРАЦИЯ ОТЧЕТА
   */
  generateReport() {
    const totalTime = performance.now() - this.startTime;
    const totalTests = this.results.size;
    const passedTests = Array.from(this.results.values()).filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log('\n📊 TEST SUITE REPORT');
    console.log('='.repeat(50));
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log(`Total Time: ${totalTime.toFixed(2)}ms`);
    console.log('='.repeat(50));

    // Детальный отчет по категориям
    const categories = ['unit', 'integration', 'performance'];
    categories.forEach(category => {
      const categoryTests = Array.from(this.results.entries())
        .filter(([_, result]) => result.category === category);
      
      if (categoryTests.length > 0) {
        const categoryPassed = categoryTests.filter(([_, result]) => result.passed).length;
        console.log(`\n${category.toUpperCase()} TESTS: ${categoryPassed}/${categoryTests.length} passed`);
        
        categoryTests.forEach(([name, result]) => {
          const status = result.passed ? '✅' : '❌';
          const duration = result.duration ? `${result.duration.toFixed(2)}ms` : 'N/A';
          console.log(`  ${status} ${name}: ${duration}`);
        });
      }
    });
  }
}

// Создаем глобальный экземпляр для удобства
export const testSuite = new TestSuite();

// Глобальный доступ для отладки
if (typeof window !== 'undefined') {
  window.testSuite = testSuite;
}
