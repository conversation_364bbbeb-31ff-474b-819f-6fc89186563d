/* Улучшенные эффекты glassmorphism для интерфейса */

/* Сайдбар: оригинальный эффект стекла как в Best24 */
.sidebar {
  background: linear-gradient(180deg, rgba(45, 35, 75, 0.75) 0%, rgba(35, 28, 60, 0.7) 100%);
  backdrop-filter: blur(20px) saturate(200%);
  -webkit-backdrop-filter: blur(20px) saturate(200%);
  border-right: 1.5px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Стеклянные контейнеры для характеристик - оригинальные */
.characteristics-column {
  background: rgba(30, 30, 46, 0.6);
  backdrop-filter: blur(7px);
  -webkit-backdrop-filter: blur(7px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transition: all 0.3s;
}

.characteristics-column:hover {
  box-shadow: 0 8px 32px rgba(114, 84, 228, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.12);
}

/* Улучшенные заголовки колонок - оригинальные */
.characteristics-header {
  background: rgba(72, 52, 153, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

/* Эффект glassmorphism для progress bars */
.progress-track {
  background: rgba(20, 20, 35, 0.5);
  border-radius: 8px;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  overflow: hidden;
}

.progress-fill {
  box-shadow: 0 0 10px rgba(114, 84, 228, 0.5);
}

/* Стеклянные кнопки и интерактивные элементы */
.sidebar-menu-item {
  /* ИСПРАВЛЕНО: убираем transition для мгновенного переключения */
  border-radius: 8px;
  user-select: none; /* Запрещаем выделение текста */
}

.sidebar-menu-item:hover {
  background: rgba(114, 84, 228, 0.15);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.sidebar-menu-item.active {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
}

/* Общий фон - оригинальный темный */
#bg-gradient-layer {
  background:
    radial-gradient(circle at 20% 20%, rgba(114, 84, 228, 0.1) 0%, transparent 60%),
    radial-gradient(circle at 80% 30%, rgba(54, 180, 220, 0.1) 0%, transparent 60%),
    radial-gradient(circle at 60% 80%, rgba(220, 70, 150, 0.1) 0%, transparent 60%),
    radial-gradient(circle at 10% 90%, rgba(120, 210, 100, 0.1) 0%, transparent 60%);
  opacity: 0.95;
}

/* Улучшенные светящиеся тексты */
.glow-text {
  text-shadow:
    0 0 10px rgba(114, 84, 228, 0.5),
    0 0 20px rgba(114, 84, 228, 0.3);
  letter-spacing: 0.05em;
}
