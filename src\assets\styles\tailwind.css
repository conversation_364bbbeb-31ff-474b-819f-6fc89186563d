@tailwind base;
@tailwind components;
@tailwind utilities;

/* ========================================
   DAISYUI КОМПОНЕНТЫ ДЛЯ ТАНКОВ
   ======================================== */

@layer components {
  /* Кнопки с DaisyUI стилем - исправленные CSS правила */
  .btn-tank {
    @apply btn btn-primary btn-sm gap-2;
    transition: all 0.3s ease;
  }

  .btn-tank:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  .btn-tank-secondary {
    @apply btn btn-secondary btn-sm gap-2;
    transition: all 0.3s ease;
  }

  .btn-tank-ghost {
    @apply btn btn-ghost btn-sm gap-2;
    transition: all 0.3s ease;
  }

  /* Карточки танков с DaisyUI - исправленные CSS правила */
  .tank-card-daisy {
    @apply card bg-base-200/20 shadow-xl border border-base-300/30;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .tank-card-daisy:hover {
    transform: translateY(-0.25rem);
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  /* Таблицы сборок с DaisyUI - исправленные CSS правила */
  .builds-table-daisy {
    @apply table w-full bg-transparent;
    border-radius: 0;
    overflow: visible;
  }

  .builds-table-daisy thead {
    background-color: transparent;
    color: white;
  }

  .builds-table-daisy th {
    @apply text-xs font-bold uppercase tracking-wide cursor-pointer;
    background-color: transparent;
    transition: all 0.3s ease;
  }

  .builds-table-daisy th:hover {
    transform: translateY(-0.125rem);
    background-color: transparent;
  }

  .builds-table-daisy td {
    @apply text-xs text-center;
    background-color: transparent;
  }

  .builds-table-daisy td:first-child {
    text-align: left;
    font-weight: 600;
  }

  /* Карточки примечаний с DaisyUI - исправленные CSS правила */
  .note-card-daisy {
    @apply card shadow-xl border;
    background-color: rgba(30, 41, 59, 0.3);
    border-color: rgba(51, 65, 85, 0.4);
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;
  }

  .note-card-daisy:hover {
    transform: translateY(-0.25rem);
    border-color: rgba(139, 92, 246, 0.6);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  /* Модалы с DaisyUI - исправленные CSS правила */
  .modal-tank {
    @apply modal;
    align-items: flex-end;
  }

  @media (min-width: 640px) {
    .modal-tank {
      align-items: center;
    }
  }

  .modal-tank-box {
    @apply modal-box bg-base-200/90 border border-base-300/50;
    backdrop-filter: blur(20px);
  }

  /* Формы с DaisyUI - исправленные CSS правила */
  .form-tank {
    @apply form-control w-full;
    max-width: 20rem;
  }

  .input-tank {
    @apply input input-bordered w-full;
    background-color: rgba(30, 41, 59, 0.5);
  }

  /* Бейджи типов танков с DaisyUI - исправленные CSS правила */
  .badge-tank-type {
    @apply badge font-bold uppercase;
    font-size: 1rem;
    line-height: 1.25rem;
    height: 2rem;
  }

  .badge-tank-lt {
    @apply badge-tank-type;
    background-color: var(--primary);
    color: var(--primary-content);
  }

  .badge-tank-st {
    @apply badge-tank-type;
    background-color: var(--secondary);
    color: var(--secondary-content);
  }

  .badge-tank-tt {
    @apply badge-tank-type;
    background-color: var(--accent);
    color: var(--accent-content);
  }

  .badge-tank-td {
    @apply badge-tank-type;
    background-color: var(--success);
    color: var(--success-content);
  }

  .badge-tank-spg {
    @apply badge-tank-type;
    background-color: var(--error);
    color: var(--error-content);
  }

  /* Алерты с DaisyUI - исправленные CSS правила */
  .alert-tank {
    @apply alert;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .alert-tank-success {
    @apply alert-tank;
    background-color: var(--success);
    color: var(--success-content);
  }

  .alert-tank-error {
    @apply alert-tank;
    background-color: var(--error);
    color: var(--error-content);
  }

  .alert-tank-warning {
    @apply alert-tank;
    background-color: var(--warning);
    color: var(--warning-content);
  }

  .alert-tank-info {
    @apply alert-tank;
    background-color: var(--info);
    color: var(--info-content);
  }
}

/* Кастомные утилиты */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  }

  .glass-effect {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  .glow-primary {
    box-shadow: 0 0 20px rgba(114, 84, 228, 0.5);
  }

  .glow-accent {
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
  }

  .no-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
}
