/**
 * ТЕСТЫ НОВОГО ИНТЕРФЕЙСА СРАВНЕНИЯ
 */

export class NewCompareInterfaceTests {
  constructor() {
    this.testResults = [];
  }

  /**
   * ЗАПУСК ВСЕХ ТЕСТОВ
   */
  async runAllTests() {
    console.log('🧪 Запуск тестов нового интерфейса сравнения...');

    await this.testTableGeneration();
    await this.testTankAddition();
    await this.testTankRemoval();
    await this.testSyncWithOldSystem();
    await this.testCompareButtonNavigation();
    await this.testResponsiveDesign();

    this.generateReport();
  }

  /**
   * ТЕСТ ГЕНЕРАЦИИ ТАБЛИЦЫ
   */
  async testTableGeneration() {
    const testName = 'Table Generation Test';
    
    try {
      const table = document.querySelector('.comparison-table');
      const tableWrapper = document.querySelector('.comparison-table-wrapper');
      const addButtons = document.querySelectorAll('.tank-add-button');
      
      let score = 0;
      let maxScore = 4;

      // Проверка 1: Таблица существует
      if (table) score++;

      // Проверка 2: Обертка для скролла существует
      if (tableWrapper) score++;

      // Проверка 3: Кнопки добавления танков созданы
      if (addButtons.length >= 8) score++;

      // Проверка 4: Характеристики отображаются
      const charRows = table?.querySelectorAll('tbody tr');
      if (charRows && charRows.length >= 10) score++;

      const percentage = (score / maxScore) * 100;

      this.testResults.push({
        name: testName,
        status: percentage >= 75 ? 'PASS' : 'PARTIAL',
        details: `Таблица: ${score}/${maxScore} элементов (${percentage.toFixed(1)}%)`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ ДОБАВЛЕНИЯ ТАНКОВ
   */
  async testTankAddition() {
    const testName = 'Tank Addition Test';
    
    try {
      if (!window.newCompareSystem) {
        this.testResults.push({
          name: testName,
          status: 'FAIL',
          details: 'Новая система сравнения не найдена'
        });
        return;
      }

      // Очищаем систему
      window.newCompareSystem.clearAll();

      // Добавляем тестовый танк
      const testTank = {
        name: 'Test Tank Interface',
        level: 10,
        type: 'heavyTank',
        nation: 'ussr',
        hp: 2000,
        damage: 400,
        penetration: 250
      };

      const addResult = window.newCompareSystem.addTank(testTank, 0);

      // Проверяем, что танк добавился
      const tankCard = document.querySelector('.tank-card-mini');
      const tankName = tankCard?.querySelector('.tank-mini-name')?.textContent;

      // Очищаем после теста
      window.newCompareSystem.clearAll();

      this.testResults.push({
        name: testName,
        status: (addResult && tankName === testTank.name) ? 'PASS' : 'FAIL',
        details: `Добавление: ${addResult}, Отображение: ${tankName === testTank.name}`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ УДАЛЕНИЯ ТАНКОВ
   */
  async testTankRemoval() {
    const testName = 'Tank Removal Test';
    
    try {
      if (!window.newCompareSystem) {
        this.testResults.push({
          name: testName,
          status: 'FAIL',
          details: 'Новая система сравнения не найдена'
        });
        return;
      }

      // Добавляем тестовый танк
      const testTank = {
        name: 'Test Tank Removal',
        level: 10,
        type: 'heavyTank',
        nation: 'ussr'
      };

      window.newCompareSystem.addTank(testTank, 0);

      // Удаляем танк
      const removeResult = window.newCompareSystem.removeTank(0);

      // Проверяем, что танк удален
      const tankCards = document.querySelectorAll('.tank-card-mini');
      const addButtons = document.querySelectorAll('.tank-add-button');

      this.testResults.push({
        name: testName,
        status: (removeResult && tankCards.length === 0 && addButtons.length > 0) ? 'PASS' : 'FAIL',
        details: `Удаление: ${removeResult}, Карточек танков: ${tankCards.length}, Кнопок добавления: ${addButtons.length}`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ СИНХРОНИЗАЦИИ СО СТАРОЙ СИСТЕМОЙ
   */
  async testSyncWithOldSystem() {
    const testName = 'Sync With Old System Test';
    
    try {
      if (!window.newCompareSystem || !window.advancedCompareSystem) {
        this.testResults.push({
          name: testName,
          status: 'FAIL',
          details: 'Системы сравнения не найдены'
        });
        return;
      }

      // Очищаем обе системы
      window.newCompareSystem.clearAll();
      window.advancedCompareSystem.clearAll();

      // Добавляем танк в старую систему
      const testTank = {
        name: 'Sync Test Tank',
        level: 10,
        type: 'heavyTank',
        nation: 'ussr'
      };

      window.advancedCompareSystem.addTank(testTank);

      // Синхронизируем
      window.newCompareSystem.syncWithOldSystem();

      // Проверяем, что танк появился в новой системе
      const newSystemTank = window.newCompareSystem.tanks[0];
      const syncSuccess = newSystemTank && newSystemTank.name === testTank.name;

      // Очищаем после теста
      window.newCompareSystem.clearAll();
      window.advancedCompareSystem.clearAll();

      this.testResults.push({
        name: testName,
        status: syncSuccess ? 'PASS' : 'FAIL',
        details: `Синхронизация: ${syncSuccess}, Танк в новой системе: ${newSystemTank?.name || 'нет'}`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ НАВИГАЦИИ КНОПКИ СРАВНЕНИЯ
   */
  async testCompareButtonNavigation() {
    const testName = 'Compare Button Navigation Test';
    
    try {
      // Проверяем, что кнопка сравнения переключает на вкладку
      let navigationCalled = false;
      const originalOnMenuSelected = window.onMenuSelected;
      
      window.onMenuSelected = (menu) => {
        if (menu === 'compare') {
          navigationCalled = true;
        }
        // Восстанавливаем оригинальную функцию
        if (originalOnMenuSelected) {
          originalOnMenuSelected(menu);
        }
      };

      // Добавляем танки для активации кнопки
      if (window.advancedCompareSystem) {
        const testTank1 = { name: 'Nav Test 1', level: 10, type: 'heavyTank', nation: 'ussr' };
        const testTank2 = { name: 'Nav Test 2', level: 10, type: 'mediumTank', nation: 'germany' };
        
        window.advancedCompareSystem.addTank(testTank1);
        window.advancedCompareSystem.addTank(testTank2);

        // Ждем обновления UI
        await new Promise(resolve => setTimeout(resolve, 100));

        // Симулируем клик по кнопке сравнения
        const compareBtn = document.querySelector('.floating-action-btn.compare-btn');
        if (compareBtn && !compareBtn.disabled) {
          compareBtn.click();
        }

        // Очищаем после теста
        window.advancedCompareSystem.clearAll();
      }

      // Восстанавливаем оригинальную функцию
      window.onMenuSelected = originalOnMenuSelected;

      this.testResults.push({
        name: testName,
        status: navigationCalled ? 'PASS' : 'FAIL',
        details: `Навигация вызвана: ${navigationCalled}`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ АДАПТИВНОГО ДИЗАЙНА
   */
  async testResponsiveDesign() {
    const testName = 'Responsive Design Test';
    
    try {
      const table = document.querySelector('.comparison-table');
      const wrapper = document.querySelector('.comparison-table-wrapper');
      
      let score = 0;
      let maxScore = 3;

      // Проверка 1: Таблица имеет минимальную ширину
      if (table) {
        const computedStyle = window.getComputedStyle(table);
        if (computedStyle.minWidth && parseInt(computedStyle.minWidth) >= 1000) {
          score++;
        }
      }

      // Проверка 2: Обертка имеет горизонтальный скролл
      if (wrapper) {
        const computedStyle = window.getComputedStyle(wrapper);
        if (computedStyle.overflowX === 'auto') {
          score++;
        }
      }

      // Проверка 3: Первая колонка зафиксирована
      const firstHeader = table?.querySelector('th:first-child');
      if (firstHeader) {
        const computedStyle = window.getComputedStyle(firstHeader);
        if (computedStyle.position === 'sticky') {
          score++;
        }
      }

      const percentage = (score / maxScore) * 100;

      this.testResults.push({
        name: testName,
        status: percentage >= 66 ? 'PASS' : 'PARTIAL',
        details: `Адаптивность: ${score}/${maxScore} критериев (${percentage.toFixed(1)}%)`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        details: error.message
      });
    }
  }

  /**
   * ГЕНЕРАЦИЯ ОТЧЕТА
   */
  generateReport() {
    const passedTests = this.testResults.filter(test => test.status === 'PASS').length;
    const totalTests = this.testResults.length;

    console.log('\n🎯 ОТЧЕТ О ТЕСТИРОВАНИИ НОВОГО ИНТЕРФЕЙСА СРАВНЕНИЯ');
    console.log('='.repeat(60));
    console.log(`✅ Пройдено тестов: ${passedTests}/${totalTests}`);
    console.log(`📊 Успешность: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log('\n📋 ДЕТАЛИ ТЕСТОВ:');
    
    this.testResults.forEach((test, index) => {
      const statusIcon = {
        'PASS': '✅',
        'PARTIAL': '⚠️',
        'FAIL': '❌',
        'ERROR': '💥'
      }[test.status] || '❓';

      console.log(`${index + 1}. ${statusIcon} ${test.name}`);
      console.log(`   Статус: ${test.status}`);
      console.log(`   Детали: ${test.details}`);
      console.log('');
    });

    console.log('='.repeat(60));
    
    if (passedTests === totalTests) {
      console.log('🎉 НОВЫЙ ИНТЕРФЕЙС СРАВНЕНИЯ РАБОТАЕТ ОТЛИЧНО!');
    } else {
      console.log('⚠️  Некоторые функции требуют доработки.');
    }
  }
}

// Экспорт для использования в консоли
window.NewCompareInterfaceTests = NewCompareInterfaceTests;

// Автоматический запуск тестов в dev режиме
if (import.meta.env?.MODE === 'development') {
  window.runNewCompareTests = () => {
    const tests = new NewCompareInterfaceTests();
    tests.runAllTests();
  };
  
  console.log('🧪 Тесты нового интерфейса загружены! Запустите: runNewCompareTests()');
}
