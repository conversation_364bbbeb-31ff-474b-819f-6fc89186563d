/**
 * ОПТИМИЗИРОВАННЫЕ КОНСТАНТЫ ПРОИЗВОДИТЕЛЬНОСТИ
 * Централизованное хранилище критических параметров производительности
 */

// ULTRA-OPTIMIZED PERFORMANCE THRESHOLDS FOR 113 TANKS
export const PER<PERSON>ORMANCE_THRESHOLDS = {
  // Рендеринг - ENHANCED RENDERING
  TANK_COUNT: 113,
  BATCH_SIZE: 18,          // Increased for better throughput
  BATCH_THRESHOLD: 40,     // Higher threshold for efficiency
  VIRTUAL_THRESHOLD: 80,   // More aggressive virtualization
  ADAPTIVE_BATCH_MULTIPLIER: 1.5, // Dynamic scaling

  // Временные ограничения (в миллисекундах) - ULTRA OPTIMIZED
  MAX_TASK_TIME: 12,       // Tighter frame budget
  FRAME_BUDGET: 6,         // More aggressive budget
  DEBOUNCE_DELAY: 0,       // INSTANT OPERATIONS
  THROTTLE_DELAY: 8,       // 120 FPS capability
  IDLE_CALLBACK_TIMEOUT: 5, // Faster idle processing

  // Память (в байтах) - INTELLIGENT MEMORY MANAGEMENT
  MEMORY_THRESHOLD: 35 * 1024 * 1024,  // 35MB - more aggressive
  MEMORY_WARNING_THRESHOLD: 25 * 1024 * 1024, // 25MB warning
  MEMORY_CRITICAL_THRESHOLD: 50 * 1024 * 1024, // 50MB critical
  IMAGE_CACHE_SIZE: 120,               // Larger image cache
  DOM_CACHE_SIZE: 80,                  // Larger DOM cache
  COMPUTED_CACHE_SIZE: 60,             // Cache for computed values

  // FPS и производительность - ENHANCED PERFORMANCE
  MIN_FPS: 45,             // Higher minimum for modern devices
  TARGET_FPS: 60,
  PERFORMANCE_BUDGET: 100, // Tighter budget for faster startup
  FRAME_TIME_BUDGET: 16.67, // 60fps frame time in ms

  // Ленивая загрузка - PREDICTIVE LOADING
  LAZY_LOAD_THRESHOLD: 350,  // Larger threshold for smoother experience
  PRELOAD_COUNT: 6,          // More aggressive preloading
  INTERSECTION_MARGIN: '100px', // Larger intersection margin

  // Виртуализация - ENHANCED VIRTUALIZATION
  VISIBLE_ITEMS: 30,         // More visible items for better UX
  BUFFER_SIZE: 10,           // Larger buffer for smoother scrolling
  ITEM_HEIGHT: 130,          // px height per tank item - увеличено для многострочных названий
  OVERSCAN: 5,               // Items to render outside viewport
};

// ОПТИМИЗИРОВАННЫЕ СЕЛЕКТОРЫ CSS
export const OPTIMIZED_SELECTORS = {
  TANK_LIST: '#tank-list',
  TANK_ITEM: '.tank-item',
  TANK_NAME: '.tank-name',
  TANK_ICON: '.tank-icon',
  TANK_TYPE: '.tank-type-indicator',
  LOADING_OVERLAY: '#modern-loading',
  CHARACTERISTICS: '#tank-characteristics-container',
};

// ПУТИ К РЕСУРСАМ
export const RESOURCE_PATHS = {
  TANKS_DIR: '/src/assets/images/tanks/',
  FLAGS_DIR: '/src/assets/images/flags/',
  UPGRADES_DIR: '/src/assets/images/upgrades/',
  DEFAULT_TANK: 't-62a.webp',
  IMAGE_FORMAT: '.webp',
};

// НАСТРОЙКИ АНИМАЦИЙ
export const ANIMATION_CONFIG = {
  DURATION_FAST: 150,      // ms
  DURATION_NORMAL: 250,    // ms
  DURATION_SLOW: 350,      // ms - уменьшено для лучшей производительности
  EASING: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', // Более плавная кривая
  HOVER_SCALE: 1.03,       // Немного увеличено для лучшего UX
  HOVER_TRANSLATE: -3,     // px подъем при hover
};

// НАСТРОЙКИ МОНИТОРИНГА ПРОИЗВОДИТЕЛЬНОСТИ
export const MONITORING_CONFIG = {
  ENABLED_IN_DEV: true,
  ENABLED_IN_PROD: false,
  METRICS_RETENTION: 10,    // Уменьшено для экономии памяти
  WARNING_THRESHOLD: 16,    // ms для предупреждения о медленных функциях
  MEMORY_CHECK_INTERVAL: 10000, // ms между проверками памяти
  FPS_SAMPLE_SIZE: 30,      // Уменьшено для экономии ресурсов
};

// ОПТИМИЗИРОВАННЫЕ РАЗМЕРЫ ДЛЯ РАЗНЫХ УСТРОЙСТВ
export const RESPONSIVE_BREAKPOINTS = {
  MOBILE: 480,
  TABLET: 768,
  DESKTOP: 1024,
  LARGE: 1440,
};

// НАСТРОЙКИ GRID LAYOUT
export const GRID_CONFIG = {
  MIN_COLUMN_WIDTH: 200,    // px минимальная ширина колонки - оригинальный размер
  GAP: 8,                   // px между элементами (0.5rem) - оригинальный размер
  PADDING: 16,              // px отступы контейнера - оригинальный размер
  COLUMNS_MOBILE: 1,
  COLUMNS_TABLET: 2,
  COLUMNS_DESKTOP: 'auto-fill',
};

// НАСТРОЙКИ КЭШИРОВАНИЯ
export const CACHE_CONFIG = {
  DOM_ELEMENTS: true,
  IMAGES: true,
  COMPUTED_STYLES: false,   // Отключено для экономии памяти
  FILTER_RESULTS: true,
  MAX_CACHE_AGE: 300000,    // 5 минут в ms
};

// ЭКСПЕРИМЕНТАЛЬНЫЕ ОПТИМИЗАЦИИ
export const EXPERIMENTAL_CONFIG = {
  USE_INTERSECTION_OBSERVER: true,
  USE_PASSIVE_LISTENERS: true,
  USE_WILL_CHANGE: true,    // Включено для критических элементов
  USE_TRANSFORM_3D: true,   // Для аппаратного ускорения
  USE_CONTAIN_CSS: true,    // CSS containment для изоляции
  USE_CONTENT_VISIBILITY: true, // Для виртуализации
};

// ФУНКЦИИ-УТИЛИТЫ ДЛЯ РАБОТЫ С КОНСТАНТАМИ
export const getOptimalBatchSize = (itemCount) => {
  if (itemCount <= 50) return Math.min(itemCount, 10);
  if (itemCount <= 100) return PERFORMANCE_THRESHOLDS.BATCH_SIZE;
  return Math.max(PERFORMANCE_THRESHOLDS.BATCH_SIZE, Math.ceil(itemCount / 20));
};

export const getOptimalDelay = (itemCount) => {
  if (itemCount <= PERFORMANCE_THRESHOLDS.TANK_COUNT) return 0;
  return Math.min(10, Math.ceil(itemCount / 100));
};

export const isLowEndDevice = () => {
  // Детекция слабых устройств
  const memory = navigator.deviceMemory || 4;
  const cores = navigator.hardwareConcurrency || 4;
  const connection = navigator.connection;

  // Учитываем качество соединения
  const slowConnection = connection &&
    (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');

  return memory <= 2 || cores <= 2 || slowConnection;
};

export const getPerformanceMode = () => {
  if (isLowEndDevice()) return 'low';

  const memory = navigator.deviceMemory || 4;
  const cores = navigator.hardwareConcurrency || 4;

  if (memory >= 8 && cores >= 8) return 'high';
  return 'medium';
};

// МАТЕМАТИЧЕСКИЕ КОНСТАНТЫ ДЛЯ UI
export const MATH_CONSTANTS = {
  GOLDEN_RATIO: 1.618,
  // Оптимальные соотношения для UI
  ASPECT_RATIO_CARD: 1.4,
  ASPECT_RATIO_ICON: 1.0,
  SCALE_FACTOR: 1.05, // Уменьшено для более тонких эффектов
};

export default {
  PERFORMANCE_THRESHOLDS,
  OPTIMIZED_SELECTORS,
  RESOURCE_PATHS,
  ANIMATION_CONFIG,
  MONITORING_CONFIG,
  RESPONSIVE_BREAKPOINTS,
  GRID_CONFIG,
  CACHE_CONFIG,
  EXPERIMENTAL_CONFIG,
  MATH_CONSTANTS,
  getOptimalBatchSize,
  getOptimalDelay,
  isLowEndDevice,
  getPerformanceMode,
};
