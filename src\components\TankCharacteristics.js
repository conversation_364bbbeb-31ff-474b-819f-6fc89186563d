/**
 * ОПТИМИЗИРОВАННЫЙ КОМПОНЕНТ ХАРАКТЕРИСТИК ТАНКОВ
 * Упрощенная логика с устранением дублирования и улучшенной производительностью
 */

import { unifiedStateManager } from '../store/UltraStateManager.js';
import { getTankIconPath } from '../utils/constants.js';
import { cacheManager } from '../utils/CacheMigrationAdapter.js';

// Используем унифицированное состояние
const state = unifiedStateManager.state;

/**
 * ОПТИМИЗИРОВАННЫЕ УТИЛИТЫ
 */

// Кэшируем создание ID танков
const tankIdCache = new Map();

function createTankId(tankName) {
  if (tankIdCache.has(tankName)) {
    return tankIdCache.get(tankName);
  }

  const iconPath = getTankIconPath(tankName);
  const tankId = iconPath.split('/').pop().replace('.webp', '');
  tankIdCache.set(tankName, tankId);
  return tankId;
}

// Оптимизированная функция скрытия списка танков
function hideTankList() {
  const tankListElement = cacheManager.dom.getCachedElement('#tank-list');
  if (tankListElement) {
    tankListElement.style.display = 'none';
    tankListElement.classList.add('hidden');
  }
}

/**
 * УПРОЩЕННАЯ ФУНКЦИЯ ПОКАЗА ХАРАКТЕРИСТИК
 */
export function showTankCharacteristics(tankName, saveFilters = true) {
  console.log(`🎯 Showing tank characteristics: ${tankName}`);

  // Немедленно скрываем список
  hideTankList();

  // Упрощенное сохранение фильтров
  if (saveFilters) {
    const filtersBeforeSelection = {
      selectedCountry: state.selectedCountry || 'all',
      selectedCategory: state.selectedCategory || 'all',
      searchQuery: state.searchQuery || '',
      timestamp: Date.now()
    };

    localStorage.setItem('filtersBeforeSelection', JSON.stringify(filtersBeforeSelection));
    console.log('💾 Filters saved:', filtersBeforeSelection);
  }

  const tankId = createTankId(tankName);
  showTankCharacteristicsById(tankId);
}

/**
 * ОПТИМИЗИРОВАННАЯ ФУНКЦИЯ ПОКАЗА ПО ID
 */
export async function showTankCharacteristicsById(tankId) {
  try {
    // Кэшированный поиск танка
    const cachedTank = cacheManager.data.getComputed(`tank_by_id_${tankId}`);
    let tank = cachedTank;

    if (!tank) {
      const { findTankNameByUrlId } = await import('../utils/helpers.js');
      const tankName = findTankNameByUrlId(tankId);
      tank = tankName ? state.allTanks.find(t => t.name === tankName) : null;

      if (tank) {
        cacheManager.data.cacheComputed(`tank_by_id_${tankId}`, tank);
      }
    }

    if (!tank) {
      console.error(`Tank with ID ${tankId} not found`);
      return;
    }

    console.log(`✅ Tank found: ${tank.name}`);

    // Устанавливаем состояние
    document.body.setAttribute('data-tank-page', 'true');
    hideTankList();

    state.selectedTank = tank;
    window.currentTankId = tankId;

    // Обновляем фильтры через FilterManager
    const { filterManager } = await import('../core/FilterManager.js');
    filterManager.setFiltersForTank(tank);

    // Обновляем URL
    const { createTankUrlId } = await import('../utils/helpers.js');
    const expectedUrl = `/vehicles/${createTankUrlId(tank.name)}`;
    if (window.location.pathname !== expectedUrl) {
      history.replaceState(
        { tankName: tank.name, section: 'vehicles' },
        `Tank: ${tank.name}`,
        expectedUrl
      );
    }

    // Сохраняем выбранный танк
    localStorage.setItem('selectedTank', JSON.stringify({
      name: tank.name,
      country: tank.country,
      type: tank.type,
      timestamp: Date.now()
    }));
    localStorage.setItem('activeMenuItem', 'vehicles');

    // Показываем секцию vehicles и характеристики
    const [{ showSection }, { updateTankDetailsUI }] = await Promise.all([
      import('../utils/ui.js'),
      import('./tank-details/TankDetails.js')
    ]);

    showSection('vehicles');

    const characteristicsContainer = cacheManager.dom.getCachedElement('#tank-characteristics-container');
    if (!characteristicsContainer) {
      console.error('Tank characteristics container not found!');
      return;
    }

    // Показываем контейнер характеристик
    characteristicsContainer.classList.remove('hidden');
    characteristicsContainer.style.display = 'block';
    characteristicsContainer.style.opacity = '1';

    // Обновляем UI деталей танка
    updateTankDetailsUI(tank);

    // Показываем кнопку "Назад к списку"
    const backButton = cacheManager.dom.getCachedElement('#back-to-list-btn');
    if (backButton) {
      backButton.classList.remove('hidden');
    }

    console.log(`✅ Tank characteristics shown for: ${tank.name}`);
  } catch (error) {
    console.error('Error showing tank characteristics:', error);
  }
}

/**
 * УПРОЩЕННЫЕ УТИЛИТЫ
 */

// Скрытие характеристик танка
export function hideTankCharacteristics() {
  const container = cacheManager.dom.getCachedElement('#tank-characteristics-container');
  if (container) {
    container.classList.add('hidden');
    container.style.display = 'none';
  }
}

// Показ списка танков
export async function showTankList() {
  console.log('🔙 Returning to tank list');

  history.replaceState(null, 'Vehicles', '/vehicles');

  // Сбрасываем состояние
  state.selectedTank = null;
  localStorage.removeItem('selectedTank');
  document.body.removeAttribute('data-tank-page');

  hideTankCharacteristics();

  // Показываем список через TankManager
  if (window.tankManager) {
    window.tankManager.applyFiltersAndRenderTankList();
  }

  // Синхронизируем бейджики сравнения после возврата к списку
  if (window.advancedCompareSystem) {
    window.advancedCompareSystem.syncAllBadges();
  }
}

// Инициализация кнопки "Назад"
export function initBackToListButton() {
  const backButton = cacheManager.dom.getCachedElement('#back-to-list-btn');
  if (backButton) {
    backButton.addEventListener('click', showTankList);
  }
}

// Экспорт для глобального доступа (совместимость)
if (typeof window !== 'undefined') {
  window.showTankCharacteristics = showTankCharacteristics;
  window.showTankCharacteristicsById = showTankCharacteristicsById;
  window.hideTankCharacteristics = hideTankCharacteristics;
  window.showTankList = showTankList;
}
