/**
 * БЫСТРЫЙ РЕНДЕРЕР СПИСКА ТАНКОВ - С ПРАВИЛЬНЫМИ ПУТЯМИ
 */
import { getTankIconPath, getBlurredFlagPath, getTankTypeIconPath } from '../../utils/constants.js';

/**
 * ОБРАБОТЧИК КЛИКА ПО БЕЙДЖИКУ СРАВНЕНИЯ
 */
window.handleCompareBadgeClick = function(event, tankName) {
  console.log('🎯 Compare badge clicked for:', tankName);
  event.stopPropagation();
  event.preventDefault();

  // Получаем расширенную систему сравнения
  if (window.advancedCompareSystem) {
    const tankCard = event.target.closest('.tank-item');
    const compareBadge = tankCard?.querySelector('.tank-compare-badge');

    if (compareBadge?.classList.contains('active')) {
      // Удаляем танк из сравнения
      if (import.meta.env?.MODE === 'development') {
        console.log('🗑️ Removing tank from comparison:', tankName);
      }
      window.advancedCompareSystem.removeTankByName(tankName);
    } else {
      // Добавляем танк в сравнение
      if (import.meta.env?.MODE === 'development') {
        console.log('➕ Adding tank to comparison:', tankName);
      }
      const tankData = getTankDataByName(tankName);
      if (tankData) {
        window.advancedCompareSystem.addTank(tankData);
      } else {
        if (import.meta.env?.MODE === 'development') {
          console.error('❌ Tank data not found for:', tankName);
        }
      }
    }
  } else {
    if (import.meta.env?.MODE === 'development') {
      console.error('❌ Advanced compare system not available');
    }
  }
};

/**
 * ПОЛУЧИТЬ ДАННЫЕ ТАНКА ПО ИМЕНИ
 */
function getTankDataByName(tankName) {
  if (window.tankManager && window.tankManager.allTanks) {
    return window.tankManager.allTanks.find(tank => tank.name === tankName);
  }
  return null;
}
export async function renderTankList(tanks, tanksData, handleTankSelection, selectedTankName) {
  const tankListContainer = document.getElementById('tank-list');
  if (!tankListContainer) {
    console.error('Tank list container not found');
    return;
  }

  // Быстрая очистка и рендеринг
  try {
    await fastRender(tanks, tankListContainer, handleTankSelection, selectedTankName);
  } catch (error) {
    console.error('Render error:', error);
    tankListContainer.innerHTML = `
      <div class="error-message">
        <h3>Ошибка рендеринга</h3>
        <p>Не удалось отобразить список танков. Попробуйте обновить страницу.</p>
      </div>
    `;
  }
}

/**
 * БЫСТРЫЙ РЕНДЕРИНГ БЕЗ ЛИШНИХ ПРОВЕРОК
 */
async function fastRender(tanks, container, handleSelection, selectedTank) {
  if (!tanks || tanks.length === 0) {
    container.innerHTML = '<div class="no-tanks">Танки не найдены</div>';
    return;
  }

  // Создаем HTML одним блоком
  const html = tanks.map(tank => createTankHTML(tank, handleSelection, selectedTank)).join('');
  container.innerHTML = html;

  // Восстанавливаем правильный grid layout - используем CSS классы
  container.style.display = 'grid';
  // Убираем принудительные стили - используем CSS
  container.style.gridTemplateColumns = '';
  container.style.gap = '';
  container.style.padding = '';
  container.style.justifyContent = '';

  // Добавляем обработчики событий (только если еще не добавлены)
  if (!container.hasAttribute('data-events-attached')) {
    container.addEventListener('click', (e) => {
      // Проверяем, не клик ли по бейджику сравнения
      if (e.target.closest('.tank-compare-badge')) {
        return; // Не обрабатываем клик по карточке, если кликнули по бейджику
      }

      const tankItem = e.target.closest('.tank-item');
      if (tankItem && handleSelection) {
        const tankName = tankItem.dataset.tankName;
        handleSelection(tankName);
      }
    });
    container.setAttribute('data-events-attached', 'true');
  }

  // Дополнительная синхронизация для случаев, когда состояние не было восстановлено
  setTimeout(() => {
    if (window.advancedCompareSystem) {
      window.advancedCompareSystem.syncAllBadges();
    }
  }, 50);
}

/**
 * СОЗДАНИЕ HTML ТАНКА
 */
function createTankHTML(tank, handleSelection, selectedTank) {
  const name = tank.name || 'Неизвестный танк';
  const level = tank.level || 10;

  // Преобразуем числовой уровень в римские цифры
  const levelRoman = numberToRoman(level);
  const country = tank.country || 'International';
  const type = tank.type || 'СТ';

  // Используем правильные функции из constants.js
  const flagPath = getBlurredFlagPath(country);
  const iconPath = getTankIconPath(name);
  const typeIcon = getTankTypeIconPath(type);

  // Добавляем правильный класс для типа техники
  const typeClass = getTypeClass(type);

  // Проверяем, находится ли танк в сравнении
  let isInComparison = false;
  if (window.advancedCompareSystem && window.advancedCompareSystem.tanks) {
    // Сначала пытаемся восстановить состояние если оно еще не восстановлено
    if (window.advancedCompareSystem.tanks.every(t => t === null)) {
      window.advancedCompareSystem.restoreState();
    }
    isInComparison = window.advancedCompareSystem.tanks.some(t => t && t.name === name);
  }
  const badgeClass = isInComparison ? 'tank-compare-badge active' : 'tank-compare-badge';

  return `
    <div class="tank-item" data-tank-name="${name}">
      <div class="tank-flag-background" style="background-image: url('${flagPath}')"></div>
      <div class="tank-info-container">
        <div class="tank-type-container">
          <div class="tank-type-indicator ${typeClass}">${type}</div>
          <img class="tank-type-icon" src="${typeIcon}" alt="${type}" loading="lazy">
        </div>
        <div class="tank-name-row">
          <span class="tank-name">${name}</span>
        </div>
      </div>
      <div class="tank-icon" style="background-image: url('${iconPath}')"></div>
      <div class="tank-level-badge">${levelRoman}</div>
      <div class="${badgeClass}" data-tank-name="${name}">
        <svg class="compare-plus-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
        </svg>
        <svg class="compare-check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
        </svg>
      </div>
    </div>
  `;
}

/**
 * ПОЛУЧЕНИЕ КЛАССА ДЛЯ ТИПА ТЕХНИКИ
 */
function getTypeClass(type) {
  const typeClassMap = {
    'ЛТ': 'lt',      // Легкий танк - фиолетовый (#7c3aed)
    'СТ': 'st',      // Средний танк - розовый (#db2777)
    'ТТ': 'tt',      // Тяжелый танк - синий (#2563eb)
    'ПТ-САУ': 'td',  // ПТ-САУ - зеленый (#059669)
    'САУ': 'spg'     // САУ - красный (#dc2626)
  };
  return typeClassMap[type] || 'st';
}

/**
 * ПРЕОБРАЗОВАНИЕ ЧИСЛА В РИМСКИЕ ЦИФРЫ
 */
function numberToRoman(num) {
  const romanMap = {
    10: 'X',
    9: 'IX',
    8: 'VIII',
    7: 'VII',
    6: 'VI',
    5: 'V',
    4: 'IV',
    3: 'III',
    2: 'II',
    1: 'I'
  };
  return romanMap[num] || 'X';
}



/**
 * Функция обновления множественного выбора фильтров
 */
export function updateMultipleFilterSelection(filterType, selectedValues) {
  const filterContainerId = filterType === 'country' ? 'nation-filter' : 'type-filter';
  const dataAttribute = filterType === 'country' ? 'country' : 'category';

  const filterItems = document.querySelectorAll(`#${filterContainerId} .filter-item`);

  filterItems.forEach(item => {
    const itemValue = item.dataset[dataAttribute];

    // Очистка состояний
    item.classList.remove('selected', 'active');
    item.style.background = '';
    item.style.border = '';
    item.style.boxShadow = '';
    item.blur();
    item.style.outline = 'none';

    // Установка нового состояния для выбранных элементов
    if (selectedValues.includes(itemValue)) {
      item.classList.add('selected');
      console.log(`⚡ MULTIPLE: ${filterType} filter "${itemValue}" activated`);
    }
  });
}

/**
 * Функция обновления фильтров - МГНОВЕННАЯ ВЕРСИЯ
 */
export function updateFilterSelection(filterType, targetValue) {
  if (typeof filterType === 'string' && typeof targetValue === 'string') {
    // Новый формат: updateFilterSelection('country', 'USSR') - СИНХРОННАЯ ВЕРСИЯ
    // Простая карта типов танков для избежания асинхронного импорта
    const tankTypeClassMap = {
      'ЛТ': 'light-tank',
      'СТ': 'medium-tank',
      'ТТ': 'heavy-tank',
      'ПТ-САУ': 'at-spg',
      'САУ': 'spg'
    };

    const getTankTypeClass = (type) => tankTypeClassMap[type] || null;

    const filterContainerId = filterType === 'country' ? 'nation-filter' : 'type-filter';
    const dataAttribute = filterType === 'country' ? 'country' : 'category';

    const filterItems = document.querySelectorAll(`#${filterContainerId} .filter-item`);

    filterItems.forEach(item => {
      const itemValue = item.dataset[dataAttribute];

      // Очистка состояний
      item.classList.remove('selected', 'active');
      item.style.background = '';
      item.style.border = '';
      item.style.boxShadow = '';
      item.blur();
      item.style.outline = 'none';

      if (filterType === 'category') {
        const suffix = getTankTypeClass(itemValue);
        if (suffix) item.classList.remove(`selected-${suffix}`);
      }

      // Установка нового состояния
      if (itemValue === targetValue) {
        item.classList.add('selected');
        if (window.DEBUG_MODE) console.log(`⚡ INSTANT: ${filterType} filter "${targetValue}" activated on element`, item);
        if (filterType === 'category' && targetValue !== 'all') {
          const suffix = getTankTypeClass(itemValue);
          if (suffix) item.classList.add(`selected-${suffix}`);
        }
      }
    });
  } else {
    // Старый формат: updateFilterSelection(countryFilter, categoryFilter)
    const countryFilter = filterType;
    const categoryFilter = targetValue;

    // Обновляем фильтры стран
    const countryButtons = document.querySelectorAll('[data-country]');
    countryButtons.forEach(button => {
      const buttonCountry = button.getAttribute('data-country');
      if (buttonCountry === countryFilter || (countryFilter === 'all' && buttonCountry === 'all')) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });

    // Обновляем фильтры категорий
    const categoryButtons = document.querySelectorAll('[data-category]');
    categoryButtons.forEach(button => {
      const buttonCategory = button.getAttribute('data-category');
      if (buttonCategory === categoryFilter || (categoryFilter === 'all' && buttonCategory === 'all')) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });
  }
}

