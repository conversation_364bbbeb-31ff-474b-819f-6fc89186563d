export default {
  plugins: {
    // Исправляем плагин Tailwind
    '@tailwindcss/postcss': {},
    autoprefixer: {
      // Агрессивная оптимизация для современных браузеров
      overrideBrowserslist: [
        '> 2%',
        'last 2 versions',
        'not dead',
        'not ie 11',
        'not op_mini all',
        'not android < 5',
        'not ios_saf < 12'
      ],
      // Удаляем ненужные префиксы для лучшей производительности
      remove: true,
      // Добавляем только необходимые префиксы
      add: true,
      // Оптимизация для современных браузеров
      flexbox: 'no-2009',
      grid: 'autoplace',
      // Дополнительные оптимизации
      cascade: false, // Отключаем каскадирование для меньшего размера
      supports: false, // Отключаем @supports если не нужно
    },
    // Агрессивная оптимизация CSS для продакшена
    ...(process.env.NODE_ENV === 'production'
      ? {
          cssnano: {
            preset: [
              'default',
              {
                discardComments: {
                  removeAll: true,
                },
                normalizeWhitespace: true,
                mergeLonghand: true,
                mergeRules: true,
                colormin: true,
                convertValues: {
                  length: true,
                  angle: true,
                  time: true,
                },
                discardDuplicates: true,
                discardEmpty: true,
                discardOverridden: true,
                minifyFontValues: true,
                minifyGradients: true,
                minifyParams: true,
                minifySelectors: true,
                normalizeCharset: true,
                normalizeDisplayValues: true,
                normalizePositions: true,
                normalizeRepeatStyle: true,
                normalizeString: true,
                normalizeTimingFunctions: true,
                normalizeUnicode: true,
                normalizeUrl: true,
                orderedValues: true,
                reduceInitial: true,
                reduceTransforms: true,
                svgo: {
                  plugins: [
                    'preset-default',
                    'removeDimensions',
                    'removeStyleElement',
                  ],
                },
                uniqueSelectors: true,
                // Специальные оптимизации для проекта
                calc: {
                  precision: 3,
                },
                zindex: false, // Оставляем z-index как есть для слоев UI
                autoprefixer: false, // Отключаем встроенный autoprefixer
                rawCache: true, // Кэширование для ускорения
              },
            ],
          },
        }
      : {}),
  },
};
