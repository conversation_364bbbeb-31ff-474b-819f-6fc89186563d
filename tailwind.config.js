/** @type {import('tailwindcss').Config} */
export default {
  content: {
    files: ['./index.html', './src/**/*.{js,ts,jsx,tsx,vue,html}'],
    // ULTRA-OPTIMIZED CLASS EXTRACTION
    options: {
      defaultExtractor: (content) => {
        // Enhanced extraction with support for dynamic classes and performance optimization
        const patterns = [
          /[^<>"'`\s]*[^<>"'`\s:]/g,                    // Basic classes
          /[^<>"'`\s.(){}[\]#=%]*[^<>"'`\s.(){}[\]#=%:]/g, // Nested classes
          /(?:class|className)=["']([^"']*)["']/g,      // Class attributes
          /classList\.(?:add|remove|toggle)\(['"]([^'"]*)['"]\)/g, // JavaScript classes
          /\b(?:bg|text|border|hover|focus|active|disabled|group)-[\w-]+/g, // Tailwind patterns
          /\b(?:animate|transition|transform|scale|rotate|translate)-[\w-]+/g, // Animation patterns
          /\b(?:grid|flex|block|inline|hidden|visible)-[\w-]*/g, // Layout patterns
        ];

        const matches = new Set();
        patterns.forEach(pattern => {
          const found = content.match(pattern) || [];
          found.forEach(match => matches.add(match));
        });

        return Array.from(matches);
      },
      safelist: [
        // Critical animations - optimized set
        'animate-pulse',
        'animate-fade-in',
        'animate-slide-in',
        'animate-scale-in',

        // Tank type colors - only used ones
        'bg-purple-500', 'bg-pink-500', 'bg-blue-500', 'bg-green-500', 'bg-red-500',
        'text-purple-500', 'text-pink-500', 'text-blue-500', 'text-green-500', 'text-red-500',
        'border-purple-500', 'border-pink-500', 'border-blue-500', 'border-green-500', 'border-red-500',

        // Badge components
        'badge-primary', 'badge-secondary', 'badge-accent', 'badge-success', 'badge-error',
        'badge-warning', 'badge-info',

        // Dynamic classes for tank levels (1-10)
        /^level-[1-9]$/, /^level-10$/,
        /^tier-[1-9]$/, /^tier-10$/,

        // State classes - essential only
        'hidden', 'block', 'flex', 'grid', 'inline-block', 'inline-flex',
        'visible', 'invisible',

        // Critical utilities
        'opacity-0', 'opacity-50', 'opacity-100',
        'transform', 'transition-all', 'transition-opacity', 'transition-transform',
        'duration-150', 'duration-200', 'duration-300',
        'ease-in-out', 'ease-out', 'ease-in',

        // Performance optimized classes
        'gpu-accelerated', 'performance-optimized', 'will-change-transform',

        // Glass effect and modern UI
        'glass-effect', 'backdrop-blur-sm', 'backdrop-blur-md',
      ],
    },
  },
  theme: {
    extend: {
      colors: {
        // Кастомные цвета для танков
        'tank-primary': '#7254e4',
        'tank-secondary': '#a17bf8',
        'tank-accent': '#ff6b6b',
        'glass-bg': 'rgba(255, 255, 255, 0.08)',
        'glass-border': 'rgba(255, 255, 255, 0.15)',
      },
      backdropBlur: {
        xs: '2px',
      },
      animation: {
        // Optimized animations with better performance
        shimmer: 'shimmer 1.8s ease-in-out infinite',
        glow: 'glow 2s ease-in-out infinite alternate',
        'fade-in': 'fadeIn 0.4s ease-out',
        'fade-out': 'fadeOut 0.3s ease-in',
        'slide-in': 'slideIn 0.25s ease-out',
        'slide-out': 'slideOut 0.25s ease-in',
        'scale-in': 'scaleIn 0.2s ease-out',
        'scale-out': 'scaleOut 0.2s ease-in',
        // New performance-optimized animations
        'bounce-subtle': 'bounceSubtle 0.6s ease-out',
        'pulse-fast': 'pulseFast 1s ease-in-out infinite',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
      },
      keyframes: {
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        glow: {
          '0%': { boxShadow: '0 0 5px rgba(114, 84, 228, 0.5)' },
          '100%': { boxShadow: '0 0 20px rgba(114, 84, 228, 0.8)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        slideIn: {
          '0%': { transform: 'translateY(-8px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideOut: {
          '0%': { transform: 'translateY(0)', opacity: '1' },
          '100%': { transform: 'translateY(-8px)', opacity: '0' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.96)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        scaleOut: {
          '0%': { transform: 'scale(1)', opacity: '1' },
          '100%': { transform: 'scale(0.96)', opacity: '0' },
        },
        // New optimized keyframes
        bounceSubtle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-4px)' },
        },
        pulseFast: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.7' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
      // Дополнительные утилиты
      spacing: {
        18: '4.5rem',
        88: '22rem',
        128: '32rem',
      },
      fontSize: {
        '2xs': ['0.625rem', { lineHeight: '0.75rem' }],
      },
      zIndex: {
        60: '60',
        70: '70',
        80: '80',
        90: '90',
        100: '100',
      },
    },
  },
  plugins: [
    require('daisyui'),
    require('@tailwindcss/typography'),
    // Оптимизированные плагины
    function ({ addUtilities, addBase, addComponents, theme }) {
      // Базовые стили с оптимизацией
      addBase({
        '*': {
          'box-sizing': 'border-box',
        },
        'html': {
          'scroll-behavior': 'smooth',
          'text-rendering': 'optimizeLegibility',
          '-webkit-font-smoothing': 'antialiased',
          '-moz-osx-font-smoothing': 'grayscale',
        },
        'body': {
          'font-feature-settings': '"kern" 1',
          'text-rendering': 'optimizeLegibility',
        },
      });

      // Оптимизированные утилиты
      const newUtilities = {
        '.glass-effect': {
          background: 'rgba(255, 255, 255, 0.08)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.15)',
          willChange: 'backdrop-filter',
        },
        '.text-shadow': {
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.5)',
        },
        '.text-shadow-lg': {
          textShadow: '0 4px 8px rgba(0, 0, 0, 0.5)',
        },
        '.scrollbar-hide': {
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        },
        '.gpu-accelerated': {
          transform: 'translateZ(0)',
          willChange: 'transform',
        },
        '.performance-optimized': {
          containIntrinsicSize: 'auto',
          contentVisibility: 'auto',
        },
      };
      addUtilities(newUtilities);
    },
  ],
  daisyui: {
    themes: [
      {
        'tank-theme': {
          primary: '#7254e4',
          'primary-focus': '#5a3fb8',
          'primary-content': '#ffffff',
          secondary: '#a17bf8',
          'secondary-focus': '#8b5cf6',
          'secondary-content': '#ffffff',
          accent: '#ff6b6b',
          'accent-focus': '#ff5252',
          'accent-content': '#ffffff',
          neutral: '#1d1d2e',
          'neutral-focus': '#27233d',
          'neutral-content': '#e6e6e6',
          'base-100': '#1a1a2e',
          'base-200': '#16213e',
          'base-300': '#0f172a',
          'base-content': '#ffffff',
          info: '#3abff8',
          success: '#36d399',
          warning: '#fbbd23',
          error: '#f87272',
        },
      },
      'dark', // Fallback theme
    ],
    darkTheme: 'tank-theme',
    base: true,
    styled: true,
    utils: true,
    rtl: false,
    prefix: '',
    logs: process.env.NODE_ENV === 'development',
  },
};
