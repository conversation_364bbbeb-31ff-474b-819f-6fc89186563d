/**
 * ГЛОБАЛЬНЫЙ МЕНЕДЖЕР ОЧИСТКИ
 * Централизованная система для управления всеми задачами очистки
 * Предотвращает дублирование интервалов и оптимизирует производительность
 */

class GlobalCleanupManager {
  constructor() {
    this.cleanupFunctions = new Map();
    this.isRunning = false;
    this.cleanupInterval = null;
    this.lastCleanup = 0;
    
    this.init();
  }

  init() {
    if (this.isRunning) return;
    
    // Запускаем единый интервал очистки каждые 5 минут
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, 300000); // 5 минут
    
    // Очистка при скрытии страницы
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.performCleanup();
      }
    });
    
    // Очистка при выгрузке страницы
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });
    
    this.isRunning = true;
    console.log('🧹 GlobalCleanupManager initialized');
  }

  /**
   * Регистрация функции очистки
   */
  registerCleanupFunction(name, cleanupFn) {
    if (typeof cleanupFn !== 'function') {
      console.warn(`Invalid cleanup function for ${name}`);
      return;
    }
    
    this.cleanupFunctions.set(name, cleanupFn);
    console.log(`📝 Registered cleanup function: ${name}`);
  }

  /**
   * Удаление функции очистки
   */
  unregisterCleanupFunction(name) {
    const removed = this.cleanupFunctions.delete(name);
    if (removed) {
      console.log(`🗑️ Unregistered cleanup function: ${name}`);
    }
    return removed;
  }

  /**
   * Выполнение всех зарегистрированных функций очистки
   */
  performCleanup() {
    const now = Date.now();
    
    // Предотвращаем слишком частую очистку
    if (now - this.lastCleanup < 60000) { // Минимум 1 минута между очистками
      return;
    }
    
    console.log('🧹 Starting global cleanup...');
    let successCount = 0;
    let errorCount = 0;
    
    for (const [name, cleanupFn] of this.cleanupFunctions) {
      try {
        cleanupFn();
        successCount++;
      } catch (error) {
        console.error(`Error in cleanup function ${name}:`, error);
        errorCount++;
      }
    }
    
    this.lastCleanup = now;
    console.log(`✅ Global cleanup completed: ${successCount} success, ${errorCount} errors`);
  }

  /**
   * Принудительная очистка всех ресурсов
   */
  cleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    // Выполняем финальную очистку
    this.performCleanup();
    
    this.cleanupFunctions.clear();
    this.isRunning = false;
    
    console.log('🧹 GlobalCleanupManager cleaned up');
  }

  /**
   * Получение статистики
   */
  getStats() {
    return {
      registeredFunctions: this.cleanupFunctions.size,
      lastCleanup: this.lastCleanup,
      isRunning: this.isRunning
    };
  }
}

// Создаем глобальный экземпляр
export const globalCleanupManager = new GlobalCleanupManager();

// Делаем доступным глобально
if (typeof window !== 'undefined') {
  window.globalCleanupManager = globalCleanupManager;
}

export default globalCleanupManager;
