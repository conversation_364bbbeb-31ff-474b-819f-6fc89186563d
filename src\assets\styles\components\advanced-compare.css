/* ========================================
   РАСШИРЕННАЯ СИСТЕМА СРАВНЕНИЯ ТАНКОВ
   ======================================== */

/* ОСНОВНОЙ КОНТЕЙНЕР СРАВНЕНИЯ */
.advanced-compare-container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: var(--spacing-2xl);
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.1), rgba(161, 123, 248, 0.08));
  border-radius: 24px;
  border: 1px solid rgba(114, 84, 228, 0.2);
  backdrop-filter: blur(25px);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(114, 84, 228, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: slideInFromBottom 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* АНИМАЦИЯ ПОЯВЛЕНИЯ */
@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ЗАГОЛОВОК СЕКЦИИ */
.compare-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.compare-title-section h2.compare-main-title {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 0.5rem 0;
}

.compare-subtitle {
  color: #9ca3af;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.compare-stats {
  display: flex;
  gap: 1.5rem;
}

.compare-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-label {
  font-size: 0.875rem;
  color: #9ca3af;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #3b82f6;
}

/* ИНСТРУКЦИИ */
.compare-instructions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #e5e7eb;
  font-size: 0.875rem;
  line-height: 1.4;
}

.instruction-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  flex-shrink: 0;
}

.instruction-icon.active {
  background: linear-gradient(135deg, #10b981, #059669);
}

.instruction-icon.window {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.instruction-icon svg {
  width: 16px;
  height: 16px;
  color: white;
}

/* ОСНОВНЫЕ КНОПКИ ДЕЙСТВИЙ */
.compare-main-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.advanced-compare-button,
.advanced-clear-button,
.advanced-secondary-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.advanced-compare-button {
  background: linear-gradient(135deg, #7254e4, #5a3fb8);
  color: white;
  flex: 1;
  min-width: 200px;
}

.advanced-compare-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #6d47d9, #4c2a9e);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(114, 84, 228, 0.4);
}

.advanced-compare-button:disabled {
  background: rgba(114, 84, 228, 0.3);
  cursor: not-allowed;
  transform: none;
}

.advanced-clear-button {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.2);
}

.advanced-clear-button:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.2);
}

.advanced-secondary-button {
  background: rgba(255, 255, 255, 0.05);
  color: #9ca3af;
  border-color: rgba(255, 255, 255, 0.1);
}

.advanced-secondary-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  transform: translateY(-2px);
}

.button-icon {
  width: 18px;
  height: 18px;
}

/* РЕЗУЛЬТАТЫ СРАВНЕНИЯ */
.advanced-compare-results {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.results-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.results-controls {
  display: flex;
  gap: 0.5rem;
}

.results-export-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.results-export-btn:hover {
  background: rgba(59, 130, 246, 0.2);
}

.results-export-btn svg {
  width: 16px;
  height: 16px;
}

/* УЛЬТРА-ОПТИМИЗИРОВАННАЯ ТАБЛИЦА СРАВНЕНИЯ */
.advanced-comparison-table {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  max-width: 100vw;
}

.comparison-table-header {
  display: grid;
  grid-template-columns: 180px repeat(8, minmax(160px, 1fr));
  gap: 2px;
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.15), rgba(161, 123, 248, 0.1));
  padding: 1rem;
  overflow-x: auto;
  min-width: fit-content;
  border-radius: 16px 16px 0 0;
  border-bottom: 2px solid rgba(114, 84, 228, 0.3);
  backdrop-filter: blur(10px);
}

.comparison-category-header {
  font-weight: 700;
  color: #ffffff;
  padding: 1rem 0.75rem;
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.25), rgba(161, 123, 248, 0.2));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 0.9rem;
  min-width: 180px;
  position: sticky;
  left: 0;
  z-index: 10;
  border: 1px solid rgba(114, 84, 228, 0.4);
  box-shadow:
    0 4px 12px rgba(114, 84, 228, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.comparison-tank-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
  border-radius: 12px;
  padding: 1rem 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  min-width: 160px;
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.comparison-tank-header:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.08));
  border-color: rgba(114, 84, 228, 0.5);
  transform: translateY(-2px);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.tank-header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.tank-header-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tank-header-info {
  text-align: center;
}

.tank-header-name {
  font-weight: 600;
  color: #ffffff;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.tank-header-details {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  flex-wrap: wrap;
}

.tank-header-flag {
  width: 14px;
  height: 10px;
  border-radius: 2px;
}

.tank-header-type {
  font-size: 0.625rem;
  padding: 1px 4px;
  border-radius: 3px;
  font-weight: 500;
}

.tank-header-level {
  font-size: 0.625rem;
  color: #9ca3af;
}

/* УЛЬТРА-КОМПАКТНОЕ ТЕЛО ТАБЛИЦЫ */
.comparison-table-body {
  padding: 1rem;
  overflow-x: auto;
  max-width: 100%;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(255, 255, 255, 0.1);
}

/* КАСТОМНЫЙ СКРОЛЛБАР ДЛЯ WEBKIT */
.comparison-table-body::-webkit-scrollbar {
  height: 8px;
}

.comparison-table-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.comparison-table-body::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, rgba(114, 84, 228, 0.6), rgba(161, 123, 248, 0.6));
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.comparison-table-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, rgba(114, 84, 228, 0.8), rgba(161, 123, 248, 0.8));
}

.comparison-row {
  display: grid;
  grid-template-columns: 180px repeat(8, minmax(160px, 1fr));
  gap: 2px;
  margin-bottom: 2px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0.02));
  border-radius: 8px;
  overflow: hidden;
  min-width: fit-content;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
}

.comparison-row:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
  border-color: rgba(114, 84, 228, 0.3);
  transform: translateX(2px);
}

.comparison-category {
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.05));
  color: #f3f4f6;
  font-weight: 600;
  font-size: 0.8rem;
  display: flex;
  min-width: 180px;
  position: sticky;
  left: 0;
  z-index: 5;
  align-items: center;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.comparison-value {
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.06), rgba(255, 255, 255, 0.03));
  color: #ffffff;
  font-weight: 600;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-width: 160px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.2s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.comparison-value:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.06));
  border-color: rgba(114, 84, 228, 0.4);
}

.comparison-value.best {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.15));
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.4);
  box-shadow:
    0 0 20px rgba(16, 185, 129, 0.3),
    inset 0 1px 0 rgba(16, 185, 129, 0.2);
  font-weight: 700;
}

.comparison-value.worst {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.15));
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.4);
  box-shadow:
    0 0 20px rgba(239, 68, 68, 0.3),
    inset 0 1px 0 rgba(239, 68, 68, 0.2);
  font-weight: 700;
}

.comparison-value.equal {
  background: rgba(255, 255, 255, 0.03);
  color: #d1d5db;
}

/* БЕЙДЖИК СРАВНЕНИЯ НА КАРТОЧКАХ ТАНКОВ */
.tank-compare-badge {
  position: absolute;
  top: 36px;    /* Под бейджиком уровня */
  right: 8px;   /* Выравниваем с правым краем уровневого бейджика */
  width: 24px;  /* Размер как у уровневого бейджика */
  height: 24px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  z-index: 15;
  opacity: 1;
  transform: scale(1) translateY(0);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Эффект при наведении */
.tank-compare-badge:hover {
  transform: scale(1.1) translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.tank-compare-badge.active {
  background: linear-gradient(135deg, #10b981, #059669);
  border-color: rgba(16, 185, 129, 0.3);
}

.tank-compare-badge.active:hover {
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.tank-compare-badge svg {
  width: 14px;
  height: 14px;
  stroke-width: 2.5px;
  color: white;
}

.tank-compare-badge.active .compare-plus-icon {
  display: none;
}

.tank-compare-badge:not(.active) .compare-check-icon {
  display: none;
}

@keyframes fadeInBadge {
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* ВСПЛЫВАЮЩЕЕ ОКНО СРАВНЕНИЯ */
.compare-floating-window {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 380px;
  max-height: 600px;
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.98), rgba(22, 33, 62, 0.95));
  backdrop-filter: blur(25px);
  border-radius: 20px;
  border: 1px solid rgba(114, 84, 228, 0.3);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(114, 84, 228, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  z-index: 1000;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);
  opacity: 1;
}

.compare-floating-window.hidden {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
  pointer-events: none;
}

/* АДАПТИВНАЯ СИСТЕМА СРАВНЕНИЯ ДЛЯ 8 ТАНКОВ */
.ultra-comparison-container {
  width: 100%;
  max-width: 100vw;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, rgba(30, 39, 59, 0.95), rgba(45, 55, 75, 0.9));
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.ultra-comparison-scroll {
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.ultra-comparison-scroll::-webkit-scrollbar {
  height: 8px;
}

.ultra-comparison-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.ultra-comparison-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.ultra-comparison-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* КОМПАКТНЫЕ ЗНАЧЕНИЯ */
.comparison-value {
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  color: #ffffff;
  font-size: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-width: 140px;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.comparison-value:last-child {
  border-right: none;
}

.comparison-value.best {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  font-weight: 600;
}

.comparison-value.worst {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.comparison-value.equal {
  background: rgba(255, 255, 255, 0.03);
  color: #e5e7eb;
}

/* АДАПТИВНОСТЬ ДЛЯ РАЗНЫХ ЭКРАНОВ */
@media (max-width: 1920px) {
  .comparison-table-header {
    grid-template-columns: 140px repeat(8, minmax(120px, 1fr));
  }

  .comparison-row {
    grid-template-columns: 140px repeat(8, minmax(120px, 1fr));
  }

  .comparison-category {
    min-width: 140px;
  }

  .comparison-value {
    min-width: 120px;
    font-size: 0.7rem;
  }
}

/* УЛЬТРА-ШИРОКИЕ ЭКРАНЫ (2560px+) */
@media (min-width: 2560px) {
  .comparison-table-header {
    grid-template-columns: 220px repeat(8, minmax(200px, 1fr));
    padding: 1.5rem;
  }

  .comparison-row {
    grid-template-columns: 220px repeat(8, minmax(200px, 1fr));
  }

  .comparison-category-header,
  .comparison-category {
    min-width: 220px;
    font-size: 1.1rem;
    padding: 1.25rem;
  }

  .comparison-value {
    min-width: 200px;
    font-size: 1rem;
    padding: 1rem;
  }

  .tank-header-name {
    font-size: 1rem;
    max-width: 200px;
  }

  .tank-header-icon {
    width: 48px;
    height: 48px;
  }
}

/* ШИРОКИЕ ЭКРАНЫ (1920px-2559px) */
@media (min-width: 1920px) and (max-width: 2559px) {
  .comparison-table-header {
    grid-template-columns: 200px repeat(8, minmax(180px, 1fr));
    padding: 1.25rem;
  }

  .comparison-row {
    grid-template-columns: 200px repeat(8, minmax(180px, 1fr));
  }

  .comparison-category-header,
  .comparison-category {
    min-width: 200px;
    font-size: 1rem;
    padding: 1.125rem;
  }

  .comparison-value {
    min-width: 180px;
    font-size: 0.9rem;
    padding: 0.875rem;
  }

  .tank-header-name {
    font-size: 0.9rem;
    max-width: 180px;
  }

  .tank-header-icon {
    width: 42px;
    height: 42px;
  }
}

@media (max-width: 1600px) {
  .comparison-table-header {
    grid-template-columns: 140px repeat(8, minmax(120px, 1fr));
  }

  .comparison-row {
    grid-template-columns: 140px repeat(8, minmax(120px, 1fr));
  }

  .comparison-category {
    min-width: 140px;
    font-size: 0.75rem;
  }

  .comparison-value {
    min-width: 120px;
    font-size: 0.7rem;
    padding: 0.5rem 0.6rem;
  }

  .tank-header-name {
    font-size: 0.75rem;
    max-width: 120px;
  }

  .tank-header-icon {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 1200px) {
  .comparison-table-header {
    grid-template-columns: 100px repeat(8, minmax(80px, 1fr));
  }

  .comparison-row {
    grid-template-columns: 100px repeat(8, minmax(80px, 1fr));
  }

  .comparison-category {
    min-width: 100px;
    font-size: 0.65rem;
    padding: 0.4rem 0.5rem;
  }

  .comparison-value {
    min-width: 80px;
    font-size: 0.6rem;
    padding: 0.3rem 0.4rem;
  }

  .tank-header-name {
    font-size: 0.65rem;
    max-width: 80px;
  }

  .tank-header-icon {
    width: 28px;
    height: 28px;
  }
}

/* ЗАГОЛОВОК ОКНА */
.floating-window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.floating-window-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-weight: 600;
  font-size: 14px;
}

.floating-window-icon {
  width: 18px;
  height: 18px;
  color: #3b82f6;
}

.tank-count {
  background: linear-gradient(135deg, #7254e4, #5a3fb8);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.floating-window-close {
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.floating-window-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.floating-window-close svg {
  width: 16px;
  height: 16px;
}

/* СОДЕРЖИМОЕ ОКНА */
.floating-window-content {
  padding: 16px 20px 20px;
}

.floating-tanks-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.floating-tank-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.floating-tank-item:last-child {
  border-bottom: none;
}

.floating-tank-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.floating-tank-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.floating-tank-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.floating-tank-name {
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
  line-height: 1.2;
}

.floating-tank-type {
  color: #9ca3af;
  font-size: 11px;
  font-weight: 400;
}

.floating-tank-controls {
  display: flex;
  gap: 4px;
  align-items: center;
}

.floating-tank-duplicate,
.floating-tank-remove {
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.floating-tank-duplicate {
  color: #3b82f6;
}

.floating-tank-duplicate:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
}

.floating-tank-remove {
  color: #ef4444;
}

.floating-tank-remove:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.floating-tank-duplicate svg,
.floating-tank-remove svg {
  width: 14px;
  height: 14px;
}

/* ДЕЙСТВИЯ В ОКНЕ */
.floating-window-actions {
  display: flex;
  gap: 8px;
}

.floating-action-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.floating-action-btn svg {
  width: 16px;
  height: 16px;
}

.floating-action-btn.compare-btn {
  background: linear-gradient(135deg, #7254e4, #5a3fb8);
  color: white;
}

.floating-action-btn.compare-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #6d47d9, #4c2a9e);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(114, 84, 228, 0.4);
}

.floating-action-btn.compare-btn:disabled {
  background: rgba(114, 84, 228, 0.3);
  cursor: not-allowed;
}

.floating-action-btn.clear-btn {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.floating-action-btn.clear-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-1px);
}

/* СКРОЛЛБАР ДЛЯ СПИСКА ТАНКОВ */
.floating-tanks-list::-webkit-scrollbar {
  width: 4px;
}

.floating-tanks-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.floating-tanks-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.floating-tanks-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* КОНТЕЙНЕР ТАБЛИЦЫ СРАВНЕНИЯ */
.comparison-table-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* ПОЛНЫЙ КОНТЕЙНЕР СРАВНЕНИЯ */
.full-comparison-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

/* СЕКЦИЯ КАТЕГОРИИ */
.category-section {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.category-section:last-child {
  border-bottom: none;
}

/* ЗАГОЛОВОК КАТЕГОРИИ */
.category-header {
  padding: 1rem;
  cursor: default;
  user-select: none;
}

/* Hover эффект для category-header убран */

.category-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.category-icon {
  font-size: 1.25rem;
  width: 24px;
  text-align: center;
}

.category-name {
  font-size: 1rem;
  font-weight: 600;
  flex: 1;
}

.category-arrow {
  font-size: 0.875rem;
  color: #9ca3af;
  transition: transform 0.3s ease;
}

/* КОНТЕНТ КАТЕГОРИИ */
.category-content {
  overflow: hidden;
  transition: all 0.3s ease;
}

/* ТАБЛИЦА ХАРАКТЕРИСТИК */
.characteristics-table {
  background: rgba(255, 255, 255, 0.01);
}

.empty-table-message {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
  font-style: italic;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 2px dashed rgba(255, 255, 255, 0.1);
}

/* ЗАГОЛОВОК ТАБЛИЦЫ */
.table-header {
  display: grid;
  grid-template-columns: 250px repeat(auto-fit, minmax(150px, 1fr));
  gap: 1px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-cell {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  font-weight: 600;
  color: #e5e7eb;
}

.characteristic-header {
  background: rgba(124, 58, 237, 0.1);
  color: #a855f7;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.tank-header {
  position: relative;
}

.tank-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.tank-header-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  object-fit: cover;
  flex-shrink: 0;
}

.tank-header-info {
  flex: 1;
  min-width: 0;
}

.tank-header-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f3f4f6;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tank-header-details {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.25rem;
}

.tank-remove-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.tank-remove-btn:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

/* СТРОКИ КАТЕГОРИЙ */
.category-row {
  display: grid;
  grid-template-columns: 200px repeat(auto-fit, minmax(150px, 1fr));
  gap: 1px;
  background: rgba(255, 255, 255, 0.08);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.category-cell {
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.03);
  font-size: 0.875rem;
  font-weight: 600;
}

.category-tank-cell {
  background: rgba(255, 255, 255, 0.01);
}

/* СТРОКИ ХАРАКТЕРИСТИК */
.characteristic-row {
  display: grid;
  grid-template-columns: 250px repeat(auto-fit, minmax(150px, 1fr));
  gap: 1px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: background-color 0.2s ease;
}

.characteristic-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.characteristic-row.indented .characteristic-name-cell {
  padding-left: 2rem;
  font-style: italic;
  color: #9ca3af;
}

.indent-marker {
  color: #6b7280;
  margin-right: 0.5rem;
  font-family: monospace;
}

.characteristic-name-cell {
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.02);
  font-size: 0.875rem;
  color: #d1d5db;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
}

.characteristic-value-cell {
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.01);
  font-size: 0.875rem;
  color: #f3f4f6;
  text-align: center;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.characteristic-value-cell:hover {
  background: rgba(255, 255, 255, 0.03);
}

.unit {
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 400;
}

/* АДАПТИВНОСТЬ ДЛЯ ТАБЛИЦЫ СРАВНЕНИЯ */
@media (max-width: 1200px) {
  .table-header,
  .category-row,
  .characteristic-row {
    grid-template-columns: 180px repeat(auto-fit, minmax(120px, 1fr));
  }

  .tank-header-name {
    font-size: 0.8rem;
  }

  .tank-header-details {
    font-size: 0.7rem;
  }
}

@media (max-width: 768px) {
  .comparison-table {
    overflow-x: auto;
  }

  .table-header,
  .category-row,
  .characteristic-row {
    grid-template-columns: 150px repeat(auto-fit, minmax(100px, 1fr));
    min-width: 600px;
  }

  .header-cell,
  .category-cell,
  .characteristic-name-cell,
  .characteristic-value-cell {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .tank-header-content {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .tank-header-icon {
    width: 24px;
    height: 24px;
  }

  .main-add-tank-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .table-header,
  .category-row,
  .characteristic-row {
    grid-template-columns: 120px repeat(auto-fit, minmax(80px, 1fr));
  }

  .characteristic-name-cell {
    font-size: 0.7rem;
  }

  .characteristic-value-cell {
    font-size: 0.7rem;
  }

  .add-tank-section {
    padding: 1rem;
  }

  .main-add-tank-button {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }
}

/* ЗАГОЛОВОК COMPARE */
.compare-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 0 0 1.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.compare-title-section {
  flex: 1;
}

.compare-title {
  font-size: 2rem;
  font-weight: 700;
  color: #f3f4f6;
  margin: 0 0 0.5rem 0;
}

.compare-subtitle {
  color: #9ca3af;
  font-size: 1rem;
  margin: 0;
}

.compare-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.compare-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.compare-action-btn .btn-icon {
  width: 16px;
  height: 16px;
  stroke-width: 2.5;
}

.clear-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.clear-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.compare-btn {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  color: white;
  box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);
}

.compare-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(124, 58, 237, 0.4);
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.compare-btn:disabled {
  background: rgba(107, 114, 128, 0.5);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  box-shadow: none;
}

.compare-btn:disabled:hover {
  transform: none;
}

/* ПОСТОЯННАЯ ТАБЛИЦА СРАВНЕНИЯ */
.permanent-comparison-table {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

/* КОНТЕЙНЕР НОВОЙ СИСТЕМЫ СРАВНЕНИЯ */
.new-comparison-table {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* КОМПАКТНАЯ ТАБЛИЦА */
.compact-table {
  width: 100%;
  max-width: 100%;
  min-width: 100%;
}

/* ПОСТОЯННАЯ ТАБЛИЦА СРАВНЕНИЯ */
.permanent-comparison-table {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

.permanent-table {
  width: 100%;
}

/* ЗАГОЛОВОК ПОСТОЯННОЙ ТАБЛИЦЫ */
.permanent-table-header {
  display: grid;
  grid-template-columns: 350px repeat(12, minmax(60px, 1fr));
  gap: 0;
  background: rgba(255, 255, 255, 0.08);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  min-width: 100%;
}

.header-characteristics {
  padding: 1rem 0.75rem;
  background: rgba(124, 58, 237, 0.1);
  color: #a855f7;
  font-weight: 700;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.header-tank-slot {
  padding: 0.75rem 0.5rem;
  background: rgba(255, 255, 255, 0.03);
  min-height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  overflow: hidden;
  word-wrap: break-word;
  text-overflow: ellipsis;
}

.header-tank-slot:last-child {
  border-right: none;
}

/* Чередующиеся цвета для лучшего визуального разделения */
.header-tank-slot:nth-child(even) {
  background: rgba(255, 255, 255, 0.05);
}

.header-tank-slot:nth-child(odd) {
  background: rgba(255, 255, 255, 0.02);
}

.header-tank-slot:hover {
  background: rgba(255, 255, 255, 0.08) !important;
}

.header-tank-slot.has-tank {
  cursor: default;
}

.tank-header-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  width: 100%;
  position: relative;
}

.tank-icon {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  object-fit: cover;
}

.tank-details {
  text-align: center;
  flex: 1;
}

.tank-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: #f3f4f6;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  width: 100%;
}

.tank-meta {
  font-size: 0.65rem;
  color: #9ca3af;
  margin-top: 0.2rem;
}

.remove-tank-btn {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 0.6rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0.8;
}

.remove-tank-btn:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
  opacity: 1;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
}

.empty-tank-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%;
}

.empty-tank-slot:hover {
  border-color: rgba(124, 58, 237, 0.5);
  background: rgba(124, 58, 237, 0.1);
}

.empty-slot-icon {
  font-size: 1.5rem;
  color: #9ca3af;
  font-weight: bold;
}

.empty-slot-text {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: center;
}

/* СЕКЦИИ КАТЕГОРИЙ */
.permanent-category-section {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.permanent-category-section:last-child {
  border-bottom: none;
}

.permanent-category-header {
  display: grid;
  grid-template-columns: 350px repeat(12, minmax(60px, 1fr));
  gap: 0;
  cursor: default;
  user-select: none;
  width: 100%;
  min-width: 100%;
}

/* Hover эффект для permanent-category-header убран */

.category-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: none;
  overflow: hidden;
  width: 100%;
  max-width: 350px;
}

.category-name {
  font-size: 0.9rem;
  font-weight: 600;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 280px;
}

.category-arrow {
  font-size: 0.875rem;
  color: #9ca3af;
  transition: transform 0.3s ease;
}

.category-tank-headers {
  display: contents;
}

.category-tank-header {
  background: none;
}

/* КОНТЕНТ КАТЕГОРИЙ */
.permanent-category-content {
  overflow: hidden;
  transition: all 0.3s ease;
}

/* СТРОКИ ХАРАКТЕРИСТИК */
.permanent-characteristic-row {
  display: grid;
  grid-template-columns: 350px repeat(12, minmax(60px, 1fr));
  gap: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.03);
  transition: background-color 0.2s ease;
  width: 100%;
  min-width: 100%;
}


.permanent-characteristic-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.permanent-characteristic-row.indented .permanent-characteristic-name {
  padding-left: 2rem;
  font-style: italic;
  color: #9ca3af;
}

.permanent-characteristic-name {
  padding: 0.6rem 0.75rem;
  background: rgba(255, 255, 255, 0.02);
  font-size: 0.8rem;
  color: #d1d5db;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
}

.permanent-characteristic-value {
  padding: 0.6rem 0.4rem;
  background: rgba(255, 255, 255, 0.01);
  font-size: 0.75rem;
  color: #f3f4f6;
  text-align: center;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.2rem;
  min-height: 36px;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  overflow: hidden;
  word-wrap: break-word;
  text-overflow: ellipsis;
}

.permanent-characteristic-value:last-child {
  border-right: none;
}

/* Чередующиеся цвета для ячеек характеристик */
.permanent-characteristic-value:nth-child(even) {
  background: rgba(255, 255, 255, 0.03);
}

.permanent-characteristic-value:nth-child(odd) {
  background: rgba(255, 255, 255, 0.01);
}

.permanent-characteristic-value:hover {
  background: rgba(255, 255, 255, 0.08) !important;
}

.permanent-characteristic-value .unit {
  font-size: 0.7rem;
  color: #9ca3af;
  font-weight: 400;
}

/* ЦВЕТОВОЕ ВЫДЕЛЕНИЕ ЛУЧШИХ И ХУДШИХ ХАРАКТЕРИСТИК */
.permanent-characteristic-value.best,
.comparison-value.best {
  background: linear-gradient(135deg,
    rgba(34, 197, 94, 0.15) 0%,
    rgba(16, 185, 129, 0.1) 100%);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
  font-weight: 600;
  box-shadow: 0 0 10px rgba(34, 197, 94, 0.2);
}

.permanent-characteristic-value.worst,
.comparison-value.worst {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.15) 0%,
    rgba(220, 38, 38, 0.1) 100%);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
  font-weight: 600;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.2);
}

.permanent-characteristic-value.best .unit,
.comparison-value.best .unit {
  color: rgba(34, 197, 94, 0.8);
}

.permanent-characteristic-value.worst .unit,
.comparison-value.worst .unit {
  color: rgba(239, 68, 68, 0.8);
}

/* Hover эффекты для цветовых выделений */
.permanent-characteristic-value.best:hover,
.comparison-value.best:hover {
  background: linear-gradient(135deg,
    rgba(34, 197, 94, 0.25) 0%,
    rgba(16, 185, 129, 0.2) 100%);
  border-color: rgba(34, 197, 94, 0.5);
  box-shadow: 0 0 15px rgba(34, 197, 94, 0.3);
  transform: scale(1.02);
}

.permanent-characteristic-value.worst:hover,
.comparison-value.worst:hover {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.25) 0%,
    rgba(220, 38, 38, 0.2) 100%);
  border-color: rgba(239, 68, 68, 0.5);
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
  transform: scale(1.02);
}

.permanent-characteristic-value.equal:hover,
.comparison-value.equal:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: scale(1.01);
}

/* АДАПТИВНОСТЬ ДЛЯ ПОСТОЯННОЙ ТАБЛИЦЫ */
@media (max-width: 1400px) {
  .permanent-table-header,
  .permanent-category-header,
  .permanent-characteristic-row {
    grid-template-columns: 280px repeat(12, minmax(50px, 1fr));
  }

  .header-characteristics {
    padding: 1rem 0.75rem;
    font-size: 0.9rem;
  }

  .tank-name {
    font-size: 0.75rem;
    max-width: 80px;
  }

  .tank-meta {
    font-size: 0.65rem;
  }
}

@media (max-width: 1200px) {
  .permanent-table-header,
  .permanent-category-header,
  .permanent-characteristic-row {
    grid-template-columns: 250px repeat(12, minmax(45px, 1fr));
  }

  .permanent-characteristic-name {
    font-size: 0.8rem;
    padding: 0.6rem 0.75rem;
  }

  .permanent-characteristic-value {
    font-size: 0.75rem;
    padding: 0.6rem 0.25rem;
  }
}

@media (max-width: 768px) {
  .compare-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }

  .compare-title-section {
    text-align: center;
    width: 100%;
  }

  .compare-title {
    font-size: 1.5rem;
  }

  .compare-actions {
    width: 100%;
    justify-content: center;
  }

  .compare-action-btn {
    padding: 0.875rem 1.25rem;
    font-size: 0.85rem;
  }
}

/* Дублирующий медиа-запрос удален */

  .permanent-comparison-table {
    overflow-x: auto;
  }

  .permanent-table {
    min-width: 800px;
  }

  .permanent-table-header,
  .permanent-category-header,
  .permanent-characteristic-row {
    grid-template-columns: 200px repeat(12, minmax(40px, 1fr));
  }

  .header-tank-slot {
    padding: 0.75rem 0.5rem;
    min-height: 70px;
  }

  .tank-icon {
    width: 24px;
    height: 24px;
  }

  .tank-name {
    font-size: 0.7rem;
    max-width: 60px;
  }

  .tank-meta {
    font-size: 0.6rem;
  }

  .permanent-characteristic-name {
    font-size: 0.75rem;
    padding: 0.5rem;
  }

  .permanent-characteristic-value {
    font-size: 0.7rem;
    padding: 0.5rem 0.25rem;
  }

/* Дублирующий стиль удален - используется выше */

.compare-main-title {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 0.5rem 0;
}

.compare-subtitle {
  color: #9ca3af;
  font-size: 1rem;
  margin: 0;
}

.compare-control-panel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
  flex-wrap: wrap;
}

.tank-counter-display {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.counter-icon {
  font-size: 1.5rem;
}

.counter-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.counter-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #7c3aed;
}

.counter-label {
  font-size: 0.875rem;
  color: #9ca3af;
}

.compare-action-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* КНОПКИ ДЕЙСТВИЙ */
.compare-add-button,
.compare-clear-button,
.compare-start-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.compare-add-button {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.compare-add-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669, #047857);
}

.compare-clear-button {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.compare-clear-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.compare-start-button {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  color: white;
  box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);
}

.compare-start-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(124, 58, 237, 0.4);
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.compare-start-button:disabled {
  background: rgba(107, 114, 128, 0.5);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  box-shadow: none;
}

.btn-icon {
  width: 18px;
  height: 18px;
  stroke-width: 2.5;
}

/* АДАПТИВНОСТЬ ДЛЯ НОВОГО ИНТЕРФЕЙСА */
@media (max-width: 768px) {
  .compare-interface-header {
    padding: 1.5rem;
  }

  .compare-main-title {
    font-size: 1.5rem;
  }

  .compare-control-panel {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }

  .tank-counter-display {
    justify-content: center;
  }

  .compare-action-buttons {
    justify-content: center;
  }

  .compare-add-button,
  .compare-clear-button,
  .compare-start-button {
    flex: 1;
    min-width: 0;
    justify-content: center;
  }
}

/* СЕКЦИЯ ДОБАВЛЕНИЯ ТАНКОВ */
.add-tank-section {
  text-align: center;
  padding: 1.5rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.main-add-tank-button {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.main-add-tank-button:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.main-add-tank-button .button-icon {
  width: 20px;
  height: 20px;
}

.add-tank-hint {
  margin: 0.75rem 0 0 0;
  font-size: 0.875rem;
  color: #9ca3af;
}

/* ГОРИЗОНТАЛЬНЫЕ СЛОТЫ ТАНКОВ */
.tank-slots-header-horizontal {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  overflow-x: auto;
  padding: 1rem 0;
}

.characteristics-spacer {
  min-width: 150px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #e5e7eb;
  text-align: center;
}

.tank-slot-header {
  min-width: 100px;
  max-width: 120px;
  padding: 0.4rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 0.7rem;
  position: relative;
  flex-shrink: 0;
}

.tank-slot-header:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.tank-slot-header.has-tank {
  background: rgba(124, 58, 237, 0.1);
  border-color: rgba(124, 58, 237, 0.3);
}

.tank-header-compact {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  width: 100%;
}

.tank-icon-mini {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  object-fit: cover;
  flex-shrink: 0;
}

.tank-info-mini {
  flex: 1;
  min-width: 0;
  text-align: left;
}

.tank-name-mini {
  font-size: 0.65rem;
  font-weight: 600;
  color: #e5e7eb;
  line-height: 1.1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tank-details-mini {
  margin-top: 0.1rem;
}

.tank-level-mini {
  font-size: 0.6rem;
  color: #9ca3af;
  font-weight: 500;
}

.remove-tank-mini {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 0.7rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.remove-tank-mini:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

.empty-tank-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  height: 100%;
  color: #6b7280;
  transition: all 0.3s ease;
}

.empty-slot-icon {
  font-size: 1.2rem;
  font-weight: bold;
  color: #7c3aed;
}

.empty-slot-text {
  font-size: 0.6rem;
  text-align: center;
  line-height: 1.1;
}

.tank-slot-header:hover .empty-tank-slot {
  color: #9ca3af;
}

.tank-slot-header:hover .empty-slot-icon {
  color: #8b5cf6;
}

/* АДАПТИВНОСТЬ ДЛЯ КОМПАКТНОГО ИНТЕРФЕЙСА */
@media (max-width: 768px) {
  .tank-slot-header {
    min-width: 80px;
    max-width: 100px;
    padding: 0.3rem;
  }

  .tank-icon-mini {
    width: 20px;
    height: 20px;
  }

  .tank-name-mini {
    font-size: 0.6rem;
  }

  .tank-level-mini {
    font-size: 0.55rem;
  }

  .remove-tank-mini {
    width: 14px;
    height: 14px;
    font-size: 0.65rem;
    top: -3px;
    right: -3px;
  }

  .empty-slot-icon {
    font-size: 1rem;
  }

  .empty-slot-text {
    font-size: 0.55rem;
  }
}

@media (max-width: 480px) {
  .tank-slot-header {
    min-width: 70px;
    max-width: 85px;
    padding: 0.25rem;
  }

  .tank-header-compact {
    flex-direction: column;
    gap: 0.2rem;
  }

  .tank-icon-mini {
    width: 18px;
    height: 18px;
  }

  .tank-name-mini {
    font-size: 0.55rem;
    text-align: center;
  }

  .tank-level-mini {
    font-size: 0.5rem;
    text-align: center;
  }
}

/* АДАПТИВНОСТЬ */
@media (max-width: 1024px) {
  .advanced-compare-container {
    padding: 1.5rem;
  }

  .compare-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .compare-stats {
    justify-content: center;
  }

  .comparison-table-header {
    grid-template-columns: 150px repeat(auto-fit, minmax(140px, 1fr));
  }

  .comparison-row {
    grid-template-columns: 150px repeat(auto-fit, minmax(140px, 1fr));
  }

  .tank-header-name {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .advanced-compare-container {
    padding: 1rem;
    margin: 0 0.5rem;
  }

  .compare-main-title {
    font-size: 1.5rem;
  }

  .compare-instructions {
    grid-template-columns: 1fr;
    padding: 1rem;
  }

  .compare-main-actions {
    flex-direction: column;
  }

  .advanced-compare-button,
  .advanced-clear-button,
  .advanced-secondary-button {
    min-width: auto;
    width: 100%;
  }

  .comparison-table-header {
    grid-template-columns: 120px repeat(auto-fit, minmax(120px, 1fr));
    padding: 0.5rem;
  }

  .comparison-row {
    grid-template-columns: 120px repeat(auto-fit, minmax(120px, 1fr));
  }

  .comparison-category-header,
  .comparison-category {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .comparison-value {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .tank-header-content {
    gap: 0.5rem;
  }

  .tank-header-icon {
    width: 36px;
    height: 36px;
  }

  .tank-header-name {
    font-size: 0.75rem;
  }

  .tank-header-details {
    gap: 0.25rem;
  }

  .compare-floating-window {
    width: 280px;
    bottom: 10px;
    right: 10px;
  }

  .floating-window-header {
    padding: 12px 16px;
  }

  .floating-window-content {
    padding: 12px 16px 16px;
  }

  .tank-compare-badge {
    width: 24px;
    height: 24px;
    bottom: 6px;
    right: 6px;
  }

  .tank-compare-badge svg {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .advanced-compare-container {
    padding: 0.75rem;
    margin: 0 0.25rem;
  }

  .compare-main-title {
    font-size: 1.25rem;
  }

  .compare-subtitle {
    font-size: 0.875rem;
  }

  .instruction-item {
    font-size: 0.8rem;
  }

  .instruction-icon {
    width: 28px;
    height: 28px;
  }

  .comparison-table-header {
    grid-template-columns: 100px repeat(auto-fit, minmax(100px, 1fr));
  }

  .comparison-row {
    grid-template-columns: 100px repeat(auto-fit, minmax(100px, 1fr));
  }

  .tank-header-icon {
    width: 32px;
    height: 32px;
  }

  .compare-floating-window {
    width: calc(100vw - 20px);
    left: 10px;
    right: 10px;
  }
}
