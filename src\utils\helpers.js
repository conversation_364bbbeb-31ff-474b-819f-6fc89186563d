/**
 * Throttles a function, ensuring it's called at most once per interval.
 * @param {Function} fn - Function to throttle
 * @param {number} ms - Throttle interval in milliseconds
 * @returns {Function} Throttled function
 */
export const throttle = (fn, ms) => {
  let inThrottle;
  return function (...args) {
    if (!inThrottle) {
      fn.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), ms);
    }
  };
};

/**
 * Debounces a function, delaying execution until after wait milliseconds.
 * @param {Function} fn - Function to debounce
 * @param {number} ms - Debounce delay in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (fn, ms = 300) => {
  let timeout;
  return function (...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => fn.apply(this, args), ms);
  };
};

/**
 * Converts Arabic numerals to Roman numerals
 * @param {number} num - Number to convert (1-10)
 * @returns {string} Roman numeral
 */
export function toRomanNumeral(num) {
  const romanNumerals = {
    1: 'I',
    2: 'II',
    3: 'III',
    4: 'IV',
    5: 'V',
    6: 'VI',
    7: 'VII',
    8: 'VIII',
    9: 'IX',
    10: 'X'
  };

  return romanNumerals[num] || num.toString();
}

/**
 * Slugifies a string: lowercases latin letters, replaces spaces and non-alphanumerics with hyphens
 * @param {string} name - String to slugify
 * @returns {string} Slugified string
 */
export function slugify(name) {
  if (!name || typeof name !== 'string') return '';

  return name
    .toString()
    .normalize('NFD') // decompose accents
    .replace(/[^\w\s-а-яА-Я]/g, '') // remove non word/space/hyphen (including punctuation)
    .replace(/[\u0300-\u036f]/g, '') // remove diacritics
    .toLowerCase()
    .replace(/\s+/g, '-') // replace spaces with hyphens
    .replace(/-+/g, '-') // collapse multiple hyphens
    .replace(/^-|-$/g, ''); // trim leading/trailing hyphens
}

import { getTankIconPath, TANK_NAME_TO_ICON_MAP } from './constants.js';

/**
 * Creates a tank ID from tank name using the constants module
 * @param {string} tankName - Tank name to create ID from
 * @returns {string} Tank ID
 */
export function createTankId(tankName) {
  if (!tankName || typeof tankName !== 'string') return '';

  try {
    // Используем функцию из constants.js для получения имени файла иконки
    const iconPath = getTankIconPath(tankName);
    // Извлекаем имя файла без расширения и пути
    return iconPath.split('/').pop().replace('.webp', '');
  } catch (error) {
    console.warn('Error creating tank ID:', error);
    return slugify(tankName);
  }
}

/**
 * Creates a URL-friendly tank ID from tank name using latin names from constants
 * @param {string} tankName - Tank name to create URL ID from
 * @returns {string} URL-friendly ID in latin characters
 */
export function createTankUrlId(tankName) {
  if (!tankName || typeof tankName !== 'string') return '';

  // Ищем латинское название в карте
  const latinName = TANK_NAME_TO_ICON_MAP[tankName];

  if (latinName) {
    // Используем готовое латинское название
    return latinName;
  }

  // Fallback: если нет в карте, создаем из русского названия
  return tankName
    .trim()
    // Заменяем пробелы на дефисы
    .replace(/\s+/g, '-')
    // Убираем все спецсимволы кроме букв, цифр и дефисов
    .replace(/[^\w-]/g, '')
    // Множественные дефисы в один
    .replace(/-+/g, '-')
    // Убираем дефисы в начале и конце
    .replace(/^-|-$/g, '')
    // В нижний регистр
    .toLowerCase();
}

/**
 * Finds tank name by URL ID (reverse lookup)
 * @param {string} urlId - URL ID to find tank name for
 * @returns {string|null} Tank name or null if not found
 */
export function findTankNameByUrlId(urlId) {
  if (!urlId || typeof urlId !== 'string') return null;

  // Ищем в карте по значению (латинскому названию)
  for (const [tankName, latinName] of Object.entries(TANK_NAME_TO_ICON_MAP)) {
    if (latinName === urlId) {
      return tankName;
    }
  }

  return null;
}

/**
 * Formats a number with thousands separators
 * @param {number} num - Number to format
 * @returns {string} Formatted number
 */
export function formatNumber(num) {
  if (typeof num !== 'number' || isNaN(num)) return '0';
  return num.toLocaleString();
}

/**
 * Clamps a number between min and max values
 * @param {number} value - Value to clamp
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {number} Clamped value
 */
export function clamp(value, min, max) {
  return Math.min(Math.max(value, min), max);
}

/**
 * Generates a random ID string
 * @param {number} length - Length of the ID
 * @returns {string} Random ID
 */
export function generateId(length = 8) {
  return Math.random().toString(36).substring(2, 2 + length);
}

/**
 * Deep clones an object
 * @param {any} obj - Object to clone
 * @returns {any} Cloned object
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  return obj;
}

/**
 * Checks if a value is empty (null, undefined, empty string, empty array, empty object)
 * @param {any} value - Value to check
 * @returns {boolean} True if empty
 */
export function isEmpty(value) {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
}
