// Tank types constants
export const TANK_TYPES = {
  LIGHT: 'ЛТ',
  MEDIUM: 'СТ',
  HEAVY: 'ТТ',
  TD: 'ПТ-САУ',
  SPG: 'САУ',
};

// Tank roles constants
export const TANK_ROLES = {
  Универсальный: { name: 'Универсальный', icon: '/src/assets/images/role/universal.png' },
  Штурмовой: { name: 'Штурмовой', icon: '/src/assets/images/role/assault.png' },
  Снайперский: { name: 'Снайперский', icon: '/src/assets/images/role/sniper.png' },
  Прорыва: { name: 'Прорыва', icon: '/src/assets/images/role/break.png' },
  Поддержки: { name: 'Поддержки', icon: '/src/assets/images/role/support.png' },
  Колёсный: { name: 'Колёсный', icon: '/src/assets/images/role/wheeled.png' },
};

// Tank levels constants
export const TANK_LEVELS = {
  VI: 6,
  VII: 7,
  VIII: 8,
  IX: 9,
  X: 10
};

// Tank level constant (default for top tier)
export const TANK_LEVEL = 10;

// Функция для определения уровня танка по имени
const getTankTier = (tankName) => {
  // Большинство танков в игре - 10 уровень
  const tier10Tanks = [
    'Т-100 ЛТ', 'Объект 907', 'Объект 430У', 'Т-22 ср.', 'Т-62А', 'Объект 140', 'К-91',
    'ИС-4', 'Объект 705А', 'СТ-II', 'ИС-7', 'Объект 277', 'Объект 780', 'Объект 279 ранний',
    'Объект 260', 'ИС-7 «Драгун»', 'Объект 268 Вариант 4', 'Об. 268/4 Морион', 'Объект 268',
    'Объект 268 Вариант 5', 'СУ-122В', 'WT E 100 Оруженосец', 'Объект 261 Вариант 4', 'Объект 261',
    'Rheinmetall Panzerwagen', 'LKpz.70 K', 'E 50 Ausf. M', 'Leopard 1', 'Erich Konzept I',
    'Maus', 'E 100', 'Pz.Kpfw. VII', 'VK 72.01 (K)', 'Kampfpanzer 07 P(E)', 'Jagdpanzer E 100',
    'Grille 15', 'Waffenträger auf E 100', 'G.W. E 100', 'XM551 Sheridan', 'M48A5 Patton',
    'M60', 'T95E6', 'T110E5', 'M-V-Y', 'T57 Heavy Tank', 'H-3', 'Astron-FL', 'Grayhound',
    'T110E4', 'T110E3', 'XM57', 'T92 HMC', 'WZ-132-1', '121', '121B', 'Царь обезьян',
    'DZT-159', 'BZ-75', '116-F3', '113', 'WZ-111 model 5A', 'BZ-74-1', 'WZ-111 Qilin',
    '113 Beijing Opera', 'GPT-75', 'WZ-113G FT'
  ];

  // Некоторые танки 9 уровня
  const tier9Tanks = [
    'Т-54', 'ИС-8', 'СУ-122-54', 'Объект 704', 'Объект 212А',
    'E 75', 'Jagdtiger', 'G.W. Tiger P', 'M46 Patton', 'T95', 'M53/M55',
    'WZ-120', 'WZ-111 model 1-4', 'WZ-120-1G FT', 'G.W. Tiger'
  ];

  // Некоторые танки 8 уровня
  const tier8Tanks = [
    'Т-44', 'ИС-3', 'СУ-152', 'ИСУ-152', 'С-51',
    'Tiger II', 'Ferdinand', 'G.W. Tiger', 'T32', 'T28', 'M40/M43',
    'T-34-2', 'IS-2', 'WZ-131G FT', 'Lorraine 40 t'
  ];

  if (tier10Tanks.includes(tankName)) return { level: TANK_LEVELS.X, tier: 'X' };
  if (tier9Tanks.includes(tankName)) return { level: TANK_LEVELS.IX, tier: 'IX' };
  if (tier8Tanks.includes(tankName)) return { level: TANK_LEVELS.VIII, tier: 'VIII' };

  // По умолчанию - 10 уровень
  return { level: TANK_LEVELS.X, tier: 'X' };
};

const tanksData = {
  USSR: {
    [TANK_TYPES.LIGHT]: [
      {
        name: 'Т-100 ЛТ',
        level: TANK_LEVELS.X,
        tier: 'X',
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/t-100-lt.webp',
        characteristics: {},
        cost: 6100000,
        bestBuilds: [
          {
            name: 'Разведка',
            efficiency: 92.5,
            contribution: 15.2,
            damage: 1850,
            assist: 2100,
            dpm: 2850,
            spread: 0.0285,
            dynSpread: 0.0195,
            aimTime: 1.85,
            viewRange: 445,
            dispersion: 0.32,
            cv: 8.5,
            accuracy: 94.2,
            equipment: ['Улучшенная оптика', 'Стабилизатор вертикальной наводки', 'Вентиляция'],
          },
          {
            name: 'Активная разведка',
            efficiency: 89.1,
            contribution: 13.8,
            damage: 1650,
            assist: 2350,
            dpm: 2650,
            spread: 0.0295,
            dynSpread: 0.0205,
            aimTime: 1.95,
            viewRange: 465,
            dispersion: 0.34,
            cv: 9.2,
            accuracy: 91.8,
            equipment: ['Улучшенная оптика', 'Маскировочная сеть', 'Вентиляция'],
          },
        ],
      },
    ],
    [TANK_TYPES.MEDIUM]: [
      {
        name: 'Объект 907',
        level: TANK_LEVELS.X,
        tier: 'X',
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/object-907.webp',
        characteristics: {},
        cost: 15000,
        currencyIcon: 'src/assets/images/role/boni.png',
        bestBuilds: [
          {
            name: 'Штурм',
            efficiency: 95.8,
            contribution: 18.5,
            damage: 2450,
            assist: 1850,
            dpm: 3250,
            spread: 0.0265,
            dynSpread: 0.0175,
            aimTime: 2.15,
            viewRange: 420,
            dispersion: 0.29,
            cv: 7.2,
            accuracy: 96.5,
            equipment: ['Стабилизатор вертикальной наводки', 'Досылатель', 'Вентиляция'],
          },
          {
            name: 'Универсальная',
            efficiency: 93.2,
            contribution: 17.1,
            damage: 2250,
            assist: 1950,
            dpm: 3150,
            spread: 0.0275,
            dynSpread: 0.0185,
            aimTime: 2.05,
            viewRange: 435,
            dispersion: 0.31,
            cv: 7.8,
            accuracy: 94.8,
            equipment: ['Стабилизатор вертикальной наводки', 'Улучшенная оптика', 'Вентиляция'],
          },
          {
            name: 'Снайперская',
            efficiency: 91.5,
            contribution: 16.8,
            damage: 2350,
            assist: 1750,
            dpm: 3050,
            spread: 0.0255,
            dynSpread: 0.0165,
            aimTime: 1.95,
            viewRange: 410,
            dispersion: 0.27,
            cv: 6.9,
            accuracy: 97.2,
            equipment: ['Стабилизатор вертикальной наводки', 'Досылатель', 'Улучшенная наводка'],
          },
        ],
      },
      {
        name: 'Объект 430У',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/object-430u.webp',
        characteristics: {},
      },
      {
        name: 'Т-22 ср.',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/t-22-medium.webp',
        characteristics: {},
      },
      {
        name: 'Т-62А',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/t-62a.webp',
        characteristics: {
          reloadTime: 6.5,
          viewRange: 400,
          dispersion: 0.35,
          aimTime: 2.1,
          armor: {
            front: 100,
            side: 80,
            rear: 45,
            turret: {
              front: 240,
              side: 160,
              rear: 65,
            },
          },
          // Преобразуем значения скорости в отдельные поля
          speed: 50,
          reverseSpeed: 20,
          hullRotation: 45,
          turretRotation: 45,
          gun: {
            damage: {
              min: 320,
              max: 530,
              avg: 390,
            },
            penetration: {
              standard: 201,
              premium: 250,
              gold: 330,
            },
          },
          engine: {
            power: 580,
            fireChance: 0.12,
          },
          radio: {
            range: 750,
          },
        },
      },
      {
        name: 'Объект 140',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/object-140.webp',
        characteristics: {},
      },
      {
        name: 'К-91',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/k-91.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.HEAVY]: [
      {
        name: 'ИС-4',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/is-4.webp',
        characteristics: {},
      },
      {
        name: 'Объект 705А',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/object-705a.webp',
        characteristics: {},
      },
      {
        name: 'СТ-II',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/st-ii.webp',
        characteristics: {},
      },
      {
        name: 'ИС-7',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/is-7.webp',
        characteristics: {},
      },
      {
        name: 'Объект 277',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/object-277.webp',
        characteristics: {},
      },
      {
        name: 'Объект 780',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/object-780.webp',
        characteristics: {},
      },
      {
        name: 'Объект 279 ранний',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/object-279r.webp',
        characteristics: {},
      },
      {
        name: 'Объект 260',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/object-260.webp',
        characteristics: {},
      },
      {
        name: 'ИС-7 «Драгун»',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/is-7-02.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.TD]: [
      {
        name: 'Объект 268 Вариант 4',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/object-268-4.webp',
        characteristics: {},
      },
      {
        name: 'Об. 268/4 Морион',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/object-268-4-02.webp',
        characteristics: {},
      },
      {
        name: 'Объект 268',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/object-268.webp',
        characteristics: {},
      },
      {
        name: 'Объект 268 Вариант 5',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/object-730-5.webp',
        characteristics: {},
      },
      {
        name: 'СУ-122В',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/su-122v.webp',
        characteristics: {},
      },
      {
        name: 'WT E 100 Оруженосец',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/waffentrager-auf-e-100-gold.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.SPG]: [
      {
        name: 'Объект 261 Вариант 4',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/object-261-4-ii.webp',
        characteristics: {},
      },
      {
        name: 'Объект 261',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/object-261.webp',
        characteristics: {},
      },
    ],
  },
  Germany: {
    [TANK_TYPES.LIGHT]: [
      {
        name: 'Rheinmetall Panzerwagen',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/rhm-pzw.webp',
        characteristics: {},
      },
      {
        name: 'LKpz.70 K',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/lkpz-70-k.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.MEDIUM]: [
      {
        name: 'E 50 Ausf. M',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/e-50-ausf-m.webp',
        characteristics: {},
      },
      {
        name: 'Leopard 1',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/leopard-1.webp',
        characteristics: {},
      },
      {
        name: 'Erich Konzept I',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/erich-konzept-i.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.HEAVY]: [
      {
        name: 'Maus',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/maus.webp',
        characteristics: {},
      },
      {
        name: 'E 100',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/e-100.webp',
        characteristics: {},
      },
      {
        name: 'Pz.Kpfw. VII',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/pzkpfw-vii.webp',
        characteristics: {},
      },
      {
        name: 'VK 72.01 (K)',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/vk-72-01-k.webp',
        characteristics: {},
      },
      {
        name: 'Kampfpanzer 07 P(E)',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/kpz-07p-e.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.TD]: [
      {
        name: 'Jagdpanzer E 100',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/jagdpz-e-100.webp',
        cost: 6100000,
        baseCharacteristics: {
          damage: 1050,
          dpm: 2556.77,
          penetration: 299,
          penetrationAt500m: 289,
          reloadTime: 24.64,
          rateOfFire: 2.44,
          aimTime: 2.59,
          dispersion: 0.34,
          dispersionDamaged: 0.67,
          dispersionMoving: 0.18,
          dispersionTankTraverse: 0.18,
          dispersionTurretTraverse: 0.2,
          dispersionAfterFiring: 4,
          dispersionFactorDamaged: 2,
          damageVsModules: 228,
          caliber: 170,
          shellVelocity: 925,
          maxRange: 720,
          ammoCapacity: 24,
          potentialDamage: 25200,
          shellCost: 1310,
          gunDepression: 6,
          gunElevation: 17,
          speed: 30,
          reverseSpeed: 12,
          weight: 133940,
          engine: {
            power: 720,
            fireChance: 0.15,
          },
          mobility: {
            traverse: 20.86,
            turretTraverse: 27.12,
          },
          armor: {
            front: 200,
            side: 120,
            rear: 150,
            turret: {
              front: 0,
              side: 0,
              rear: 0,
            },
          },
          hitPoints: 2200,
          viewRange: 390,
          radioRange: 750.96,
        },
        characteristics: {
          dpm: 2556.77,
          damage: 1050,
          penetration: 299,
          penetrationAt500m: 289,
          reloadTime: 24.64,
          rateOfFire: 2.44,
          aimTime: 2.59,
          dispersion: 0.34,
          speed: 30,
          reverseSpeed: 12,
          enginePower: 1268.74,
          powerToWeight: 9.47,
          traverse: 20.86,
          hitPoints: 2200,
          viewRange: 390,
          radioRange: 750.96,
          fireChance: 15,
          hullArmor: {
            front: 200,
            side: 120,
            rear: 150,
          },
          turretArmor: {
            front: 0,
            side: 0,
            rear: 0,
          },
          trackArmor: 40,
          engineHealth: 360,
          trackHealth: 300,
          trackRepairedHealth: 225,
          trackRepairTime: 12.03,
          ammoRackHealth: 280,
          ammoRackRepairedHealth: 190,
          fuelTankHealth: 200,
          fuelTankRepairedHealth: 70,
          weight: 133940,
          camouflage: {
            stationary: {
              base: 3.02,
              firing: 0.33,
            },
            moving: {
              base: 1.82,
              firing: 0.2,
            },
          },
          gunTraverseSpeed: 23.63,
          gunTraverseRange: '-8 / 8',
          turretTraverseSpeed: 27.12,
          terrainResistance: {
            hard: 1.05,
            medium: 1.25,
            soft: 2.21,
          },
          gunElevation: 17,
          gunDepression: 6,
          effectiveSpeed: {
            hard: 30,
            medium: 27.73,
            soft: 15.67,
          },
          effectiveTraverse: {
            hard: 19.82,
            medium: 16.77,
            soft: 9.48,
          },
          dispersionFactors: {
            moving: 0.18,
            tankTraverse: 0.18,
            turretTraverse: 0.2,
            afterFiring: 4,
            damaged: 2,
          },
          damageVsModules: 228,
          caliber: 170,
          shellVelocity: 925,
          maxRange: 720,
          ammoCapacity: 24,
          potentialDamage: 25200,
        },
      },
      {
        name: 'Grille 15',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/grille-15.webp',
        characteristics: {},
      },
      {
        name: 'Waffenträger auf E 100',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/waffentrager-auf-e-100.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.SPG]: [
      {
        name: 'G.W. E 100',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/g-w-e-100.webp',
        characteristics: {},
      },
    ],
  },
  USA: {
    [TANK_TYPES.LIGHT]: [
      {
        name: 'XM551 Sheridan',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/xm551-sheridan.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.MEDIUM]: [
      {
        name: 'M48A5 Patton',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/m48a5-patton.webp',
        characteristics: {},
      },
      {
        name: 'M60',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/m60.webp',
        characteristics: {},
      },
      {
        name: 'T95E6',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/t95e6.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.HEAVY]: [
      {
        name: 'T110E5',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/t110e5.webp',
        characteristics: {},
      },
      {
        name: 'M-V-Y',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/m-v-y.webp',
        characteristics: {},
      },
      {
        name: 'T57 Heavy Tank',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/t57-heavy.webp',
        characteristics: {},
      },
      {
        name: 'H-3',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/h-3.webp',
        characteristics: {},
      },
      {
        name: 'Astron-FL',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/astron-fl.webp',
        characteristics: {},
      },
      {
        name: 'Grayhound',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/grayhound.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.TD]: [
      {
        name: 'T110E4',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/t110e4.webp',
        characteristics: {},
      },
      {
        name: 'T110E3',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/t110e3.webp',
        characteristics: {},
      },
      {
        name: 'XM57',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/xm57.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.SPG]: [
      {
        name: 'T92 HMC',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/t92-hmc.webp',
        characteristics: {},
      },
    ],
  },
  China: {
    [TANK_TYPES.LIGHT]: [
      {
        name: 'WZ-132-1',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/wz-132-1.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.MEDIUM]: [
      {
        name: '121',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/121.webp',
        characteristics: {},
      },
      {
        name: '121B',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/121b.webp',
        characteristics: {},
      },
      {
        name: 'Царь обезьян',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/121b-mk.webp',
        characteristics: {},
      },
      {
        name: 'DZT-159',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/dzt-159.webp',
        characteristics: {},
        cost: 25000,
        currencyIcon: 'src/assets/images/role/boni.png',
        bestBuilds: [
          {
            name: 'Штурм',
            efficiency: 94.2,
            contribution: 17.8,
            damage: 2350,
            assist: 1650,
            dpm: 3180,
            spread: 0.0275,
            dynSpread: 0.0185,
            aimTime: 2.25,
            viewRange: 405,
            dispersion: 0.31,
            cv: 7.5,
            accuracy: 95.8,
            equipment: ['Стабилизатор вертикальной наводки', 'Досылатель', 'Вентиляция'],
          },
          {
            name: 'Универсальная',
            efficiency: 91.8,
            contribution: 16.2,
            damage: 2150,
            assist: 1850,
            dpm: 2980,
            spread: 0.0285,
            dynSpread: 0.0195,
            aimTime: 2.15,
            viewRange: 420,
            dispersion: 0.33,
            cv: 8.1,
            accuracy: 93.5,
            equipment: ['Стабилизатор вертикальной наводки', 'Улучшенная оптика', 'Вентиляция'],
          },
          {
            name: 'Снайперская',
            efficiency: 89.5,
            contribution: 15.5,
            damage: 2250,
            assist: 1550,
            dpm: 2850,
            spread: 0.0265,
            dynSpread: 0.0175,
            aimTime: 2.05,
            viewRange: 395,
            dispersion: 0.29,
            cv: 7.2,
            accuracy: 96.8,
            equipment: ['Стабилизатор вертикальной наводки', 'Досылатель', 'Улучшенная наводка'],
          },
        ],
      },
    ],
    [TANK_TYPES.HEAVY]: [
      {
        name: 'BZ-75',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/bz-75.webp',
        characteristics: {},
      },
      {
        name: '116-F3',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/wz-122-6-f3.webp',
        characteristics: {},
      },
      {
        name: '113',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/113.webp',
        characteristics: {},
      },
      {
        name: 'WZ-111 model 5A',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/wz-111-model-5a.webp',
        characteristics: {},
      },
      {
        name: 'BZ-74-1',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/bz-74-1.webp',
        characteristics: {},
      },
      {
        name: 'WZ-111 Qilin',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/wz-111-qilin.webp',
        characteristics: {},
      },
      {
        name: '113 Beijing Opera',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/113-beijing-opera.webp',
        characteristics: {},
      },
      {
        name: 'GPT-75',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/gpt-75.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.TD]: [
      {
        name: 'WZ-113G FT',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/wz-113g-ft.webp',
        characteristics: {},
      },
      {
        name: '114 SP2',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/114-sp2.webp',
        characteristics: {},
      },
    ],
  },
  France: {
    [TANK_TYPES.LIGHT]: [
      {
        name: 'AMX 13 105',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/amx-13-105.webp',
        characteristics: {},
      },
      {
        name: 'Panhard EBR 105',
        level: TANK_LEVEL,
        role: TANK_ROLES['Колёсный'],
        icon: '/src/assets/images/tanks/panhard-ebr-105.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.MEDIUM]: [
      {
        name: 'Bat.-Châtillon 25 t',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/bat-chatillon-25t.webp',
        characteristics: {},
      },
      {
        name: 'Projet Murat',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/projet-murat.webp',
        characteristics: {},
      },
      {
        name: 'AMX 30 B',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/amx-30-b.webp',
        equipmentSlot1Image: 'src/assets/images/equipment/EquipSlot2Mobility.png',
        cost: 6100000,
        baseCharacteristics: {
          damage: 420,
          dpm: 3348.23,
          penetration: 248,
          penetrationAt500m: 232,
          reloadTime: 7.53,
          rateOfFire: 7.97,
          aimTime: 1.92,
          dispersion: 0.35,
          dispersionDamaged: 0.69,
          dispersionMoving: 0.12,
          dispersionTankTraverse: 0.12,
          dispersionTurretTraverse: 0.04,
          dispersionAfterFiring: 3,
          dispersionFactorDamaged: 2,
          damageVsModules: 150,
          caliber: 105,
          shellVelocity: 1300,
          maxRange: 720,
          ammoCapacity: 50,
          potentialDamage: 21000,
          shellCost: 1310,
          gunDepression: 8,
          gunElevation: 20,
          speed: 65,
          reverseSpeed: 23,
          weight: 36,
          engine: {
            power: 720,
            fireChance: 0.1,
            health: 360,
          },
          mobility: {
            traverse: 50.06,
            turretTraverse: 39.63,
            terrainResistance: {
              hard: 0.86,
              medium: 1.05,
              soft: 2.01,
            },
            effectiveSpeed: {
              hard: 65,
              medium: 65,
              soft: 38.32,
            },
            effectiveTraverse: {
              hard: 47.56,
              medium: 38.91,
              soft: 20.38,
            },
          },
          armor: {
            front: 80,
            side: 35,
            rear: 30,
            turret: {
              front: 150,
              side: 40,
              rear: 30,
            },
            track: 20,
          },
          hitPoints: 1900,
          viewRange: 410,
          radioRange: 782.25,
          modules: {
            track: {
              health: 250,
              repairedHealth: 190,
              repairTime: 12.03,
            },
            ammoRack: {
              health: 260,
              repairedHealth: 180,
            },
            fuelTank: {
              health: 200,
              repairedHealth: 70,
            },
          },
          shells: [
            {
              type: 'AP',
              name: 'OCC 105 F1 mle. 60',
              damage: 420,
              penetration: 248,
              velocity: 1300,
              weight: 15.8,
              normalization: 5,
              ricochetAngle: 70,
              cost: 1310,
            },
            {
              type: 'HEAT',
              name: 'OFL 105 F1 mle. 60',
              damage: 420,
              penetration: 300,
              velocity: 1200,
              weight: 12.6,
              normalization: 2,
              ricochetAngle: 85,
              cost: 6200,
            },
            {
              type: 'HE',
              name: 'OE 105 F1 mle. 60',
              damage: 510,
              penetration: 53,
              velocity: 900,
              weight: 15.0,
              explosionRadius: 3.36,
              cost: 900,
            },
          ],
          camouflage: {
            stationary: {
              base: 15.28,
              firing: 3.19,
            },
            moving: {
              base: 11.46,
              firing: 2.39,
            },
          },
          gunName: '105 mm mle. F1',
          gunAimingArc: 360,
          gunTraverseSpeed: 21.88,
          gunTraverseRange: '-180 / 180',
          gunAccuracy: {
            '100m': 0.35,
            '500m': 1.75,
            '1000m': 3.5,
          },
          gunStabilization: {
            vertical: 0.2,
            horizontal: 0.2,
          },
          gunDurability: 240,
          gunRepairTime: 14.5,
          gunCriticalChance: 0.1,
        },
        characteristics: {
          // Основные характеристики орудия - округленные значения
          dpm: 3348.2,
          damage: 420,
          penetration: 248,
          penetration500m: 238, // Бронепробитие на 500м
          reloadTime: 7.5,
          rateOfFire: 8.0,
          aimTime: 1.9,
          dispersion: 0.3,

          // Дополнительные характеристики орудия
          dispersionDamage: 0.2, // Разброс (повреждение)
          moveDispersion: 0.12, // Разброс при движении
          hullTraverseDispersion: 0.11, // Разброс при повороте корпуса
          turretTraverseDispersion: 0.08, // Разброс при повороте башни
          afterShotDispersion: 3.2, // Разброс после выстрела
          dispersionFactor: 1.5, // Фактор разброса
          modulesDamage: 124, // Урон по модулям
          caliber: 105, // Калибр орудия
          shellVelocity: 1185, // Скорость снаряда
          maxRange: 720, // Максимальная дальность
          ammoCapacity: 45, // Боекомплект
          potentialDamage: 18900, // Потенциальный урон (damage × ammoCapacity)
          shellCost: 580, // Стоимость снаряда

          // Базовые характеристики танка
          speed: 65,
          reverseSpeed: 23,
          enginePower: 720,
          powerToWeight: 21.15,
          traverse: 50.06,
          hitPoints: 1900,
          viewRange: 410,
          radioRange: 782.25,
          fireChance: 10,

          // Бронирование
          hullArmor: {
            front: 80,
            side: 35,
            rear: 30,
          },
          turretArmor: {
            front: 150,
            side: 40,
            rear: 30,
          },
          trackArmor: 20,

          // Базовые характеристики модулей
          engineHealth: 360,
          trackHealth: 250,
          weight: 36000,

          // Камуфляж
          camouflage: {
            stationary: {
              base: 15.28,
              firing: 3.19,
            },
            moving: {
              base: 11.46,
              firing: 2.39,
            },
          },

          // Характеристики орудия
          gunTraverseRange: '-180 / 180',
          turretTraverseSpeed: 39.63,
          gunElevation: 20,
          gunDepression: 8,
        },
      },
    ],
    [TANK_TYPES.HEAVY]: [
      {
        name: 'AMX M4 mle. 54',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/amx-m4-mle-1949-ter.webp',
        characteristics: {},
      },
      {
        name: 'AMX 50 B',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/amx-50b.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.TD]: [
      {
        name: 'AMX 50 Foch (155)',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/amx-50-foch-155.webp',
        characteristics: {},
      },
      {
        name: 'AMX 50 Foch B',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/amx-50-foch-b.webp',
        characteristics: {},
      },
      {
        name: '120 AC «Gendarme»',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/schneider-120-ac-gendarme.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.SPG]: [
      {
        name: 'Bat.-Châtillon 155 58',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/bat-chatillon-155-58.webp',
        characteristics: {},
      },
    ],
  },
  UK: {
    [TANK_TYPES.LIGHT]: [
      {
        name: 'Manticore',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/manticore.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.MEDIUM]: [
      {
        name: 'Nemesis',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/nemesis.webp',
        characteristics: {},
      },
      {
        name: 'Centurion Action X',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/centurion-action-x.webp',
        characteristics: {},
      },
      {
        name: 'Concept No. 5',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/concept-no-5.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.HEAVY]: [
      {
        name: 'FV215b',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/fv215b.webp',
        characteristics: {},
      },
      {
        name: 'Super Conqueror',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/super-conqueror.webp',
        characteristics: {},
      },
      {
        name: 'T95/FV4201 Chieftain',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/t95-fv4201-chieftain.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.TD]: [
      {
        name: 'FV217 Badger',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/fv217-badger.webp',
        characteristics: {},
      },
      {
        name: 'FV215b (183)',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/fv215b-183.webp',
        characteristics: {},
      },
      {
        name: 'FV4005 Stage II',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/fv4005.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.SPG]: [
      {
        name: 'Conqueror Gun Carriage',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/conqueror-gc.webp',
        characteristics: {},
      },
    ],
  },
  Japan: {
    [TANK_TYPES.MEDIUM]: [
      {
        name: 'STB-1',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/stb-1.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.HEAVY]: [
      {
        name: 'Type 5 Heavy',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/type-5-heavy.webp',
        characteristics: {},
      },
      {
        name: 'Type 71',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/type-71.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.TD]: [
      {
        name: 'Ho-Ri 3',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/ho-ri-3.webp',
        characteristics: {},
      },
    ],
  },
  Czech: {
    [TANK_TYPES.MEDIUM]: [
      {
        name: 'TVP T 50/51',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/tvp-t-50-51.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.HEAVY]: [
      {
        name: 'Vz. 55',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/vz-55.webp',
        characteristics: {},
      },
      {
        name: 'Gothic Warrior',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/gothic-warrior.webp',
        characteristics: {},
      },
    ],
  },
  Sweden: {
    [TANK_TYPES.MEDIUM]: [
      {
        name: 'UDES 15/16',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/udes-15-16.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.HEAVY]: [
      {
        name: 'Kranvagn',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/kranvagn.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.TD]: [
      {
        name: 'Strv 103B',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/strv-103b.webp',
        characteristics: {},
      },
    ],
  },
  Poland: {
    [TANK_TYPES.MEDIUM]: [
      {
        name: 'CS-63',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/cs-63.webp',
        characteristics: {},
      },
      {
        name: 'CS-63 Wilk',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/cs-63-wilk.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.HEAVY]: [
      {
        name: 'Czołg Pancernik wz.46',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/czolg-p-wz-46.webp',
        characteristics: {},
      },
      {
        name: 'Czołг (P) wz.46 Wiedźmak',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/czolg-p-wz-46-verbesserter.webp',
        characteristics: {},
      },
      {
        name: '60TP Lewandowskiego',
        level: TANK_LEVEL,
        role: TANK_ROLES['Прорыва'],
        icon: '/src/assets/images/tanks/60tp-lewandowskiego.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.TD]: [
      {
        name: 'Wz.70 Żubr',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/nc-wz-70t.webp',
        characteristics: {},
      },
    ],
  },
  Italy: {
    [TANK_TYPES.MEDIUM]: [
      {
        name: 'Lion',
        level: TANK_LEVEL,
        role: TANK_ROLES['Снайперский'],
        icon: '/src/assets/images/tanks/lion.webp',
        characteristics: {},
      },
      {
        name: 'Progetto M40 mod. 65',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/progetto-m40-mod-65.webp',
        characteristics: {},
      },
      {
        name: 'Carro da Combattimento 45 t',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/carro-combattimento-45t.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.HEAVY]: [
      {
        name: 'Orso',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/orso.webp',
        characteristics: {},
      },
      {
        name: 'Rinoceronte',
        level: TANK_LEVEL,
        role: TANK_ROLES['Поддержки'],
        icon: '/src/assets/images/tanks/rinoceronte.webp',
        characteristics: {},
      },
    ],
    [TANK_TYPES.TD]: [
      {
        name: 'Controcarro 3 Minotauro',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/cc-3.webp',
        characteristics: {},
      },
    ],
  },
  International: {
    [TANK_TYPES.MEDIUM]: [
      {
        name: 'T-54D',
        level: TANK_LEVEL,
        role: TANK_ROLES['Штурмовой'],
        icon: '/src/assets/images/tanks/t54d.webp',
        characteristics: {},
      },
      {
        name: 'Merkava LP',
        level: TANK_LEVEL,
        role: TANK_ROLES['Универсальный'],
        icon: '/src/assets/images/tanks/merkava-lp.webp',
        characteristics: {
          reloadTime: 7.2,
          viewRange: 380,
          dispersion: 0.38,
          aimTime: 2.3,
          armor: {
            front: 120,
            side: 90,
            rear: 50,
            turret: {
              front: 260,
              side: 180,
              rear: 70,
            },
          },
          speed: {
            forward: 45,
            reverse: 18,
            hullRotation: 40,
            turretRotation: 40,
          },
          gun: {
            damage: {
              min: 350,
              max: 580,
              avg: 420,
            },
            penetration: {
              standard: 210,
              premium: 260,
              gold: 340,
            },
          },
          engine: {
            power: 600,
            fireChance: 0.1,
          },
          radio: {
            range: 800,
          },
        },
      },
    ],
  },
};

// Adds equipmentSlots based on tank level and class
for (const country in tanksData) {
  for (const type in tanksData[country]) {
    tanksData[country][type].forEach(tank => {
      // Танки БЕЗ цены (награды, недоступные)
      const exclusions = [
        'LKpz.70 K',
        'Lion',
        'Объект 780',
        'Nemesis',
        'ИС-7 «Драгун»',
        'Объект 260',
        'Projet Murat',
        'WT E 100 Оруженосец',
        'Czołg Pancernik wz.46',
        'Czołг (P) wz.46 Wiedźmak',
        'AMX 50 Foch (155)',
        'Об. 268/4 Морион',
        'Объект 279 ранний',
        'Erich Konzept I',
        'Kampfpanzer 07 P(E)',
        'Waffenträger auf E 100',
        'Grayhound',
        'H-3',
        'XM57',
        'BZ-74-1',
        'WZ-111 Qilin',
        'Царь обезьян',
        '120 AC «Gendarme»',
        'Orso',
        'DZT-159',
      ];

      // Танки с ценой 25000 бон
      if (
        [
          'Объект 907',
          'VK 72.01 (K)',
          'Carro da Combattimento 45 t',
          'T95/FV4201 Chieftain',
          '114 SP2',
          'CS-63 Wilk',
        ].includes(tank.name)
      ) {
        tank.cost = 25000;
        tank.currencyIcon = '/src/assets/images/role/boni.png';
      }
      // Танки с ценой 15000 бон
      else if (
        [
          'Т-22 ср.',
          'M60',
          'T95E6',
          '121B',
          '116-F3',
          'FV215b',
          'FV215b (183)',
          'Gothic Warrior',
          '113 Beijing Opera',
        ].includes(tank.name)
      ) {
        tank.cost = 15000;
        tank.currencyIcon = '/src/assets/images/role/boni.png';
      }
      // Добавляем цену 6100000, кроме исключений
      else if (!exclusions.includes(tank.name) && !tank.cost) {
        tank.cost = 6100000;
      }

      // Добавляем специальную иконку валюты для танков БЕЗ цены (награды)
      if (exclusions.includes(tank.name) && !tank.currencyIcon) {
        tank.currencyIcon = '/src/assets/images/role/boni.png';
      }

      const lvl = tank.level;
      let slotCount = 0;
      if (lvl <= 1) slotCount = 0;
      else if (lvl === 2) slotCount = 1;
      else if (lvl === 3) slotCount = 2;
      else if (lvl >= 4) slotCount = 3;
      const categoryMap = {
        [TANK_TYPES.LIGHT]: 'Reconnaissance',
        [TANK_TYPES.MEDIUM]: 'Mobility',
        [TANK_TYPES.HEAVY]: 'Survivability',
        [TANK_TYPES.TD]: 'Firepower',
        [TANK_TYPES.SPG]: 'Firepower',
      };
      const slots = [];
      for (let i = 1; i <= slotCount; i++) {
        let category = null;
        if (lvl >= 6 && i === 1) {
          category = categoryMap[type] || null;
        }
        slots.push({ id: i, category });
      }
      tank.equipmentSlots = slots;
    });
  }
}

// Очистка дублирующих icon в characteristics
for (const countryTanks of Object.values(tanksData)) {
  for (const tankList of Object.values(countryTanks)) {
    tankList.forEach(tank => {
      if (tank.characteristics?.icon) {
        delete tank.characteristics.icon;
      }
    });
  }
}



// Export the optimized data structure

// Константы для перков экипажа
export const CREW_PERKS = {
  COMMON: {
    'Боевое братство': {
      icon: 'brothers_in_arms.webp',
      description: 'Улучшает все характеристики экипажа',
      bonus: { crewBonus: 0.05 },
    },
    Ремонт: {
      icon: 'repair.webp',
      description: 'Ускоряет ремонт повреждённых модулей',
      bonus: { repairSpeed: 0.1 },
    },
    'Пожарная подготовка': {
      icon: 'fire_fighting.webp',
      description: 'Снижает вероятность возгорания',
      bonus: { fireChance: -0.25 },
    },
    Камуфляж: {
      icon: 'camouflage.webp',
      description: 'Улучшает маскировку танка',
      bonus: { camouflage: 0.03 },
    },
  },

  COMMANDER: {
    'Орлиный глаз': {
      icon: 'eagle_eye.webp',
      description: 'Увеличивает дальность обзора',
      bonus: { viewRange: 0.02 },
    },
    'Мастер на все руки': {
      icon: 'jack_of_all_trades.webp',
      description: 'Снижает штраф к характеристикам при выбытии члена экипажа',
      bonus: { crewLossPenalty: -0.5 },
    },
    Эксперт: {
      icon: 'expert.webp',
      description: 'Улучшает скорость обнаружения вражеских танков',
      bonus: { detectionTime: -0.1 },
    },
  },

  DRIVER: {
    'Король бездорожья': {
      icon: 'off_road_king.webp',
      description: 'Улучшает проходимость по мягкому грунту',
      bonus: { softTerrainResistance: -0.1 },
    },
    'Плавный ход': {
      icon: 'smooth_ride.webp',
      description: 'Снижает разброс при движении',
      bonus: { dispersionOnMove: -0.04 },
    },
    Виртуоз: {
      icon: 'virtuoso.webp',
      description: 'Улучшает маневренность танка',
      bonus: { traverseSpeed: 0.05 },
    },
    'Чистота и порядок': {
      icon: 'clean_driving.webp',
      description: 'Снижает вероятность возгорания двигателя',
      bonus: { engineFireChance: -0.25 },
    },
  },

  GUNNER: {
    Снайпер: {
      icon: 'sniper.webp',
      description: 'Увеличивает шанс нанести критический урон',
      bonus: { criticalHitChance: 0.03 },
    },
    'Плавный поворот башни': {
      icon: 'smooth_turret.webp',
      description: 'Снижает разброс при повороте башни',
      bonus: { dispersionOnTurretTraverse: -0.07 },
    },
    'Мастер-оружейник': {
      icon: 'armorer.webp',
      description: 'Снижает штраф к характеристикам орудия при повреждении',
      bonus: { damagedGunPenalty: -0.25 },
    },
  },

  LOADER: {
    Отчаянный: {
      icon: 'desperate.webp',
      description: 'Ускоряет перезарядку при малом запасе прочности',
      bonus: { reloadSpeed: -0.09 },
    },
    Интуиция: {
      icon: 'intuition.webp',
      description: 'Даёт шанс мгновенно сменить тип снаряда',
      bonus: { ammoSwitchChance: 0.17 },
    },
    'Бесконтактная боеукладка': {
      icon: 'safe_stowage.webp',
      description: 'Снижает вероятность взрыва боеукладки',
      bonus: { ammoRackExplosionChance: -0.25 },
    },
  },

  RADIO_OPERATOR: {
    Радиоперехват: {
      icon: 'signal_boosting.webp',
      description: 'Увеличивает дальность связи',
      bonus: { radioRange: 0.2 },
    },
    Изобретатель: {
      icon: 'inventor.webp',
      description: 'Увеличивает дальность связи и обзора',
      bonus: { radioRange: 0.05, viewRange: 0.03 },
    },
    'Последний резерв': {
      icon: 'last_resort.webp',
      description: 'Улучшает характеристики при потере радиста',
      bonus: { radioOperatorLossPenalty: -0.5 },
    },
  },
};

// Import equipment data
import {
  STANDARD_EQUIPMENT,
  SPECIAL_EQUIPMENT,
  IMPROVED_EQUIPMENT,
  EXPERIMENTAL_EQUIPMENT,
} from './equipment.js';

// Объединённые данные по всем типам оборудования
export const EQUIPMENT_DATA = {
  ...STANDARD_EQUIPMENT,
  ...SPECIAL_EQUIPMENT,
  ...IMPROVED_EQUIPMENT,
  ...EXPERIMENTAL_EQUIPMENT,
};

// ОПТИМИЗИРОВАННЫЕ ИНДЕКСЫ ДЛЯ БЫСТРОГО ДОСТУПА
let optimizedTanksData = null;
let tanksByName = null;
let tanksByCountry = null;
let tanksByType = null;

/**
 * СОЗДАНИЕ ОПТИМИЗИРОВАННЫХ ИНДЕКСОВ
 */
function createOptimizedIndexes() {
  if (optimizedTanksData) return optimizedTanksData;

  const allTanks = [];
  const nameIndex = new Map();
  const countryIndex = new Map();
  const typeIndex = new Map();

  // Преобразуем иерархическую структуру в плоский массив с индексами
  Object.entries(tanksData).forEach(([country, types]) => {
    Object.entries(types).forEach(([type, tanks]) => {
      tanks.forEach(tank => {
        // Автоматически определяем уровень танка если не указан
        const tierInfo = tank.tier ? { level: tank.level, tier: tank.tier } : getTankTier(tank.name);

        const optimizedTank = {
          ...tank,
          country,
          type,
          level: tierInfo.level,
          tier: tierInfo.tier,
          id: `${country}_${type}_${tank.name}`.replace(/\s+/g, '_'),
          searchKey: `${tank.name} ${country} ${type} ${tierInfo.tier}`.toLowerCase()
        };

        allTanks.push(optimizedTank);

        // Индекс по имени
        nameIndex.set(tank.name.toLowerCase(), optimizedTank);

        // Индекс по стране
        if (!countryIndex.has(country)) {
          countryIndex.set(country, []);
        }
        countryIndex.get(country).push(optimizedTank);

        // Индекс по типу
        if (!typeIndex.has(type)) {
          typeIndex.set(type, []);
        }
        typeIndex.get(type).push(optimizedTank);
      });
    });
  });

  optimizedTanksData = {
    allTanks,
    nameIndex,
    countryIndex,
    typeIndex,
    totalCount: allTanks.length
  };

  tanksByName = nameIndex;
  tanksByCountry = countryIndex;
  tanksByType = typeIndex;

  console.log(`📊 Optimized tank data created: ${allTanks.length} tanks indexed`);
  return optimizedTanksData;
}

/**
 * БЫСТРЫЙ ПОИСК ТАНКА ПО ИМЕНИ
 */
export function getTankByName(name) {
  if (!tanksByName) createOptimizedIndexes();
  return tanksByName.get(name.toLowerCase());
}

/**
 * БЫСТРЫЙ ПОИСК ТАНКОВ ПО СТРАНЕ
 */
export function getTanksByCountry(country) {
  if (!tanksByCountry) createOptimizedIndexes();
  return tanksByCountry.get(country) || [];
}

/**
 * БЫСТРЫЙ ПОИСК ТАНКОВ ПО ТИПУ
 */
export function getTanksByType(type) {
  if (!tanksByType) createOptimizedIndexes();
  return tanksByType.get(type) || [];
}

/**
 * ПОЛУЧЕНИЕ ВСЕХ ТАНКОВ В ОПТИМИЗИРОВАННОМ ФОРМАТЕ
 */
export function getAllOptimizedTanks() {
  const data = createOptimizedIndexes();
  return data.allTanks;
}

/**
 * ПОЛУЧЕНИЕ СТАТИСТИКИ ДАННЫХ
 */
export function getTankDataStats() {
  const data = createOptimizedIndexes();
  return {
    totalTanks: data.totalCount,
    countriesCount: data.countryIndex.size,
    typesCount: data.typeIndex.size,
    indexesCreated: !!optimizedTanksData
  };
}

// Export the tanks data
export { tanksData };
