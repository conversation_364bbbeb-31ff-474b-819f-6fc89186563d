.sidebar-logo-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  margin-right: 10px;
  display: block;
  object-fit: contain;
  transform-origin: 50% 50%;
  transition: none;
  /* Добавляем плавное вращение */
  animation: smoothRotate 8s linear infinite;
}

.sidebar-logo-icon path:nth-child(1) {
  stroke: #ff2db3;
  animation: iconColorFlow1 4s ease-in-out infinite;
}
.sidebar-logo-icon path:nth-child(2) {
  stroke: #ffa020;
  animation: iconColorFlow2 4s ease-in-out infinite;
}
.sidebar-logo-icon path:nth-child(3) {
  stroke: #1eb83a;
  animation: iconColorFlow3 4s ease-in-out infinite;
}
.sidebar-logo-icon path:nth-child(4) {
  stroke: #0a7fff;
  animation: iconColorFlow4 4s ease-in-out infinite;
}

.sidebar-logo-icon.super-fast-spin {
  transform-origin: 50% 50%;
}

.sidebar-logo-icon.super-fast-spin path:nth-child(1) {
  stroke: #ff2db3;
}
.sidebar-logo-icon.super-fast-spin path:nth-child(2) {
  stroke: #ffa020;
}
.sidebar-logo-icon.super-fast-spin path:nth-child(3) {
  stroke: #1eb83a;
}
.sidebar-logo-icon.super-fast-spin path:nth-child(4) {
  stroke: #0a7fff;
}

@keyframes textPulse {
  0%, 100% {
    text-shadow: 0 0 6px rgba(255, 255, 255, 0.9), 0 0 16px rgba(172, 142, 255, 0.65);
  }
  50% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 1), 0 0 24px rgba(172, 142, 255, 0.9);
  }
}

@keyframes aiTextGlow {
  0%, 100% {
    filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 20px rgba(255, 255, 255, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 16px rgba(255, 255, 255, 1)) drop-shadow(0 0 28px rgba(255, 255, 255, 0.8));
  }
}

/* Анимации переливающихся шариков-волн */
/* ГОРИЗОНТАЛЬНЫЙ ШАРИК: лево→право (розовый шарик) */
/* Path 1 (ЛЕВАЯ): шарик стартует здесь */
@keyframes iconColorFlow1 {
  0% {
    stroke: #ff2db3;
  }
  25% {
    stroke: #ff2db3;
  }
  50% {
    stroke: #1eb83a;
  }
  75% {
    stroke: #1eb83a;
  }
  100% {
    stroke: #ff2db3;
  }
}

/* Path 3 (ПРАВАЯ): шарик приходит сюда с задержкой */
@keyframes iconColorFlow3 {
  0% {
    stroke: #1eb83a;
  }
  12.5% {
    stroke: #1eb83a;
  }
  37.5% {
    stroke: #ff2db3;
  }
  62.5% {
    stroke: #ff2db3;
  }
  87.5% {
    stroke: #1eb83a;
  }
  100% {
    stroke: #1eb83a;
  }
}

/* ВЕРТИКАЛЬНЫЙ ШАРИК: верх→низ (желтый шарик) */
/* Path 2 (ВЕРХНЯЯ): шарик стартует здесь */
@keyframes iconColorFlow2 {
  0% {
    stroke: #ffa020;
  }
  25% {
    stroke: #ffa020;
  }
  50% {
    stroke: #0a7fff;
  }
  75% {
    stroke: #0a7fff;
  }
  100% {
    stroke: #ffa020;
  }
}

/* Path 4 (НИЖНЯЯ): шарик приходит сюда с задержкой */
@keyframes iconColorFlow4 {
  0% {
    stroke: #0a7fff;
  }
  12.5% {
    stroke: #0a7fff;
  }
  37.5% {
    stroke: #ffa020;
  }
  62.5% {
    stroke: #ffa020;
  }
  87.5% {
    stroke: #0a7fff;
  }
  100% {
    stroke: #0a7fff;
  }
}

/* RGB анимация цвета линии - плавная смена цветов */
@keyframes rgbLineColor {
  0% {
    border-bottom-color: rgba(255, 0, 100, 0.9);
  }
  16.66% {
    border-bottom-color: rgba(255, 100, 0, 0.9);
  }
  33.33% {
    border-bottom-color: rgba(255, 200, 0, 0.9);
  }
  50% {
    border-bottom-color: rgba(100, 255, 0, 0.9);
  }
  66.66% {
    border-bottom-color: rgba(0, 200, 255, 0.9);
  }
  83.33% {
    border-bottom-color: rgba(150, 0, 255, 0.9);
  }
  100% {
    border-bottom-color: rgba(255, 0, 100, 0.9);
  }
}

/* Empty .sidebar-header-content ruleset removed to fix lint error */

div.sidebar .sidebar-header-content {
  /* Increased specificity for header container */
  display: flex;
  flex-direction: row; /* Force horizontal layout */
  align-items: center;
  justify-content: center; /* Perfect center alignment */
  padding: 1.25rem 1rem; /* Equal padding on all sides */
  border-bottom: 2px solid rgba(255, 0, 100, 0.9); /* RGB линия */
  animation: rgbLineColor 4s linear infinite;
  margin-bottom: 5px; /* Minimal space before ANALYSIS */
  height: 60px; /* ФИКСИРОВАННАЯ ВЫСОТА - НЕ min-height */
  gap: 0; /* Remove any gap between items */
  width: 100%; /* Full width for proper centering */
  position: relative; /* Для псевдоэлементов */
  overflow: visible; /* Показываем тень */
  /* ПРЕДОТВРАЩАЕМ ДЕРГАНИЕ */
  transform: translateZ(0);
  backface-visibility: hidden;
  /* Removed cursor: pointer and hover effects */
}

/* Тень под RGB линией - синхронизированная с цветом линии */
div.sidebar .sidebar-header-content::before {
  content: '';
  position: absolute;
  bottom: -3px; /* Ниже линии */
  left: 0;
  width: 100%;
  height: 2px;
  background: rgba(255, 0, 100, 0.4);
  animation: rgbShadowColor 4s ease-in-out infinite;
  filter: blur(1px);
  z-index: -1;
}

/* Анимация цвета тени - точно синхронизирована с линией */
@keyframes rgbShadowColor {
  0% {
    background: rgba(255, 0, 100, 0.4);
  }
  16.66% {
    background: rgba(255, 100, 0, 0.4);
  }
  33.33% {
    background: rgba(255, 200, 0, 0.4);
  }
  50% {
    background: rgba(100, 255, 0, 0.4);
  }
  66.66% {
    background: rgba(0, 200, 255, 0.4);
  }
  83.33% {
    background: rgba(150, 0, 255, 0.4);
  }
  100% {
    background: rgba(255, 0, 100, 0.4);
  }
}

/* Старые анимации удалены */

@keyframes neonShimmer {
  0% {
    left: -100%;
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  50% {
    left: 0%;
    opacity: 1;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

.ai-title-text {
  font-weight: 600; /* Ensure semibold is applied */
  /* УБИРАЕМ АНИМАЦИЮ ПОДСВЕТКИ */
  font-size: 0.8rem; /* Немного больше чем ANALYSIS (0.7rem) */
  background: linear-gradient(135deg, #ffffff, #f8fafc, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.5px;
  line-height: 1.2;
  display: flex;
  align-items: center;
  white-space: nowrap; /* Prevent text wrapping */
  /* УБИРАЕМ ЛЮБУЮ ПОДСВЕТКУ */
  border: none;
  box-shadow: none;
  outline: none;
  text-shadow: none;
  background-color: transparent;
  /* Removed cursor: pointer and hover effects */
}

/* УБИРАЕМ HOVER ЭФФЕКТЫ БЕЗ !important */
.ai-title-text:hover,
.ai-title-text:focus,
.ai-title-text:active,
.sidebar-header-content:hover .ai-title-text {
  /* УБИРАЕМ ВСЕ ЭФФЕКТЫ ТОЛЬКО ДЛЯ ТЕКСТА */
  border: none;
  box-shadow: none;
  outline: none;
  text-shadow: none;
  background-color: transparent;
  filter: none;
  animation: none;
  transition: none;
}

/* УБИРАЕМ КУРСОР POINTER ДЛЯ ВСЕГО HEADER КРОМЕ ИКОНКИ */
.sidebar-header-content,
.ai-title-text {
  cursor: default;
  pointer-events: none;
}

/* НО ИКОНКА ДОЛЖНА РЕАГИРОВАТЬ НА МЫШЬ */
.sidebar-logo-icon {
  cursor: default;
  pointer-events: auto;
}

/* Responsive behavior - ПОКАЗЫВАЕМ AI Efficiency на всех экранах */
@media (max-width: 767px) {
  div.sidebar .sidebar-header-content {
    justify-content: center; /* Center icon + text on mobile */
    padding: 1rem 0.75rem; /* Немного меньше отступы на мобильных */
  }

  .ai-title-text {
    display: flex; /* Показываем текст на мобильных */
    font-size: 0.9rem; /* Немного меньше размер на мобильных */
  }
}

@media (min-width: 768px) {
  div.sidebar .sidebar-header-content {
    justify-content: center; /* Center icon + text on desktop */
  }

  .ai-title-text {
    display: flex; /* Show text on desktop */
    font-size: 1rem; /* Полный размер на десктопе */
  }
}

.section-title {
  padding: 0rem 1.5rem 0.5rem 1.5rem; /* No top padding, only bottom */
  margin-bottom: 0rem;
  font-size: 0.7rem; /* Approx 11px */
  font-weight: 600; /* Semibold */
  text-transform: uppercase;
  color: rgba(226, 230, 240, 0.5); /* Muted text color for 'ANALYSIS' */
  opacity: 0.6;
  letter-spacing: 1px;
  border-bottom: none;
}

/* Переменные сайдбара перенесены в base/variables.css для избежания дублирования */

/* Дублирующиеся стили body удалены - используются из main.css */

/* Дублирующиеся стили app-container удалены - используются из main.css */

body .sidebar {
  width: 250px;
  min-width: 250px;
  max-width: 250px;
  height: 100vh;
  color: var(--sidebar-text);
  position: fixed;
  top: 0;
  left: 0;
  padding: 0;
  box-sizing: border-box;
  overflow-x: hidden; /* Скрываем только горизонтальный скролл */
  overflow-y: auto; /* Разрешаем вертикальный скролл */
  z-index: 10;
  /* СТАБИЛИЗАЦИЯ ПРИ МАСШТАБИРОВАНИИ */
  will-change: auto;
  transform: none;
  transition: none;
  /* ОРИГИНАЛЬНЫЙ ФОН САЙДБАРА КАК В Best24 */
  background: linear-gradient(180deg, rgba(45, 35, 75, 0.75) 0%, rgba(35, 28, 60, 0.7) 100%);
  backdrop-filter: blur(20px) saturate(200%);
  -webkit-backdrop-filter: blur(20px) saturate(200%);
  border-right: 1.5px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  padding: 20px 24px 32px 24px; /* Максимальный отступ снизу */
  margin-bottom: 5px;
  border-bottom: var(--sidebar-border);
  background: transparent;
  min-height: 90px; /* Максимальная высота */
  height: auto; /* Автоматическая высота */
  overflow: visible; /* Контент не обрезается */
  /* Специальные стили для масштабирования */
  box-sizing: border-box;
  position: relative;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background-color: var(--unity-purple-light);
  border-radius: 8px;
  margin-right: 12px;
  position: relative;
}

.logo-icon:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50%;
  height: 50%;
  background-color: var(--sidebar-bg);
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}

.logo-icon-brain {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

.logo-icon-brain svg {
  width: 100%;
  height: 100%;
}

.logo-text {
  font-size: 1.2rem;
  font-weight: 600;
  margin-left: 16px; /* Увеличиваем отступ между иконкой и текстом */
  /* Используем белый цвет как у активных вкладок */
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0px 0px 5px rgba(255, 255, 255, 0.2);
  filter: contrast(1.1) brightness(1.05);
  letter-spacing: 0.5px;
  /* РАДИКАЛЬНОЕ решение для буквы "y" */
  padding: 4px 0 8px 0; /* Отступы сверху и снизу */
  line-height: 1.6; /* Еще больше высота строки */
  display: inline-block; /* Inline-block для лучшего контроля */
  vertical-align: middle; /* Выравнивание по середине */
  position: relative; /* Для точного позиционирования */
  top: -2px; /* Слегка поднимаем текст */
}

/* Hover эффект для AI Efficiency логотипа */
.sidebar-logo:hover .logo-text {
  color: #f8fafc; /* Слегка светлее при hover */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0px 0px 8px rgba(255, 255, 255, 0.3);
  filter: contrast(1.2) brightness(1.1);
  transition: all 0.2s ease;
}

/* Специальные стили для масштабирования - предотвращаем обрезание буквы "y" */
@media (min-resolution: 1.25dppx) {
  .logo-text {
    padding-bottom: 10px;
    line-height: 1.7;
  }
  .sidebar-logo {
    min-height: 95px;
    padding-bottom: 36px;
  }
}

@media (min-resolution: 1.5dppx) {
  .logo-text {
    padding-bottom: 12px;
    line-height: 1.8;
  }
  .sidebar-logo {
    min-height: 100px;
    padding-bottom: 40px;
  }
}

.sidebar-section {
  margin-bottom: 24px;
  padding: 0 16px;
}

.flag-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0 1.5rem 0.5rem;
}

.flag-list img {
  width: 35px;
  height: 27px;
  object-fit: contain;
  border-radius: 2px;
}

/* Divider line between flag and type filter sections */
.flag-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(139, 92, 246, 0.3) 5%,
    rgba(139, 92, 246, 0.6) 50%,
    rgba(139, 92, 246, 0.3) 95%,
    transparent 100%
  );
  margin: 0.5rem 0.2rem;
}

/* Removed duplicate .section-title - using the one at line 465 instead */

.sidebar-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  /* НЕТ transition - мгновенные изменения */
  background: transparent;
  border: 1px solid transparent;
  position: relative;
  color: var(--sidebar-text);
  font-weight: 500;
  box-shadow: none;
  outline: none;
  text-shadow: none;
}

.sidebar-menu-item:hover {
  background: rgba(114, 84, 228, 0.15);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Active menu item styles */
/* Active menu item styles */
.sidebar-menu-item.active {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
  border-radius: 0.5rem;
  border: 1px solid var(--unity-purple-light); /* #a17bf8 */
  box-shadow: none;
}

.sidebar-menu-item .menu-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  color: var(--sidebar-icon-color, #a17bf8); /* Default icon color (light purple) */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  /* УБИРАЕМ TRANSITION - МГНОВЕННЫЕ ИЗМЕНЕНИЯ */
}

.sidebar-menu-item .menu-icon svg {
  width: 100%; /* Use 100% to fill the 20x20 container */
  height: 100%;
  stroke-width: 1.5; /* Consistent stroke width */
  stroke: #a17bf8;
  fill: none;
}

.sidebar-menu-item .menu-text {
  font-size: clamp(0.9rem, 1.4vw, 1rem); /* Увеличенный размер как у названий танков */
  line-height: 1.2;
  background: linear-gradient(135deg, #a17bf8, #8b5cf6, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600; /* Semibold weight for non-active text */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  filter: contrast(1.1) brightness(1.05);
  /* УБИРАЕМ TRANSITION - МГНОВЕННЫЕ ИЗМЕНЕНИЯ */
}

.sidebar-menu-item.active .menu-text {
  opacity: 1;
  font-weight: 600;
  color: #ffffff;
}

.sidebar-menu-item.active .menu-icon svg {
  stroke: #ffffff;
  fill: none;
}

.sidebar-menu-item.active .menu-text {
  background: linear-gradient(135deg, #ffffff, #f8fafc, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700; /* Более жирный шрифт для активной вкладки */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
}

/* Hover эффекты для главных вкладок меню - только для неактивных */
.sidebar-menu-item:hover:not(.active) .menu-text {
  background: linear-gradient(135deg, #ffffff, #f1f5f9, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
  font-weight: 600; /* Оставляем тот же вес что и по умолчанию */
}

/* УБИРАЕМ HOVER ЭФФЕКТЫ ДЛЯ АКТИВНЫХ ВКЛАДОК */
.sidebar-menu-item.active:hover .menu-text {
  /* Сохраняем стили активной вкладки без изменений */
  background: linear-gradient(135deg, #ffffff, #f1f5f9, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700; /* Сохраняем жирность активной вкладки */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
}

/* Глобальные стили для кнопок фильтра - убираем outline */
.filter-item,
.flag-item,
.type-item {
  outline: none;
  border: 1px solid transparent;
}

.filter-item:focus,
.flag-item:focus,
.type-item:focus,
.filter-item:focus-visible,
.flag-item:focus-visible,
.type-item:focus-visible {
  outline: none;
  border-color: transparent;
}

/* Классы deselecting больше не нужны - используем мгновенные изменения */

/* Flag item button styles */
.flag-item {
  display: flex;
  align-items: center;
  gap: 6px; /* tighter gap */
  padding: 2px 8px; /* reduce vertical padding to offset larger icon */
  width: 100%;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px; /* Match main menu items */
  cursor: pointer;
  color: var(--sidebar-text);
  outline: none;
  /* ПОЛНОСТЬЮ УБИРАЕМ ВСЕ TRANSITION */
  transition: none;
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
}

.flag-item:hover:not(.selected):not(.active),
.flag-item:focus:not(.selected):not(.active),
.flag-item:focus-visible:not(.selected):not(.active) {
  background: rgba(114, 84, 228, 0.15);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  outline: none;
  border-color: transparent;
}

/* ПРИНУДИТЕЛЬНО УБИРАЕМ ФОН У SELECTED/ACTIVE */
.flag-item.selected,
.flag-item.active {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
  border: 1px solid var(--unity-purple-light);
  border-radius: 0.5rem;
  color: #ffffff;
}

.flag-item.selected:hover,
.flag-item.active:hover,
.flag-item.selected:focus,
.flag-item.active:focus,
.flag-item.selected:active,
.flag-item.active:active {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
  border: 1px solid var(--unity-purple-light);
  border-radius: 0.5rem;
  color: #ffffff;
}

/* ПРИНУДИТЕЛЬНО УБИРАЕМ ЛЮБЫЕ HOVER ЭФФЕКТЫ У АКТИВНЫХ */
.flag-item.selected:hover,
.flag-item.active:hover {
  box-shadow: none;
}

/* ПРИНУДИТЕЛЬНО УБИРАЕМ FOCUS СОСТОЯНИЯ */
.flag-item:focus,
body .sidebar .flag-item:focus-visible,
body .sidebar .flag-item:focus-within {
  outline: none;
  box-shadow: none;
  background: transparent;
}

body .sidebar .flag-item.selected:focus,
body .sidebar .flag-item.active:focus {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
}

/* ГЛОБАЛЬНОЕ ОТКЛЮЧЕНИЕ TRANSITION ДЛЯ ФИЛЬТРОВ */
.flag-item,
.flag-item *,
.flag-item:hover,
body .sidebar .flag-item:focus,
body .sidebar .flag-item:active {
  transition: none;
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
}

/* Removed hover effects for icons to maintain quality */

/* УБИРАЕМ ДУБЛИРУЮЩИЕ СТИЛИ - используем стили выше */

.flag-item.selected .flag-name,
.flag-item.active .flag-name,
.flag-item:active .flag-name {
  /* Added :active for text */
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
  font-weight: 700;
  /* НЕТ transition - мгновенное изменение */
}

/* Hover эффект для названий стран */
.flag-item:hover:not(.selected) .flag-name {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
  font-weight: 600; /* Оставляем тот же вес что и по умолчанию */
}

.flag-item img {
  flex-shrink: 0;
  width: 38px; /* Default size for large screens */
  height: 30px;
  object-fit: contain;
  border-radius: 2px;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

.flag-name {
  font-size: clamp(0.8rem, 1.4vw, 0.9rem);
  font-weight: 600;
  white-space: nowrap;
  color: var(--sidebar-icon-color, #a17bf8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  filter: contrast(1.1) brightness(1.05);
  /* УБИРАЕМ TRANSITION - МГНОВЕННЫЕ ИЗМЕНЕНИЯ */
  transition: none;
}

/* Tank type list styles */
.type-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.5rem 1.5rem 0.75rem;
}

/* Type item button styles (similar to flag-item) */
.type-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 2px 8px;
  width: 100%;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px; /* Match main menu items */
  cursor: pointer;
  color: var(--sidebar-text);
  outline: none;
  /* НЕТ transition - как у главных кнопок меню */
}

.type-item:hover:not(.selected):not(.active),
.type-item:focus:not(.selected):not(.active),
.type-item:focus-visible:not(.selected):not(.active) {
  background: rgba(114, 84, 228, 0.15);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  outline: none;
  border-color: transparent;
}

/* ПРИНУДИТЕЛЬНО УБИРАЕМ ФОН У SELECTED/ACTIVE */
.type-item.selected,
.type-item.active {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
  border: 1px solid var(--unity-purple-light);
  border-radius: 0.5rem;
  color: #ffffff;
}

.type-item.selected:hover,
.type-item.active:hover,
.type-item.selected:focus,
.type-item.active:focus,
.type-item.selected:active,
.type-item.active:active {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
  border: 1px solid var(--unity-purple-light);
  border-radius: 0.5rem;
  color: #ffffff;
}

/* ПРИНУДИТЕЛЬНО УБИРАЕМ ЛЮБЫЕ HOVER ЭФФЕКТЫ У АКТИВНЫХ */
.type-item.selected:hover,
.type-item.active:hover {
  box-shadow: none;
}

/* ПРИНУДИТЕЛЬНО УБИРАЕМ FOCUS СОСТОЯНИЯ */
.type-item:focus,
body .sidebar .type-item:focus-visible,
body .sidebar .type-item:focus-within {
  outline: none;
  box-shadow: none;
  background: transparent;
}

body .sidebar .type-item.selected:focus,
body .sidebar .type-item.active:focus {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
}

/* ГЛОБАЛЬНОЕ ОТКЛЮЧЕНИЕ TRANSITION ДЛЯ ФИЛЬТРОВ */
.type-item,
.type-item *,
.type-item:hover,
body .sidebar .type-item:focus,
body .sidebar .type-item:active {
  transition: none;
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
}

/* УБИРАЕМ ДУБЛИРУЮЩИЕ СТИЛИ - используем стили выше */

.type-item.selected .type-name,
.type-item.active .type-name,
.type-item:active .type-name {
  /* Added :active for text */
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
  font-weight: 700;
  /* НЕТ transition - мгновенное изменение */
}

/* Hover эффект для названий типов техники */
.type-item:hover:not(.selected) .type-name {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
  font-weight: 600; /* Оставляем тот же вес что и по умолчанию */
}

.type-item img {
  flex-shrink: 0;
  width: 38px; /* Default size for large screens */
  height: 30px;
  object-fit: contain;
  border-radius: 2px;
  image-rendering: auto; /* Smooth, high-quality rendering */
}

.type-name {
  font-size: clamp(0.8rem, 1.4vw, 0.9rem);
  font-weight: 600;
  white-space: nowrap;
  color: var(--sidebar-icon-color, #a17bf8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  filter: contrast(1.1) brightness(1.05);
  /* УБИРАЕМ TRANSITION - МГНОВЕННЫЕ ИЗМЕНЕНИЯ */
  transition: none;
}

/* Animation for country/type filter container */
body .sidebar .flag-section {
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  transform: translateY(-8px);
  transition:
    max-height 0.15s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.1s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  display: block;
  visibility: hidden;
}

body .sidebar .flag-section.open {
  max-height: 1000px; /* Large enough for all content */
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
  visibility: visible;
}

/* HD Monitor optimizations (1920x1080 and below) */
@media (max-height: 1080px) {
  /* Compact spacing for HD screens */
  .sidebar-section {
    margin-bottom: 12px; /* Reduced spacing */
  }

  .flag-list {
    gap: 0.15rem; /* Tighter gaps */
    padding: 0 1.2rem 0.3rem;
  }

  .type-list {
    gap: 0.15rem; /* Tighter gaps */
    padding: 0.2rem 1.2rem 0.4rem;
  }

  .flag-item,
  .type-item {
    padding: 1px 6px; /* Compact padding */
  }

  .flag-item img,
  .type-item img {
    width: 34px; /* Optimized size for HD screens */
    height: 26px;
  }

  .flag-name,
  .type-name {
    font-size: clamp(0.75rem, 1.6vw, 0.85rem); /* Увеличенный размер */
  }
}

/* Medium screens (900px height and below) */
@media (max-height: 900px) {
  .sidebar-section {
    margin-bottom: 8px; /* Even more compact */
  }

  .flag-item,
  .type-item {
    padding: 0px 4px; /* Very compact */
  }

  .flag-item img,
  .type-item img {
    width: 30px; /* Optimized for medium screens */
    height: 23px;
  }

  .flag-name,
  .type-name {
    font-size: clamp(0.7rem, 1.8vw, 0.8rem); /* Увеличенный размер */
  }
}

/* Small screens (768px height and below) */
@media (max-height: 768px) {
  .sidebar-menu-item {
    padding: 6px 12px; /* Compact menu items */
    margin-bottom: 2px;
  }

  .sidebar-section {
    margin-bottom: 6px; /* Minimal spacing */
  }

  .flag-list,
  .type-list {
    gap: 0.1rem; /* Minimal gaps */
    padding: 0.1rem 1rem 0.2rem;
  }

  .flag-item,
  .type-item {
    padding: 0px 3px; /* Minimal padding */
  }

  .flag-item img,
  .type-item img {
    width: 26px; /* Small but clear icons */
    height: 20px;
  }

  .flag-name,
  .type-name {
    font-size: clamp(0.65rem, 2vw, 0.75rem); /* Увеличенный размер */
  }
}

.sidebar-menu-item:not(.active):focus,
.sidebar-menu-item:not(.active):active,
.sidebar-menu-item:not(.active):focus-visible {
  outline: none;
  outline-style: none;
  outline-offset: 0;
  outline-width: 0;
  box-shadow: none;
  --tw-ring-shadow: none;
  --tw-ring-offset-shadow: none;
  --tw-ring-color: transparent;
  --tw-ring-offset-width: 0;
}

/* Дублирующие стили цветных линий удалены - используются стили выше */

.sidebar-section {
  margin-bottom: 24px;
  padding: 0 16px;
}

/* Removed duplicate .section-title - using the one at line 122 instead */

/* Removed duplicate .sidebar-menu-item styles - using the ones at line 280 instead */

/* АНИМАЦИЯ ПЕРЕЛИВОВ ЦВЕТОВ ДЛЯ ТЕКСТА */
@keyframes textGradientFlow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}
