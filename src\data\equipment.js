// Данные оборудования для World of Tanks

// СТАНДАРТНОЕ ОБОРУДОВАНИЕ
export const STANDARD_EQUIPMENT = {
  // Орудийные системы
  'enhanced-gun-laying-drive': {
    name: 'Улучшенные приводы наводки',
    type: 'standard',
    category: 'firepower',
    description: 'Уменьшает время сведения орудия на 10%',
    bonuses: {
      aimTime: -0.1
    },
    cost: 600000,
    icon: '/src/assets/images/equipment/enhanced-gun-laying-drive.webp'
  },
  
  'gun-rammer': {
    name: 'Досылатель',
    type: 'standard', 
    category: 'firepower',
    description: 'Уменьшает время перезарядки на 10%',
    bonuses: {
      reloadTime: -0.1
    },
    cost: 500000,
    icon: '/src/assets/images/equipment/gun-rammer.webp'
  },

  'vertical-stabilizer': {
    name: 'Стабилизатор вертикальной наводки',
    type: 'standard',
    category: 'firepower', 
    description: 'Уменьшает разброс при движении и повороте башни на 20%',
    bonuses: {
      dispersion: -0.2
    },
    cost: 600000,
    icon: '/src/assets/images/equipment/vertical-stabilizer.webp'
  },

  // Защита
  'spall-liner': {
    name: 'Противоосколочный подбой',
    type: 'standard',
    category: 'survivability',
    description: 'Уменьшает урон от фугасов и таранов на 15%',
    bonuses: {
      heProtection: 0.15
    },
    cost: 500000,
    icon: '/src/assets/images/equipment/spall-liner.webp'
  },

  'improved-ventilation': {
    name: 'Улучшенная вентиляция',
    type: 'standard',
    category: 'crew',
    description: 'Улучшает все навыки экипажа на 5%',
    bonuses: {
      crewSkills: 0.05
    },
    cost: 600000,
    icon: '/src/assets/images/equipment/improved-ventilation.webp'
  },

  // Мобильность
  'grousers': {
    name: 'Дополнительные грунтозацепы',
    type: 'standard',
    category: 'mobility',
    description: 'Улучшает проходимость по мягким грунтам на 15%',
    bonuses: {
      terrainResistance: -0.15
    },
    cost: 500000,
    icon: '/src/assets/images/equipment/grousers.webp'
  },

  'improved-rotation-mechanism': {
    name: 'Улучшенный поворотный механизм',
    type: 'standard',
    category: 'mobility',
    description: 'Увеличивает скорость поворота башни на 10%',
    bonuses: {
      turretTraverse: 0.1
    },
    cost: 600000,
    icon: '/src/assets/images/equipment/improved-rotation-mechanism.webp'
  },

  // Разведка
  'coated-optics': {
    name: 'Просветленная оптика',
    type: 'standard',
    category: 'reconnaissance',
    description: 'Увеличивает дальность обзора на 10%',
    bonuses: {
      viewRange: 0.1
    },
    cost: 500000,
    icon: '/src/assets/images/equipment/coated-optics.webp'
  },

  'binocular-telescope': {
    name: 'Стереотруба',
    type: 'standard',
    category: 'reconnaissance', 
    description: 'Увеличивает дальность обзора на 25% в неподвижном состоянии',
    bonuses: {
      viewRangeStationary: 0.25
    },
    cost: 500000,
    icon: '/src/assets/images/equipment/binocular-telescope.webp'
  },

  'camouflage-net': {
    name: 'Маскировочная сеть',
    type: 'standard',
    category: 'reconnaissance',
    description: 'Улучшает маскировку на 15% в неподвижном состоянии',
    bonuses: {
      camouflageBonusStationary: 0.15
    },
    cost: 100000,
    icon: '/src/assets/images/equipment/camouflage-net.webp'
  }
};

// СПЕЦИАЛЬНОЕ ОБОРУДОВАНИЕ
export const SPECIAL_EQUIPMENT = {
  'experimental-optics': {
    name: 'Экспериментальная оптика',
    type: 'special',
    category: 'reconnaissance',
    description: 'Увеличивает дальность обзора на 13%',
    bonuses: {
      viewRange: 0.13
    },
    cost: 1000000,
    icon: '/src/assets/images/equipment/experimental-optics.webp'
  },

  'experimental-aiming': {
    name: 'Экспериментальная система наводки',
    type: 'special',
    category: 'firepower',
    description: 'Уменьшает время сведения на 12.5%',
    bonuses: {
      aimTime: -0.125
    },
    cost: 1000000,
    icon: '/src/assets/images/equipment/experimental-aiming.webp'
  },

  'experimental-loading': {
    name: 'Экспериментальная система заряжания',
    type: 'special',
    category: 'firepower',
    description: 'Уменьшает время перезарядки на 12.5%',
    bonuses: {
      reloadTime: -0.125
    },
    cost: 1000000,
    icon: '/src/assets/images/equipment/experimental-loading.webp'
  }
};

// УЛУЧШЕННОЕ ОБОРУДОВАНИЕ
export const IMPROVED_EQUIPMENT = {
  'improved-gun-rammer': {
    name: 'Улучшенный досылатель',
    type: 'improved',
    category: 'firepower',
    description: 'Уменьшает время перезарядки на 12%',
    bonuses: {
      reloadTime: -0.12
    },
    cost: 3000000,
    icon: '/src/assets/images/equipment/improved-gun-rammer.webp'
  },

  'improved-vertical-stabilizer': {
    name: 'Улучшенный стабилизатор',
    type: 'improved',
    category: 'firepower',
    description: 'Уменьшает разброс при движении на 22%',
    bonuses: {
      dispersion: -0.22
    },
    cost: 3000000,
    icon: '/src/assets/images/equipment/improved-vertical-stabilizer.webp'
  },

  'improved-optics': {
    name: 'Улучшенная оптика',
    type: 'improved',
    category: 'reconnaissance',
    description: 'Увеличивает дальность обзора на 12%',
    bonuses: {
      viewRange: 0.12
    },
    cost: 3000000,
    icon: '/src/assets/images/equipment/improved-optics.webp'
  }
};

// ЭКСПЕРИМЕНТАЛЬНОЕ ОБОРУДОВАНИЕ
export const EXPERIMENTAL_EQUIPMENT = {
  'bond-gun-rammer': {
    name: 'Трофейный досылатель',
    type: 'bond',
    category: 'firepower',
    description: 'Уменьшает время перезарядки на 12.5%',
    bonuses: {
      reloadTime: -0.125
    },
    cost: 'bonds',
    icon: '/src/assets/images/equipment/bond-gun-rammer.webp'
  },

  'bond-vertical-stabilizer': {
    name: 'Трофейный стабилизатор',
    type: 'bond',
    category: 'firepower', 
    description: 'Уменьшает разброс при движении на 22.5%',
    bonuses: {
      dispersion: -0.225
    },
    cost: 'bonds',
    icon: '/src/assets/images/equipment/bond-vertical-stabilizer.webp'
  },

  'bond-optics': {
    name: 'Трофейная оптика',
    type: 'bond',
    category: 'reconnaissance',
    description: 'Увеличивает дальность обзора на 12.5%',
    bonuses: {
      viewRange: 0.125
    },
    cost: 'bonds',
    icon: '/src/assets/images/equipment/bond-optics.webp'
  }
};

// Категории оборудования
export const EQUIPMENT_CATEGORIES = {
  firepower: {
    name: 'Огневая мощь',
    icon: '🔥',
    color: '#ef4444'
  },
  survivability: {
    name: 'Живучесть', 
    icon: '🛡️',
    color: '#10b981'
  },
  mobility: {
    name: 'Мобильность',
    icon: '⚡',
    color: '#f59e0b'
  },
  reconnaissance: {
    name: 'Разведка',
    icon: '👁️',
    color: '#3b82f6'
  },
  crew: {
    name: 'Экипаж',
    icon: '👥',
    color: '#8b5cf6'
  }
};

// Типы оборудования
export const EQUIPMENT_TYPES = {
  standard: {
    name: 'Стандартное',
    color: '#6b7280'
  },
  special: {
    name: 'Специальное',
    color: '#3b82f6'
  },
  improved: {
    name: 'Улучшенное',
    color: '#8b5cf6'
  },
  bond: {
    name: 'Трофейное',
    color: '#f59e0b'
  }
};

// ОПТИМИЗИРОВАННЫЕ ИНДЕКСЫ ДЛЯ ОБОРУДОВАНИЯ
let optimizedEquipmentData = null;
let equipmentByName = null;
let equipmentByCategory = null;
let equipmentByType = null;

/**
 * СОЗДАНИЕ ОПТИМИЗИРОВАННЫХ ИНДЕКСОВ ОБОРУДОВАНИЯ
 */
function createOptimizedEquipmentIndexes() {
  if (optimizedEquipmentData) return optimizedEquipmentData;

  const allEquipment = [];
  const nameIndex = new Map();
  const categoryIndex = new Map();
  const typeIndex = new Map();

  // Объединяем все типы оборудования
  const allEquipmentSources = [
    STANDARD_EQUIPMENT,
    SPECIAL_EQUIPMENT,
    IMPROVED_EQUIPMENT,
    EXPERIMENTAL_EQUIPMENT
  ];

  allEquipmentSources.forEach(equipmentSource => {
    Object.entries(equipmentSource).forEach(([key, equipment]) => {
      const optimizedEquipment = {
        ...equipment,
        id: key,
        searchKey: `${equipment.name} ${equipment.category} ${equipment.type}`.toLowerCase()
      };

      allEquipment.push(optimizedEquipment);

      // Индекс по имени
      nameIndex.set(equipment.name.toLowerCase(), optimizedEquipment);

      // Индекс по категории
      if (!categoryIndex.has(equipment.category)) {
        categoryIndex.set(equipment.category, []);
      }
      categoryIndex.get(equipment.category).push(optimizedEquipment);

      // Индекс по типу
      if (!typeIndex.has(equipment.type)) {
        typeIndex.set(equipment.type, []);
      }
      typeIndex.get(equipment.type).push(optimizedEquipment);
    });
  });

  optimizedEquipmentData = {
    allEquipment,
    nameIndex,
    categoryIndex,
    typeIndex,
    totalCount: allEquipment.length
  };

  equipmentByName = nameIndex;
  equipmentByCategory = categoryIndex;
  equipmentByType = typeIndex;

  console.log(`📊 Optimized equipment data created: ${allEquipment.length} items indexed`);
  return optimizedEquipmentData;
}

/**
 * БЫСТРЫЙ ПОИСК ОБОРУДОВАНИЯ ПО ИМЕНИ
 */
export function getEquipmentByName(name) {
  if (!equipmentByName) createOptimizedEquipmentIndexes();
  return equipmentByName.get(name.toLowerCase());
}

/**
 * БЫСТРЫЙ ПОИСК ОБОРУДОВАНИЯ ПО КАТЕГОРИИ
 */
export function getEquipmentByCategory(category) {
  if (!equipmentByCategory) createOptimizedEquipmentIndexes();
  return equipmentByCategory.get(category) || [];
}

/**
 * БЫСТРЫЙ ПОИСК ОБОРУДОВАНИЯ ПО ТИПУ
 */
export function getEquipmentByType(type) {
  if (!equipmentByType) createOptimizedEquipmentIndexes();
  return equipmentByType.get(type) || [];
}

/**
 * ПОЛУЧЕНИЕ ВСЕГО ОБОРУДОВАНИЯ В ОПТИМИЗИРОВАННОМ ФОРМАТЕ
 */
export function getAllOptimizedEquipment() {
  const data = createOptimizedEquipmentIndexes();
  return data.allEquipment;
}

/**
 * ПОЛУЧЕНИЕ СТАТИСТИКИ ОБОРУДОВАНИЯ
 */
export function getEquipmentDataStats() {
  const data = createOptimizedEquipmentIndexes();
  return {
    totalEquipment: data.totalCount,
    categoriesCount: data.categoryIndex.size,
    typesCount: data.typeIndex.size,
    indexesCreated: !!optimizedEquipmentData
  };
}
