/**
 * МОДУЛЬ ИНИЦИАЛИЗАЦИИ ПРИЛОЖЕНИЯ
 * Выделен из main.js для улучшения структуры кода
 */

import { simplifiedInitializer } from './SimplifiedInitializer.js';
import {
  initializeDependencies,
  getTankManager,
  getFilterService,
  getTanksData,
  getState,
  checkCircularDependencies
} from './DependencyManager.js';

/**
 * ИНИЦИАЛИЗАЦИЯ ОСНОВНЫХ СИСТЕМ
 */
export async function initializeCoreServices() {
  console.log('📦 Loading critical systems...');
  simplifiedInitializer.updateProgress(30, 'Загрузка систем...');

  // МИНИМАЛЬНЫЕ ИМПОРТЫ - ТОЛЬКО КРИТИЧЕСКИЕ
  const [{ initTheme }] = await Promise.all([
    import('../utils/theme.js')
  ]);
  
  console.log('✅ Critical systems loaded successfully');
  simplifiedInitializer.updateProgress(50, 'Системы загружены');

  // МИНИМАЛЬНАЯ ИНИЦИАЛИЗАЦИЯ
  console.log('⚡ Initializing essential systems only...');
  console.log('✅ Performance manager initialized');
  console.log('🚀 Stable optimizer initialized automatically');
  console.log('💾 Basic caching ready...');

  console.log('🎨 Initializing theme...');
  initTheme();

  return true;
}

/**
 * ЗАГРУЗКА ДАННЫХ ТАНКОВ И ОСНОВНЫХ КОМПОНЕНТОВ
 */
export async function loadApplicationData() {
  console.log('🚀 Starting optimized app initialization...');
  simplifiedInitializer.updateProgress(70, 'Загрузка основных систем...');

  // Инициализируем систему зависимостей
  await initializeDependencies();

  // Проверяем циклические зависимости
  checkCircularDependencies();

  // Получаем зависимости через менеджер
  const [tankManager, filterService, tanksData, state] = await Promise.all([
    getTankManager(),
    getFilterService(),
    getTanksData(),
    getState()
  ]);

  // Устанавливаем глобальные ссылки для обратной совместимости
  window.tankManager = tankManager;
  window.filterService = filterService;
  window.state = state;

  console.log('✅ Dependency-managed app initialization complete');
  console.log('📦 Processing tank data...');
  simplifiedInitializer.updateProgress(75, 'Обработка данных танков...');

  if (!tanksData || typeof tanksData !== 'object') {
    throw new Error('Invalid tanks data');
  }

  console.log('📊 Raw tank data loaded, countries:', Object.keys(tanksData));

  // Convert tanksData object to flat array
  const flattenedTanks = [];
  Object.entries(tanksData).forEach(([country, types]) => {
    Object.entries(types).forEach(([type, tanks]) => {
      tanks.forEach(tank => {
        flattenedTanks.push({
          ...tank,
          country,
          type,
        });
      });
    });
  });

  state.allTanks = flattenedTanks;
  window.tanksData = flattenedTanks; // For compatibility

  console.log(`✅ Tank data processed: ${flattenedTanks.length} tanks`);
  simplifiedInitializer.updateProgress(80, 'Данные танков обработаны');

  return { tankManager, filterService, tanksData: flattenedTanks, state };
}

/**
 * ИНИЦИАЛИЗАЦИЯ UI КОМПОНЕНТОВ
 */
export async function initializeUIComponents(currentPath) {
  console.log('🚀 Starting router and tank list...');
  simplifiedInitializer.updateProgress(90, 'Запуск приложения...');

  // Очищаем localStorage при заходе на главную страницу
  if (currentPath === '/' || currentPath === '') {
    localStorage.setItem('activeMenuItem', 'overview');
    localStorage.removeItem('selectedTank');
    localStorage.removeItem('filtersBeforeSelection');
  }

  // УЛЬТРА-ОПТИМИЗИРОВАННАЯ ЗАГРУЗКА UI КОМПОНЕНТОВ
  const [
    { initRouter },
    { getCachedElement },
    { setupSidebarMenuItems, onMenuSelected, initializeOverviewHandlers }
  ] = await Promise.all([
    import('../router/index.js').then(m => ({ initRouter: m.initRouter })),
    import('../utils/CacheMigrationAdapter.js').then(m => ({ getCachedElement: m.getCachedElement })),
    import('../utils/ui.js')
  ]);

  // Initialize router with tank manager
  initRouter({
    initializeAllTanksCache: () => Promise.resolve(), // Data already loaded
    cacheDOMElements: () => {
      // Cache DOM elements
      getCachedElement('#nation-filter');
      getCachedElement('#type-filter');
    },
    applyFiltersAndRenderTankList: (preserveSelection = false) => {
      try {
        window.tankManager.applyFiltersAndRenderTankList(preserveSelection);
      } catch (error) {
        console.error('Error applying filters:', error);
      }
    }
  });

  // Setup sidebar menu functionality
  console.log('🎛️ Setting up sidebar menu...');
  setupSidebarMenuItems();

  return { initRouter, getCachedElement, setupSidebarMenuItems, onMenuSelected, initializeOverviewHandlers };
}

/**
 * ОПРЕДЕЛЕНИЕ НАЧАЛЬНОЙ СЕКЦИИ
 */
export function determineInitialSection(currentPath) {
  console.log('🌐 Current path:', currentPath);

  let initialSection = 'overview';

  if (currentPath === '/vehicles') {
    initialSection = 'vehicles';
    console.log('🚗 Setting initial section: vehicles');
  } else if (currentPath.startsWith('/vehicles/')) {
    // Это страница конкретного танка - устанавливаем vehicles секцию
    initialSection = 'vehicles';
    console.log('🚗 Setting initial section: vehicles (tank page)');
  } else if (currentPath === '/' || currentPath === '') {
    initialSection = 'overview';
    // КРИТИЧНО: Принудительно очищаем localStorage при заходе на главную
    localStorage.setItem('activeMenuItem', 'overview');
    localStorage.removeItem('selectedTank');
    console.log('📊 Setting initial section: overview');
  } else {
    // Extract section from path like /compare, /tactics, etc.
    const pathParts = currentPath.split('/');
    const rawSection = pathParts[1] || 'overview';
    // Для всех остальных путей используем первую часть как секцию
    initialSection = rawSection;
    console.log(`🎯 Setting initial section: ${initialSection}`);
  }

  return initialSection;
}

/**
 * ИНИЦИАЛИЗАЦИЯ ФИЛЬТРОВ И ДОПОЛНИТЕЛЬНЫХ КОМПОНЕНТОВ
 */
export async function initializeFiltersAndComponents(initialSection) {
  console.log('🎛️ Initializing filters and UI components...');

  // Initialize filter manager
  const { filterManager } = await import('../core/FilterManager.js');
  window.filterManager = filterManager;

  // Initialize event manager for filter handlers
  const { eventManager } = await import('../core/EventManager.js');
  eventManager.initialize();
  console.log('✅ Event manager initialized - filters should work');

  // Initialize settings handlers (с проверкой дублирования)
  if (!window.settingsManagerInitialized) {
    const { initializeSettingsHandlers } = await import('../components/SettingsManager.js');
    initializeSettingsHandlers();
    window.settingsManagerInitialized = true;
    console.log('✅ Settings handlers initialized');
  } else {
    console.log('🚫 Settings handlers already initialized, skipping');
  }

  // Initialize Tier filter только на странице vehicles
  if (initialSection === 'vehicles' || initialSection.startsWith('vehicles/')) {
    const { tierFilter } = await import('../components/TierFilter.js');
    const tierContainer = document.querySelector('#tier-filter-container');

    if (tierContainer) {
      tierFilter.init('#tier-filter-container', (selectedTiers) => {
        // Обновляем состояние при изменении Tier фильтра
        window.state.selectedTiers = selectedTiers;

        // Применяем фильтры
        if (window.tankManager) {
          window.tankManager.applyFiltersAndRenderTankList();
        }

        console.log('🏆 Tier filter changed:', selectedTiers);
      });
      console.log('🏆 Tier filter initialized for vehicles page');
    }
  }
  console.log('🏆 Tier filter initialized');

  return true;
}

/**
 * ИНИЦИАЛИЗАЦИЯ ДОПОЛНИТЕЛЬНЫХ СИСТЕМ
 */
export async function initializeAdditionalSystems() {
  // Initialize notifications
  const { notificationManager } = await import('../utils/NotificationManager.js');
  window.notificationManager = notificationManager;

  // Инициализируем настройку звука из localStorage
  const soundSetting = localStorage.getItem('notificationSounds');
  if (soundSetting !== null) {
    notificationManager.setSoundEnabled(soundSetting === 'true');
  }

  console.log('✅ Notification manager initialized');

  // Initialize unified compare system
  const { initializeCompareSystem } = await import('../components/CompareSystem.js');
  initializeCompareSystem();
  console.log('✅ Unified compare system initialized');

  // Load development tools
  if (import.meta.env.DEV) {
    try {
      await import('../utils/debug-commands.js');
      console.log('🧪 Development tools loaded');
    } catch (error) {
      console.log('⚠️ Development tools not available');
    }
  }

  return true;
}

/**
 * ЗАВЕРШЕНИЕ ИНИЦИАЛИЗАЦИИ
 */
export async function finalizeInitialization(startTime, initialSection) {
  // Trigger initial tank list rendering ONLY if we're on vehicles section
  if (initialSection === 'vehicles') {
    try {
      window.tankManager.applyFiltersAndRenderTankList(false);
    } catch (error) {
      console.error('❌ Error rendering tank list:', error);
    }
  }

  // Complete initialization
  console.log('🎯 Completing initialization...');
  simplifiedInitializer.updateProgress(100, 'Готово!');

  // Hide loading screen
  setTimeout(async () => {
    console.log('🎭 Hiding loader...');
    await simplifiedInitializer.hideLoadingScreen();
    console.log('✅ Loader hidden successfully');
  }, 50);

  const totalTime = performance.now() - startTime;

  // Set completion flags
  window.MAIN_JS_LOADING = false;
  window.MAIN_JS_READY = true;

  // Update title when done
  document.title = 'Efficiency';

  // APP READY
  console.log(`🎉 Application initialized successfully in ${totalTime.toFixed(2)}ms`);

  return totalTime;
}
