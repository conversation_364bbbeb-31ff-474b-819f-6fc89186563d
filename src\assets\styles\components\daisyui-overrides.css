/* ========================================
   DAISYUI OVERRIDES FOR TANK THEME
   ======================================== */

/* Кастомизация DaisyUI компонентов для темы танков */

/* ========================================
   BUTTONS - ТОЛЬКО ДЛЯ DAISYUI КОМПОНЕНТОВ
   ======================================== */

/* Исключаем кнопку "Назад к списку" */
.btn:not(.back-to-list-btn) {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn:not(.back-to-list-btn):hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-ghost:not(.back-to-list-btn) {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.btn-ghost:not(.back-to-list-btn):hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

/* ======================================== 
   BADGES
   ======================================== */

.badge {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.badge:hover {
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Tank type badge colors */
.badge-primary {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6, #6d28d9);
  border: 1px solid rgba(124, 58, 237, 0.5);
  color: #ffffff;
}

.badge-secondary {
  background: linear-gradient(135deg, #db2777, #ec4899, #be185d);
  border: 1px solid rgba(219, 39, 119, 0.5);
  color: #ffffff;
}

.badge-accent {
  background: linear-gradient(135deg, #2563eb, #3b82f6, #1d4ed8);
  border: 1px solid rgba(37, 99, 235, 0.5);
  color: #ffffff;
}

.badge-success {
  background: linear-gradient(135deg, #059669, #10b981, #047857);
  border: 1px solid rgba(5, 150, 105, 0.5);
  color: #ffffff;
}

.badge-error {
  background: linear-gradient(135deg, #dc2626, #ef4444, #b91c1c);
  border: 1px solid rgba(220, 38, 38, 0.5);
  color: #ffffff;
}

.badge-outline {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  backdrop-filter: blur(10px);
}

.badge-outline:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
}

/* ======================================== 
   CARDS
   ======================================== */

.card {
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

/* ========================================
   TABLES - ТОЛЬКО ДЛЯ BUILDS TABLE
   ======================================== */

/* Исключаем AI Efficiency элементы из DaisyUI стилей */
#toggle-notes-icon {
  pointer-events: auto;
  cursor: pointer;
  z-index: 1000;
}

/* Убираем !important - используем оригинальные стили из sidebar.css */

/* УБРАНЫ СТИЛИ ДЛЯ ТАБЛИЦЫ СБОРОК - ИСПОЛЬЗУЕМ ТОЛЬКО ОРИГИНАЛЬНЫЕ */

/* ======================================== 
   MODALS
   ======================================== */

.modal-box {
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(30, 39, 59, 0.9);
}

/* ======================================== 
   FORMS
   ======================================== */

.input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  backdrop-filter: blur(10px);
}

.input:focus {
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* ======================================== 
   ALERTS
   ======================================== */

.alert {
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.alert-success {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.3);
  color: #10b981;
}

.alert-error {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.alert-warning {
  background: rgba(245, 158, 11, 0.2);
  border-color: rgba(245, 158, 11, 0.3);
  color: #f59e0b;
}

.alert-info {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

/* ======================================== 
   TABS
   ======================================== */

.tabs {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab {
  color: rgba(255, 255, 255, 0.7);
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.tab:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
}

.tab-active {
  color: #8b5cf6;
  border-bottom-color: #8b5cf6;
  background: rgba(139, 92, 246, 0.1);
}

/* ======================================== 
   PROGRESS
   ======================================== */

.progress {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.progress::-webkit-progress-value {
  background: linear-gradient(90deg, #8b5cf6, #a78bfa);
}

.progress::-moz-progress-bar {
  background: linear-gradient(90deg, #8b5cf6, #a78bfa);
}

/* ======================================== 
   LOADING
   ======================================== */

.loading {
  color: #8b5cf6;
}

/* ======================================== 
   TOOLTIPS
   ======================================== */

.tooltip {
  backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ======================================== 
   RESPONSIVE ADJUSTMENTS
   ======================================== */

@media (max-width: 768px) {
  .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  
  .btn:not(.back-to-list-btn) {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  .card {
    margin: 0.5rem;
  }
}
