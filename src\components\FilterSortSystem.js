/**
 * ПРОДВИНУТАЯ СИСТЕМА ФИЛЬТРАЦИИ И СОРТИРОВКИ
 * Интеллектуальная фильтрация характеристик и сортировка танков
 */

export class FilterSortSystem {
  constructor() {
    this.filters = new Map();
    this.sortCriteria = [];
    this.searchQuery = '';
    this.activeFilters = new Set();
    
    // Предустановленные фильтры
    this.presetFilters = {
      tier: { min: 1, max: 10, active: false },
      type: { values: [], active: false },
      country: { values: [], active: false },
      damage: { min: 0, max: 1000, active: false },
      armor: { min: 0, max: 500, active: false },
      speed: { min: 0, max: 100, active: false },
      viewRange: { min: 0, max: 500, active: false }
    };
    
    // Настройки сортировки
    this.sortOptions = [
      { key: 'name', label: 'Name', type: 'string' },
      { key: 'tier', label: 'Tier', type: 'number' },
      { key: 'type', label: 'Type', type: 'string' },
      { key: 'country', label: 'Country', type: 'string' },
      { key: 'damage', label: 'Damage', type: 'number' },
      { key: 'armor', label: 'Armor', type: 'number' },
      { key: 'speed', label: 'Speed', type: 'number' },
      { key: 'dpm', label: 'DPM', type: 'number' },
      { key: 'penetration', label: 'Penetration', type: 'number' },
      { key: 'accuracy', label: 'Accuracy', type: 'number' },
      { key: 'viewRange', label: 'View Range', type: 'number' }
    ];
    
    this.initializeSystem();
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ СИСТЕМЫ
   */
  initializeSystem() {
    this.createFilterInterface();
    this.setupEventListeners();
    this.loadSavedFilters();
    
    console.log('🔍 Filter & Sort System initialized');
  }

  /**
   * СОЗДАНИЕ ИНТЕРФЕЙСА ФИЛЬТРОВ
   */
  createFilterInterface() {
    const container = document.getElementById('compare-content');
    if (!container) return;

    // Создаем панель фильтров
    const filterPanel = document.createElement('div');
    filterPanel.className = 'filter-sort-panel';
    filterPanel.innerHTML = this.createFilterPanelHTML();
    
    // Вставляем панель в начало контейнера
    container.insertBefore(filterPanel, container.firstChild);
    
    this.filterPanel = filterPanel;
  }

  /**
   * СОЗДАНИЕ HTML ПАНЕЛИ ФИЛЬТРОВ
   */
  createFilterPanelHTML() {
    return `
      <div class="filter-panel-header">
        <div class="panel-title">
          <span class="filter-icon">🔍</span>
          <span class="title-text">Advanced Filters & Sort</span>
        </div>
        <div class="panel-controls">
          <button class="filter-toggle-btn" id="toggle-filters">
            <span class="toggle-icon">▼</span>
          </button>
          <button class="filter-reset-btn" id="reset-filters">
            <span class="reset-icon">🔄</span>
            Reset
          </button>
        </div>
      </div>
      
      <div class="filter-panel-content" id="filter-content">
        <div class="filter-sections">
          <!-- Поиск -->
          <div class="filter-section search-section">
            <label class="filter-label">
              <span class="label-icon">🔎</span>
              Quick Search
            </label>
            <div class="search-input-container">
              <input type="text" 
                     class="search-input" 
                     id="tank-search" 
                     placeholder="Search tanks by name..."
                     autocomplete="off">
              <div class="search-suggestions" id="search-suggestions"></div>
            </div>
          </div>
          
          <!-- Основные фильтры -->
          <div class="filter-section basic-filters">
            <h3 class="section-title">Basic Filters</h3>
            <div class="filter-grid">
              ${this.createBasicFiltersHTML()}
            </div>
          </div>
          
          <!-- Характеристики -->
          <div class="filter-section characteristics-filters">
            <h3 class="section-title">Characteristics</h3>
            <div class="characteristics-grid">
              ${this.createCharacteristicsFiltersHTML()}
            </div>
          </div>
          
          <!-- Сортировка -->
          <div class="filter-section sort-section">
            <h3 class="section-title">Sort Options</h3>
            <div class="sort-controls">
              ${this.createSortControlsHTML()}
            </div>
          </div>
          
          <!-- Предустановки -->
          <div class="filter-section presets-section">
            <h3 class="section-title">Filter Presets</h3>
            <div class="preset-buttons">
              ${this.createPresetButtonsHTML()}
            </div>
          </div>
        </div>
        
        <!-- Активные фильтры -->
        <div class="active-filters" id="active-filters">
          <div class="active-filters-header">
            <span class="active-icon">✓</span>
            Active Filters
          </div>
          <div class="active-filters-list" id="active-filters-list"></div>
        </div>
      </div>
    `;
  }

  /**
   * СОЗДАНИЕ ОСНОВНЫХ ФИЛЬТРОВ
   */
  createBasicFiltersHTML() {
    return `
      <div class="filter-item">
        <label class="filter-label">Tier Range</label>
        <div class="range-slider-container">
          <input type="range" class="range-slider" id="tier-min" min="1" max="10" value="1">
          <input type="range" class="range-slider" id="tier-max" min="1" max="10" value="10">
          <div class="range-values">
            <span id="tier-min-value">1</span> - <span id="tier-max-value">10</span>
          </div>
        </div>
      </div>
      
      <div class="filter-item">
        <label class="filter-label">Tank Type</label>
        <div class="checkbox-group">
          <label class="checkbox-item">
            <input type="checkbox" value="light" class="type-filter">
            <span class="checkbox-custom"></span>
            <span class="checkbox-label">Light Tank</span>
          </label>
          <label class="checkbox-item">
            <input type="checkbox" value="medium" class="type-filter">
            <span class="checkbox-custom"></span>
            <span class="checkbox-label">Medium Tank</span>
          </label>
          <label class="checkbox-item">
            <input type="checkbox" value="heavy" class="type-filter">
            <span class="checkbox-custom"></span>
            <span class="checkbox-label">Heavy Tank</span>
          </label>
          <label class="checkbox-item">
            <input type="checkbox" value="td" class="type-filter">
            <span class="checkbox-custom"></span>
            <span class="checkbox-label">Tank Destroyer</span>
          </label>
          <label class="checkbox-item">
            <input type="checkbox" value="spg" class="type-filter">
            <span class="checkbox-custom"></span>
            <span class="checkbox-label">Artillery</span>
          </label>
        </div>
      </div>
      
      <div class="filter-item">
        <label class="filter-label">Country</label>
        <select class="country-select" id="country-filter" multiple>
          <option value="usa">🇺🇸 USA</option>
          <option value="ussr">🇷🇺 USSR</option>
          <option value="germany">🇩🇪 Germany</option>
          <option value="uk">🇬🇧 United Kingdom</option>
          <option value="france">🇫🇷 France</option>
          <option value="china">🇨🇳 China</option>
          <option value="japan">🇯🇵 Japan</option>
          <option value="czech">🇨🇿 Czechoslovakia</option>
          <option value="sweden">🇸🇪 Sweden</option>
          <option value="poland">🇵🇱 Poland</option>
          <option value="italy">🇮🇹 Italy</option>
        </select>
      </div>
    `;
  }

  /**
   * СОЗДАНИЕ ФИЛЬТРОВ ХАРАКТЕРИСТИК
   */
  createCharacteristicsFiltersHTML() {
    const characteristics = [
      { key: 'damage', label: 'Alpha Damage', min: 0, max: 1000, unit: 'HP' },
      { key: 'dpm', label: 'DPM', min: 0, max: 5000, unit: 'HP/min' },
      { key: 'penetration', label: 'Penetration', min: 0, max: 400, unit: 'mm' },
      { key: 'armor', label: 'Armor', min: 0, max: 500, unit: 'mm' },
      { key: 'speed', label: 'Top Speed', min: 0, max: 100, unit: 'km/h' },
      { key: 'viewRange', label: 'View Range', min: 0, max: 500, unit: 'm' }
    ];

    return characteristics.map(char => `
      <div class="characteristic-filter">
        <label class="filter-label">${char.label}</label>
        <div class="range-input-container">
          <input type="number" 
                 class="range-input" 
                 id="${char.key}-min" 
                 min="${char.min}" 
                 max="${char.max}" 
                 value="${char.min}"
                 placeholder="Min">
          <span class="range-separator">—</span>
          <input type="number" 
                 class="range-input" 
                 id="${char.key}-max" 
                 min="${char.min}" 
                 max="${char.max}" 
                 value="${char.max}"
                 placeholder="Max">
          <span class="range-unit">${char.unit}</span>
        </div>
        <div class="range-slider-dual">
          <input type="range" 
                 class="dual-range" 
                 id="${char.key}-slider-min" 
                 min="${char.min}" 
                 max="${char.max}" 
                 value="${char.min}">
          <input type="range" 
                 class="dual-range" 
                 id="${char.key}-slider-max" 
                 min="${char.min}" 
                 max="${char.max}" 
                 value="${char.max}">
        </div>
      </div>
    `).join('');
  }

  /**
   * СОЗДАНИЕ КОНТРОЛОВ СОРТИРОВКИ
   */
  createSortControlsHTML() {
    return `
      <div class="sort-primary">
        <label class="filter-label">Primary Sort</label>
        <div class="sort-control-group">
          <select class="sort-select" id="primary-sort">
            <option value="">Select characteristic...</option>
            ${this.sortOptions.map(option => 
              `<option value="${option.key}">${option.label}</option>`
            ).join('')}
          </select>
          <button class="sort-direction-btn" id="primary-direction" data-direction="asc">
            <span class="direction-icon">↑</span>
          </button>
        </div>
      </div>
      
      <div class="sort-secondary">
        <label class="filter-label">Secondary Sort</label>
        <div class="sort-control-group">
          <select class="sort-select" id="secondary-sort">
            <option value="">Select characteristic...</option>
            ${this.sortOptions.map(option => 
              `<option value="${option.key}">${option.label}</option>`
            ).join('')}
          </select>
          <button class="sort-direction-btn" id="secondary-direction" data-direction="asc">
            <span class="direction-icon">↑</span>
          </button>
        </div>
      </div>
      
      <div class="sort-actions">
        <button class="sort-apply-btn" id="apply-sort">
          <span class="apply-icon">✓</span>
          Apply Sort
        </button>
        <button class="sort-clear-btn" id="clear-sort">
          <span class="clear-icon">✗</span>
          Clear
        </button>
      </div>
    `;
  }

  /**
   * СОЗДАНИЕ КНОПОК ПРЕДУСТАНОВОК
   */
  createPresetButtonsHTML() {
    const presets = [
      { id: 'damage-dealers', label: '💥 Damage Dealers', description: 'High damage tanks' },
      { id: 'armor-kings', label: '🛡️ Armor Kings', description: 'Heavy armored tanks' },
      { id: 'speed-demons', label: '⚡ Speed Demons', description: 'Fast and agile tanks' },
      { id: 'snipers', label: '🎯 Snipers', description: 'Long range specialists' },
      { id: 'brawlers', label: '👊 Brawlers', description: 'Close combat tanks' },
      { id: 'scouts', label: '👁️ Scouts', description: 'Reconnaissance tanks' }
    ];

    return presets.map(preset => `
      <button class="preset-btn" data-preset="${preset.id}" title="${preset.description}">
        ${preset.label}
      </button>
    `).join('');
  }

  /**
   * НАСТРОЙКА ОБРАБОТЧИКОВ СОБЫТИЙ
   */
  setupEventListeners() {
    // Переключение панели фильтров
    document.addEventListener('click', (e) => {
      if (e.target.closest('#toggle-filters')) {
        this.toggleFilterPanel();
      } else if (e.target.closest('#reset-filters')) {
        this.resetAllFilters();
      } else if (e.target.closest('.preset-btn')) {
        const presetId = e.target.dataset.preset;
        this.applyPreset(presetId);
      } else if (e.target.closest('#apply-sort')) {
        this.applySorting();
      } else if (e.target.closest('#clear-sort')) {
        this.clearSorting();
      } else if (e.target.closest('.sort-direction-btn')) {
        this.toggleSortDirection(e.target);
      }
    });

    // Поиск
    const searchInput = document.getElementById('tank-search');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        this.handleSearch(e.target.value);
      });
    }

    // Фильтры типов
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('type-filter')) {
        this.updateTypeFilter();
      } else if (e.target.id === 'country-filter') {
        this.updateCountryFilter();
      } else if (e.target.classList.contains('range-slider') || 
                 e.target.classList.contains('range-input') ||
                 e.target.classList.contains('dual-range')) {
        this.updateRangeFilter(e.target);
      }
    });

    // Слайдеры диапазонов
    document.addEventListener('input', (e) => {
      if (e.target.classList.contains('dual-range')) {
        this.updateDualRangeSlider(e.target);
      }
    });
  }

  /**
   * ПЕРЕКЛЮЧЕНИЕ ПАНЕЛИ ФИЛЬТРОВ
   */
  toggleFilterPanel() {
    const content = document.getElementById('filter-content');
    const toggleBtn = document.getElementById('toggle-filters');
    const icon = toggleBtn.querySelector('.toggle-icon');
    
    if (content.classList.contains('collapsed')) {
      content.classList.remove('collapsed');
      icon.textContent = '▼';
    } else {
      content.classList.add('collapsed');
      icon.textContent = '▶';
    }
  }

  /**
   * ОБРАБОТКА ПОИСКА
   */
  handleSearch(query) {
    this.searchQuery = query.toLowerCase();
    this.updateSearchSuggestions(query);
    this.applyFilters();
  }

  /**
   * ОБНОВЛЕНИЕ ПОДСКАЗОК ПОИСКА
   */
  updateSearchSuggestions(query) {
    const suggestionsContainer = document.getElementById('search-suggestions');
    if (!suggestionsContainer || query.length < 2) {
      suggestionsContainer.innerHTML = '';
      return;
    }

    // Симуляция поиска танков
    const mockTanks = [
      'T-34', 'Tiger I', 'Sherman', 'IS-3', 'Panther', 'KV-1',
      'M1 Abrams', 'Leopard 2', 'Challenger 2', 'T-90'
    ];

    const matches = mockTanks.filter(tank => 
      tank.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 5);

    suggestionsContainer.innerHTML = matches.map(tank => `
      <div class="search-suggestion" data-tank="${tank}">
        <span class="suggestion-icon">🔍</span>
        <span class="suggestion-text">${tank}</span>
      </div>
    `).join('');

    // Обработчики для подсказок
    suggestionsContainer.addEventListener('click', (e) => {
      const suggestion = e.target.closest('.search-suggestion');
      if (suggestion) {
        const tankName = suggestion.dataset.tank;
        document.getElementById('tank-search').value = tankName;
        this.handleSearch(tankName);
        suggestionsContainer.innerHTML = '';
      }
    });
  }

  /**
   * ОБНОВЛЕНИЕ ФИЛЬТРА ТИПОВ
   */
  updateTypeFilter() {
    const typeCheckboxes = document.querySelectorAll('.type-filter:checked');
    const selectedTypes = Array.from(typeCheckboxes).map(cb => cb.value);
    
    this.presetFilters.type.values = selectedTypes;
    this.presetFilters.type.active = selectedTypes.length > 0;
    
    this.updateActiveFilters();
    this.applyFilters();
  }

  /**
   * ОБНОВЛЕНИЕ ФИЛЬТРА СТРАН
   */
  updateCountryFilter() {
    const countrySelect = document.getElementById('country-filter');
    const selectedCountries = Array.from(countrySelect.selectedOptions).map(option => option.value);
    
    this.presetFilters.country.values = selectedCountries;
    this.presetFilters.country.active = selectedCountries.length > 0;
    
    this.updateActiveFilters();
    this.applyFilters();
  }

  /**
   * ОБНОВЛЕНИЕ ФИЛЬТРА ДИАПАЗОНА
   */
  updateRangeFilter(element) {
    const id = element.id;
    const [characteristic, type] = id.split('-');
    
    if (this.presetFilters[characteristic]) {
      const value = parseFloat(element.value);
      
      if (type === 'min' || type.includes('min')) {
        this.presetFilters[characteristic].min = value;
      } else if (type === 'max' || type.includes('max')) {
        this.presetFilters[characteristic].max = value;
      }
      
      this.presetFilters[characteristic].active = true;
      this.updateActiveFilters();
      this.applyFilters();
    }
  }

  /**
   * ОБНОВЛЕНИЕ ДВОЙНОГО СЛАЙДЕРА
   */
  updateDualRangeSlider(element) {
    const id = element.id;
    const [characteristic, , type] = id.split('-');
    const value = parseFloat(element.value);
    
    // Обновляем соответствующий input
    const inputId = `${characteristic}-${type}`;
    const input = document.getElementById(inputId);
    if (input) {
      input.value = value;
    }
    
    this.updateRangeFilter(element);
  }

  /**
   * ПРИМЕНЕНИЕ ПРЕДУСТАНОВКИ
   */
  applyPreset(presetId) {
    this.resetAllFilters();
    
    switch (presetId) {
      case 'damage-dealers':
        this.presetFilters.damage = { min: 400, max: 1000, active: true };
        this.presetFilters.dpm = { min: 2500, max: 5000, active: true };
        break;
      case 'armor-kings':
        this.presetFilters.armor = { min: 200, max: 500, active: true };
        this.presetFilters.type = { values: ['heavy'], active: true };
        break;
      case 'speed-demons':
        this.presetFilters.speed = { min: 60, max: 100, active: true };
        this.presetFilters.type = { values: ['light', 'medium'], active: true };
        break;
      case 'snipers':
        this.presetFilters.viewRange = { min: 400, max: 500, active: true };
        this.presetFilters.penetration = { min: 250, max: 400, active: true };
        break;
      case 'brawlers':
        this.presetFilters.armor = { min: 150, max: 500, active: true };
        this.presetFilters.damage = { min: 300, max: 1000, active: true };
        break;
      case 'scouts':
        this.presetFilters.viewRange = { min: 350, max: 500, active: true };
        this.presetFilters.type = { values: ['light'], active: true };
        break;
    }
    
    this.updateFilterInterface();
    this.updateActiveFilters();
    this.applyFilters();
  }

  /**
   * СБРОС ВСЕХ ФИЛЬТРОВ
   */
  resetAllFilters() {
    // Сбрасываем предустановленные фильтры
    Object.keys(this.presetFilters).forEach(key => {
      this.presetFilters[key].active = false;
      if (this.presetFilters[key].values) {
        this.presetFilters[key].values = [];
      }
    });
    
    // Очищаем поиск
    this.searchQuery = '';
    const searchInput = document.getElementById('tank-search');
    if (searchInput) {
      searchInput.value = '';
    }
    
    // Сбрасываем сортировку
    this.sortCriteria = [];
    
    this.updateFilterInterface();
    this.updateActiveFilters();
    this.applyFilters();
  }

  /**
   * ОБНОВЛЕНИЕ ИНТЕРФЕЙСА ФИЛЬТРОВ
   */
  updateFilterInterface() {
    // Обновляем чекбоксы типов
    document.querySelectorAll('.type-filter').forEach(checkbox => {
      checkbox.checked = this.presetFilters.type.values.includes(checkbox.value);
    });
    
    // Обновляем селект стран
    const countrySelect = document.getElementById('country-filter');
    if (countrySelect) {
      Array.from(countrySelect.options).forEach(option => {
        option.selected = this.presetFilters.country.values.includes(option.value);
      });
    }
    
    // Обновляем слайдеры и инпуты диапазонов
    Object.keys(this.presetFilters).forEach(key => {
      const filter = this.presetFilters[key];
      if (filter.min !== undefined && filter.max !== undefined) {
        const minInput = document.getElementById(`${key}-min`);
        const maxInput = document.getElementById(`${key}-max`);
        const minSlider = document.getElementById(`${key}-slider-min`);
        const maxSlider = document.getElementById(`${key}-slider-max`);
        
        if (minInput) minInput.value = filter.min;
        if (maxInput) maxInput.value = filter.max;
        if (minSlider) minSlider.value = filter.min;
        if (maxSlider) maxSlider.value = filter.max;
      }
    });
  }

  /**
   * ОБНОВЛЕНИЕ АКТИВНЫХ ФИЛЬТРОВ
   */
  updateActiveFilters() {
    const activeFiltersList = document.getElementById('active-filters-list');
    if (!activeFiltersList) return;
    
    const activeFilters = [];
    
    // Поиск
    if (this.searchQuery) {
      activeFilters.push({
        type: 'search',
        label: `Search: "${this.searchQuery}"`,
        value: this.searchQuery
      });
    }
    
    // Фильтры
    Object.keys(this.presetFilters).forEach(key => {
      const filter = this.presetFilters[key];
      if (filter.active) {
        if (filter.values && filter.values.length > 0) {
          activeFilters.push({
            type: key,
            label: `${key}: ${filter.values.join(', ')}`,
            value: filter.values
          });
        } else if (filter.min !== undefined && filter.max !== undefined) {
          activeFilters.push({
            type: key,
            label: `${key}: ${filter.min} - ${filter.max}`,
            value: { min: filter.min, max: filter.max }
          });
        }
      }
    });
    
    // Сортировка
    if (this.sortCriteria.length > 0) {
      this.sortCriteria.forEach((criteria, index) => {
        activeFilters.push({
          type: 'sort',
          label: `Sort ${index + 1}: ${criteria.key} (${criteria.direction})`,
          value: criteria
        });
      });
    }
    
    // Отображаем активные фильтры
    activeFiltersList.innerHTML = activeFilters.map(filter => `
      <div class="active-filter-item" data-type="${filter.type}">
        <span class="filter-label">${filter.label}</span>
        <button class="remove-filter-btn" data-filter-type="${filter.type}">×</button>
      </div>
    `).join('');
    
    // Показываем/скрываем секцию активных фильтров
    const activeFiltersContainer = document.getElementById('active-filters');
    if (activeFiltersContainer) {
      activeFiltersContainer.style.display = activeFilters.length > 0 ? 'block' : 'none';
    }
  }

  /**
   * ПРИМЕНЕНИЕ ФИЛЬТРОВ
   */
  applyFilters() {
    // Эмитируем событие для других компонентов
    const filterEvent = new CustomEvent('filtersChanged', {
      detail: {
        search: this.searchQuery,
        filters: this.presetFilters,
        sort: this.sortCriteria
      }
    });
    
    document.dispatchEvent(filterEvent);
    
    console.log('🔍 Filters applied:', {
      search: this.searchQuery,
      activeFilters: Object.keys(this.presetFilters).filter(key => this.presetFilters[key].active),
      sortCriteria: this.sortCriteria
    });
  }

  /**
   * ПРИМЕНЕНИЕ СОРТИРОВКИ
   */
  applySorting() {
    const primarySort = document.getElementById('primary-sort').value;
    const secondarySort = document.getElementById('secondary-sort').value;
    const primaryDirection = document.getElementById('primary-direction').dataset.direction;
    const secondaryDirection = document.getElementById('secondary-direction').dataset.direction;
    
    this.sortCriteria = [];
    
    if (primarySort) {
      this.sortCriteria.push({
        key: primarySort,
        direction: primaryDirection,
        priority: 1
      });
    }
    
    if (secondarySort && secondarySort !== primarySort) {
      this.sortCriteria.push({
        key: secondarySort,
        direction: secondaryDirection,
        priority: 2
      });
    }
    
    this.updateActiveFilters();
    this.applyFilters();
  }

  /**
   * ОЧИСТКА СОРТИРОВКИ
   */
  clearSorting() {
    this.sortCriteria = [];
    
    document.getElementById('primary-sort').value = '';
    document.getElementById('secondary-sort').value = '';
    
    this.updateActiveFilters();
    this.applyFilters();
  }

  /**
   * ПЕРЕКЛЮЧЕНИЕ НАПРАВЛЕНИЯ СОРТИРОВКИ
   */
  toggleSortDirection(button) {
    const currentDirection = button.dataset.direction;
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    
    button.dataset.direction = newDirection;
    button.querySelector('.direction-icon').textContent = newDirection === 'asc' ? '↑' : '↓';
    
    // Применяем сортировку если критерий уже выбран
    const sortSelect = button.previousElementSibling;
    if (sortSelect && sortSelect.value) {
      this.applySorting();
    }
  }

  /**
   * СОХРАНЕНИЕ ФИЛЬТРОВ
   */
  saveFilters() {
    const filtersData = {
      presetFilters: this.presetFilters,
      sortCriteria: this.sortCriteria,
      searchQuery: this.searchQuery
    };
    
    localStorage.setItem('tankCompareFilters', JSON.stringify(filtersData));
  }

  /**
   * ЗАГРУЗКА СОХРАНЕННЫХ ФИЛЬТРОВ
   */
  loadSavedFilters() {
    try {
      const savedData = localStorage.getItem('tankCompareFilters');
      if (savedData) {
        const filtersData = JSON.parse(savedData);
        
        this.presetFilters = { ...this.presetFilters, ...filtersData.presetFilters };
        this.sortCriteria = filtersData.sortCriteria || [];
        this.searchQuery = filtersData.searchQuery || '';
        
        this.updateFilterInterface();
        this.updateActiveFilters();
      }
    } catch (error) {
      console.error('Failed to load saved filters:', error);
    }
  }

  /**
   * ПОЛУЧЕНИЕ ОТФИЛЬТРОВАННЫХ ДАННЫХ
   */
  getFilteredData(data) {
    let filteredData = [...data];
    
    // Применяем поиск
    if (this.searchQuery) {
      filteredData = filteredData.filter(item => 
        item.name.toLowerCase().includes(this.searchQuery)
      );
    }
    
    // Применяем фильтры
    Object.keys(this.presetFilters).forEach(key => {
      const filter = this.presetFilters[key];
      if (filter.active) {
        if (filter.values && filter.values.length > 0) {
          filteredData = filteredData.filter(item => 
            filter.values.includes(item[key])
          );
        } else if (filter.min !== undefined && filter.max !== undefined) {
          filteredData = filteredData.filter(item => {
            const value = item[key] || 0;
            return value >= filter.min && value <= filter.max;
          });
        }
      }
    });
    
    // Применяем сортировку
    if (this.sortCriteria.length > 0) {
      filteredData.sort((a, b) => {
        for (const criteria of this.sortCriteria) {
          const aValue = a[criteria.key] || 0;
          const bValue = b[criteria.key] || 0;
          
          let comparison = 0;
          if (typeof aValue === 'string') {
            comparison = aValue.localeCompare(bValue);
          } else {
            comparison = aValue - bValue;
          }
          
          if (criteria.direction === 'desc') {
            comparison = -comparison;
          }
          
          if (comparison !== 0) {
            return comparison;
          }
        }
        return 0;
      });
    }
    
    return filteredData;
  }

  /**
   * УНИЧТОЖЕНИЕ СИСТЕМЫ
   */
  destroy() {
    if (this.filterPanel) {
      this.filterPanel.remove();
    }
    
    this.saveFilters();
    
    console.log('🔍 Filter & Sort System destroyed');
  }
}

// Создаем глобальный экземпляр
export const filterSortSystem = new FilterSortSystem();
