/**
 * Менеджер настроек приложения
 * Отвечает за управление пользовательскими настройками
 */

export class SettingsManager {
  constructor() {
    this.settings = {
      theme: 'dark',
      language: 'ru',
      'font-size': 'medium', // Исправлено для соответствия HTML
      region: 'ru',
      cacheEnabled: true,
      debugMode: false,
      animationsEnabled: true,
      autoOptimize: true,
      experimentalFeatures: false,
      notificationSounds: true // Новая настройка звука уведомлений
    };

    // Временное хранилище для несохраненных изменений
    this.pendingChanges = {};
    this.hasUnsavedChanges = false;

    this.loadSettings();
    // КРИТИЧНО: Применяем настройки сразу после загрузки
    this.applyAllSettings();
  }

  /**
   * Загрузить настройки из localStorage
   */
  loadSettings() {
    Object.keys(this.settings).forEach(key => {
      const saved = localStorage.getItem(key);
      if (saved !== null) {
        // Правильно парсим булевые значения для всех переключателей
        if (typeof this.settings[key] === 'boolean') {
          this.settings[key] = saved === 'true';
        } else {
          this.settings[key] = saved;
        }
      }
    });
    console.log('✅ Settings loaded from localStorage:', this.settings);
  }

  /**
   * Сохранение настроек в localStorage
   */
  saveSettings() {
    Object.keys(this.settings).forEach(key => {
      localStorage.setItem(key, this.settings[key]);
    });
  }

  /**
   * Добавить изменение в ожидающие сохранения
   */
  setPendingChange(key, value) {
    console.log(`📝 Pending change: ${key} = ${value}`);
    this.pendingChanges[key] = value;
    this.hasUnsavedChanges = true;
    this.updateSaveButtonState();
    // Обновляем UI для отображения ожидающего изменения
    this.updateDropdownUI(key, value);
  }

  /**
   * Сохранить настройку (старый метод для совместимости)
   */
  saveSetting(key, value) {
    this.settings[key] = value;
    localStorage.setItem(key, value);
  }

  /**
   * Получить настройку
   */
  getSetting(key) {
    return this.settings[key];
  }

  /**
   * Применить тему (унифицированная версия)
   */
  applyTheme(theme) {
    this.saveSetting('theme', theme);
    document.documentElement.setAttribute('data-theme', theme);
    document.body.setAttribute('data-theme', theme);

    // Мгновенно применяем правильный фон без серого мигания
    if (theme === 'dark') {
      document.body.classList.add('gray-bg');
      // Устанавливаем правильный фон с !important для Settings
      document.body.style.setProperty('background-color', '#2a2155', 'important');
      document.body.style.setProperty('background-image', 'radial-gradient(circle at 30% 20%, rgba(85, 64, 174, 0.5) 0%, transparent 45%), radial-gradient(circle at 80% 80%, rgba(123, 31, 162, 0.4) 0%, transparent 50%)', 'important');
      document.body.style.setProperty('transition', 'none', 'important');
      document.body.style.setProperty('animation', 'none', 'important');
    } else {
      document.body.classList.remove('gray-bg');
      // Сбрасываем inline стили для светлой темы
      document.body.style.backgroundColor = '';
      document.body.style.backgroundImage = '';
    }

    console.log('🎨 Theme applied:', theme);
  }

  /**
   * Применить размер шрифта
   */
  applyFontSize(fontSize) {
    this.saveSetting('fontSize', fontSize);

    const fontSizeMap = {
      small: '14px',
      medium: '16px',
      large: '18px',
    };

    document.documentElement.style.setProperty(
      '--base-font-size', 
      fontSizeMap[fontSize] || '16px'
    );

    console.log('Font size applied:', fontSize);
  }

  /**
   * Применить язык
   */
  applyLanguage(language) {
    this.saveSetting('language', language);
    console.log('Language applied:', language);
    // Здесь будет логика смены языка
  }

  /**
   * Применить настройку звука уведомлений
   */
  applyNotificationSounds(enabled) {
    if (window.notificationManager) {
      window.notificationManager.setSoundEnabled(enabled);
      console.log('Notification sounds applied:', enabled);
    }
  }

  /**
   * Сохранить все ожидающие изменения
   */
  saveAllPendingChanges() {
    console.log('💾 Saving all pending changes...');

    Object.keys(this.pendingChanges).forEach(key => {
      const value = this.pendingChanges[key];
      this.settings[key] = value;
      localStorage.setItem(key, String(value));

      // Применяем изменение
      this.applySettingChange(key, value);
    });

    // Очищаем ожидающие изменения
    this.pendingChanges = {};
    this.hasUnsavedChanges = false;
    this.updateSaveButtonState();

    console.log('✅ All changes saved successfully');

    // Показываем уведомление об успешном сохранении
    if (window.notificationManager) {
      window.notificationManager.success('Настройки сохранены', {
        title: 'Сохранение',
        icon: '💾',
        duration: 2000
      });
    }
  }

  /**
   * Применить конкретное изменение настройки
   */
  applySettingChange(key, value) {
    switch (key) {
      case 'theme':
        document.documentElement.setAttribute('data-theme', value);
        document.body.setAttribute('data-theme', value);
        break;
      case 'font-size':
        this.applyFontSize(value);
        break;
      case 'language':
        document.body.setAttribute('data-language', value);
        break;
      case 'region':
        document.body.setAttribute('data-region', value);
        break;
      case 'notificationSounds':
        this.applyNotificationSounds(value);
        break;
      case 'animationsEnabled':
        if (value) {
          document.body.classList.add('animations-enabled');
        } else {
          document.body.classList.remove('animations-enabled');
        }
        break;
      case 'cacheEnabled':
      case 'debugMode':
      case 'autoOptimize':
      case 'experimentalFeatures':
        // Применяем настройки переключателей
        this.applyToggleSetting(key, value);
        break;
      default:
        console.log(`Applied setting: ${key} = ${value}`);
    }
  }

  /**
   * Обновить состояние кнопки сохранения
   */
  updateSaveButtonState() {
    const saveButton = document.querySelector('#save-settings-btn');
    if (saveButton) {
      if (this.hasUnsavedChanges) {
        saveButton.disabled = false;
        saveButton.classList.add('has-changes');
        saveButton.textContent = 'Сохранить изменения *';
      } else {
        saveButton.disabled = true;
        saveButton.classList.remove('has-changes');
        saveButton.textContent = 'Сохранить изменения';
      }
    }
  }

  /**
   * Обновить UI dropdown для отображения ожидающего изменения
   */
  updateDropdownUI(key, value) {
    const dropdown = document.querySelector(`[data-dropdown="${key}"]`);
    if (!dropdown) return;

    const selected = dropdown.querySelector('.dropdown-selected');
    const option = dropdown.querySelector(`[data-value="${value}"]`);

    if (option && selected) {
      // Обновляем отображаемый текст
      const textElement = selected.querySelector('.dropdown-text');
      if (textElement) {
        textElement.textContent = option.textContent.replace('✓', '').trim();
      }

      // Обновляем data-value
      selected.dataset.value = value;

      // Обновляем выделение опций
      const optionElements = dropdown.querySelectorAll('.dropdown-option');
      optionElements.forEach(opt => {
        const isSelected = opt.dataset.value === value;
        opt.classList.toggle('selected', isSelected);
      });

      console.log(`🔄 Updated dropdown UI for ${key} = ${value}`);
    }
  }

  /**
   * Применить регион
   */
  applyRegion(region) {
    this.saveSetting('region', region);
    console.log('✅ Region applied:', region);
    // Здесь будет логика смены региона
  }

  /**
   * Очистить кэш
   */
  async clearCache() {
    if (!confirm('Вы уверены, что хотите очистить весь кэш приложения?')) {
      return;
    }

    try {
      // Очистка localStorage и sessionStorage
      localStorage.clear();
      sessionStorage.clear();

      // Очистка кэша браузера
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
      }

      alert('Кэш успешно очищен. Страница будет перезагружена.');
      location.reload();
    } catch (error) {
      console.error('Error clearing cache:', error);
      alert('Ошибка при очистке кэша');
    }
  }

  /**
   * Инициализация дропдаунов настроек
   */
  initializeDropdowns() {
    const dropdowns = document.querySelectorAll('.custom-dropdown');
    console.log('🔔 initializeDropdowns called, found dropdowns:', dropdowns.length);

    dropdowns.forEach(dropdown => {
      // Проверяем, был ли dropdown уже инициализирован
      if (dropdown.dataset.initialized === 'true') {
        console.log('🔔 Dropdown already initialized, skipping:', dropdown.dataset.dropdown);
        return;
      }

      console.log('🔔 Initializing dropdown:', dropdown.dataset.dropdown);
      const selected = dropdown.querySelector('.dropdown-selected');
      const optionElements = dropdown.querySelectorAll('.dropdown-option');

      // Устанавливаем текущее значение ТОЛЬКО при первой инициализации
      const dropdownType = dropdown.dataset.dropdown;
      const currentValue = this.getSetting(dropdownType);
      const defaultValue = selected?.dataset.value; // Значение из HTML
      const pendingValue = this.pendingChanges[dropdownType]; // Проверяем ожидающие изменения

      // Приоритет: ожидающие изменения > сохраненное значение > значение по умолчанию из HTML
      const valueToUse = pendingValue || currentValue || defaultValue;

      if (valueToUse) {
        const option = dropdown.querySelector(`[data-value="${valueToUse}"]`);
        if (option && selected) {
          selected.dataset.value = valueToUse;
          const textElement = selected.querySelector('.dropdown-text');
          if (textElement) {
            textElement.textContent = option.textContent.replace('✓', '').trim();
          }

          optionElements.forEach(opt => {
            const isSelected = opt.dataset.value === valueToUse;
            opt.classList.toggle('selected', isSelected);
          });
        }
      }

      // Помечаем как инициализированный
      dropdown.dataset.initialized = 'true';

      // Обработчик клика по выбранному элементу
      selected?.addEventListener('click', e => {
        console.log('🔔 Dropdown clicked:', dropdown.dataset.dropdown);
        e.stopPropagation();

        // Закрываем другие дропдауны
        dropdowns.forEach(otherDropdown => {
          if (otherDropdown !== dropdown) {
            otherDropdown.classList.remove('active');
            otherDropdown.querySelector('.dropdown-selected')?.classList.remove('active');
            const otherOptions = otherDropdown.querySelector('.dropdown-options');
            if (otherOptions) {
              otherOptions.classList.remove('open');
            }
          }
        });

        const isActive = dropdown.classList.contains('active');

        // Закрываем все другие dropdown'ы перед открытием нового
        this.closeAllDropdowns();

        // Получаем элементы ДО проверки состояния
        const options = dropdown.querySelector('.dropdown-options');
        const icon = dropdown.querySelector('.dropdown-icon');

        if (!isActive) {
          // Открываем только если не был активен
          dropdown.classList.add('active');
          selected.classList.add('active');

          if (options) {
            // ПОЛНОСТЬЮ скрываем оригинальный dropdown
            options.style.display = 'none';
            options.style.visibility = 'hidden';
            options.style.opacity = '0';
            options.style.pointerEvents = 'none';

            // Создаем ТОЛЬКО портал
            this.createDropdownPortal(selected, options);
          }

          if (icon) {
            icon.style.transform = 'rotate(180deg)';
          }
        }
      });

      // Обработчики кликов по опциям
      optionElements.forEach((option, index) => {
        option.addEventListener('click', e => {
          e.preventDefault();
          e.stopPropagation();

          const value = option.dataset.value;
          const text = option.textContent.replace('✓', '').trim();
          const dropdownType = dropdown.dataset.dropdown;

          if (selected) {
            selected.dataset.value = value;
            const textElement = selected.querySelector('.dropdown-text');
            if (textElement) {
              textElement.textContent = text;
            }
          }

          optionElements.forEach(opt => opt.classList.remove('selected'));
          option.classList.add('selected');

          dropdown.classList.remove('active');
          selected?.classList.remove('active');

          const options = dropdown.querySelector('.dropdown-options');
          const icon = dropdown.querySelector('.dropdown-icon');
          if (options) {
            options.classList.remove('open');
          }
          if (icon) {
            icon.style.transform = 'rotate(0deg)';
          }

          this.removeOverlay();
          this.handleDropdownChange(dropdownType, value);
        });

        // Добавляем обработчики для лучшей отзывчивости
        option.addEventListener('mousedown', e => {
          e.preventDefault();
        });

        option.addEventListener('mouseup', e => {
          e.preventDefault();
        });
      });
    });

    // Закрытие дропдаунов при клике вне их (теперь обрабатывается через overlay)

    // Закрытие дропдаунов при изменении размера окна
    window.addEventListener('resize', () => {
      this.closeAllDropdowns();
    });
  }

  /**
   * Создание портала для dropdown с правильным позиционированием
   */
  createDropdownPortal(button, originalOptions) {
    console.log('🚀 Creating dropdown portal');
    this.closeDropdownPortal(); // Убираем существующий портал

    const buttonRect = button.getBoundingClientRect();
    const portal = document.createElement('div');
    portal.className = 'dropdown-options dropdown-portal';
    portal.id = 'dropdown-portal';

    console.log(`📍 Portal position: left=${buttonRect.left}px, top=${buttonRect.bottom + 8}px, width=${buttonRect.width}px`);

    // Копируем содержимое оригинального dropdown
    portal.innerHTML = originalOptions.innerHTML;

    // Позиционируем портал через CSS переменные для максимальной стабильности
    document.documentElement.style.setProperty('--dropdown-top', `${buttonRect.bottom + 8}px`);
    document.documentElement.style.setProperty('--dropdown-left', `${buttonRect.left}px`);
    document.documentElement.style.setProperty('--dropdown-width', `${buttonRect.width}px`);

    // Дополнительная фиксация через inline стили
    portal.style.position = 'fixed';
    portal.style.zIndex = '999999';
    portal.style.pointerEvents = 'auto';

    // КРИТИЧНО: блокируем любые изменения позиции
    portal.style.transform = 'translateZ(0)'; // Создаем новый слой композитинга
    portal.style.willChange = 'auto'; // Отключаем оптимизации браузера

    document.body.appendChild(portal);

    // КРИТИЧНО: Добавляем обработчик скролла СРАЗУ после создания портала
    let isScrolling = false;
    const preventScrollMovement = () => {
      if (!isScrolling) {
        isScrolling = true;
        // Мгновенно закрываем dropdown при первом признаке скролла
        requestAnimationFrame(() => {
          this.closeAllDropdowns();
          isScrolling = false;
        });
      }
    };

    // Устанавливаем обработчики на самом высоком приоритете
    document.addEventListener('scroll', preventScrollMovement, { passive: true, capture: true });
    window.addEventListener('scroll', preventScrollMovement, { passive: true, capture: true });

    // Сохраняем для очистки
    portal._preventScrollMovement = preventScrollMovement;

    // Прямое назначение событий на каждый элемент
    const options = portal.querySelectorAll('.dropdown-option');

    // Восстанавливаем выделение из ТЕКУЩЕГО состояния UI (не перезаписываем!)
    const dropdown = button.closest('.custom-dropdown');
    const dropdownType = dropdown.dataset.dropdown;
    const currentUIValue = button.dataset.value; // Берем значение из UI
    const pendingValue = this.pendingChanges[dropdownType];
    const valueToUse = pendingValue || currentUIValue; // Приоритет ожидающим изменениям

    // НЕ обновляем кнопку - оставляем как есть в UI!

    options.forEach((option, index) => {
      // Восстанавливаем выделение из текущего состояния UI
      const isSelected = option.dataset.value === valueToUse;
      if (isSelected) {
        option.classList.add('selected');
      } else {
        option.classList.remove('selected');
      }

      // Hover эффекты убраны

      // Дополнительные события для отзывчивости
      option.addEventListener('mousedown', function(e) {
        e.preventDefault();
      });

      option.addEventListener('mouseup', function(e) {
        e.preventDefault();
      });

      // Click события
      const settingsManagerInstance = this; // Сохраняем ссылку на SettingsManager
      option.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const value = this.dataset.value;
        const text = this.textContent.trim();

        console.log(`🎯 Dropdown option clicked: ${value} = ${text}`);

        // Обновляем оригинальный dropdown
        const dropdown = button.closest('.custom-dropdown');
        const selectedText = dropdown.querySelector('.dropdown-text');
        const selectedElement = dropdown.querySelector('.dropdown-selected');

        if (selectedText) selectedText.textContent = text;
        if (selectedElement) selectedElement.dataset.value = value;

        // Обновляем выделение в портале
        const allPortalOptions = portal.querySelectorAll('.dropdown-option');
        allPortalOptions.forEach(opt => opt.classList.remove('selected'));
        this.classList.add('selected');

        // Обновляем выделение в оригинальном dropdown
        const originalOptions = dropdown.querySelectorAll('.dropdown-option');
        originalOptions.forEach(opt => opt.classList.remove('selected'));
        const originalOption = dropdown.querySelector(`[data-value="${value}"]`);
        if (originalOption) {
          originalOption.classList.add('selected');
        }

        // Добавляем в ожидающие изменения
        settingsManagerInstance.setPendingChange(dropdown.dataset.dropdown, value);

        // Показываем уведомление
        console.log(`📝 Настройка "${dropdown.dataset.dropdown}" изменена на "${text}" (ожидает сохранения)`);

        if (window.notificationManager) {
          window.notificationManager.info(`${text} (не сохранено)`, {
            title: 'Изменение ожидает сохранения',
            icon: '📝',
            duration: 2000
          });
        }

        // Закрываем dropdown
        settingsManagerInstance.closeAllDropdowns();
      });
    });

    // Предотвращаем закрытие при клике внутри портала
    portal.addEventListener('click', function(e) {
      e.stopPropagation();
    });

    // МГНОВЕННО закрываем dropdown при любом скролле
    const closeOnScroll = (e) => {
      // НЕМЕДЛЕННО скрываем портал (до рендеринга)
      portal.style.visibility = 'hidden';
      portal.style.opacity = '0';
      portal.style.pointerEvents = 'none';
      // Корректно закрываем все dropdown
      this.closeAllDropdowns();
    };

    // Перехватываем ВСЕ события скролла на самой ранней стадии
    document.addEventListener('scroll', closeOnScroll, { passive: false, capture: true });
    window.addEventListener('scroll', closeOnScroll, { passive: false, capture: true });
    window.addEventListener('wheel', closeOnScroll, { passive: false, capture: true });
    window.addEventListener('touchstart', closeOnScroll, { passive: false, capture: true });
    window.addEventListener('touchmove', closeOnScroll, { passive: false, capture: true });
    window.addEventListener('resize', closeOnScroll, { passive: false, capture: true });

    // Сохраняем ссылки для очистки
    portal._closeOnScroll = closeOnScroll;

    // Добавляем глобальный обработчик для закрытия при клике вне портала
    const globalClickHandler = (e) => {
      if (!portal.contains(e.target) && !button.contains(e.target)) {
        this.closeAllDropdowns();
        document.removeEventListener('click', globalClickHandler);
      }
    };

    // МГНОВЕННО добавляем обработчик БЕЗ ВСЯКИХ ЗАДЕРЖЕК
    document.addEventListener('click', globalClickHandler);

    // МГНОВЕННО показываем портал БЕЗ АНИМАЦИИ
    portal.classList.add('open');
    portal.style.opacity = '1';
    portal.style.visibility = 'visible';
    portal.style.transform = 'translateY(0)';
    portal.style.transition = 'none'; // Отключаем transition для мгновенного появления
  }

  /**
   * Закрытие портала dropdown с очисткой обработчиков
   */
  closeDropdownPortal() {
    const portal = document.getElementById('dropdown-portal');
    if (portal) {
      // Очищаем ВСЕ обработчики скролла
      if (portal._closeOnScroll) {
        document.removeEventListener('scroll', portal._closeOnScroll, { capture: true });
        window.removeEventListener('scroll', portal._closeOnScroll, { capture: true });
        window.removeEventListener('wheel', portal._closeOnScroll, { capture: true });
        window.removeEventListener('touchstart', portal._closeOnScroll, { capture: true });
        window.removeEventListener('touchmove', portal._closeOnScroll, { capture: true });
        window.removeEventListener('resize', portal._closeOnScroll, { capture: true });
      }

      // Очищаем дополнительные обработчики
      if (portal._preventScrollMovement) {
        document.removeEventListener('scroll', portal._preventScrollMovement, { capture: true });
        window.removeEventListener('scroll', portal._preventScrollMovement, { capture: true });
      }

      // Очищаем CSS переменные
      document.documentElement.style.removeProperty('--dropdown-top');
      document.documentElement.style.removeProperty('--dropdown-left');
      document.documentElement.style.removeProperty('--dropdown-width');

      portal.remove();
    }
  }

  /**
   * Создание overlay для блокировки кликов
   */
  createOverlay() {
    this.removeOverlay(); // Убираем существующий overlay

    const overlay = document.createElement('div');
    overlay.className = 'dropdown-overlay';
    overlay.id = 'dropdown-overlay';

    // Закрытие при клике на overlay
    overlay.addEventListener('click', (e) => {
      console.log('🔴 Overlay clicked - closing dropdowns');
      e.preventDefault();
      e.stopPropagation();
      this.closeAllDropdowns();
    });

    // Предотвращаем всплытие событий
    overlay.addEventListener('mousedown', (e) => {
      console.log('🔴 Overlay mousedown');
      e.preventDefault();
      e.stopPropagation();
    });

    document.body.appendChild(overlay);
  }

  /**
   * Удаление overlay
   */
  removeOverlay() {
    const overlay = document.getElementById('dropdown-overlay');
    if (overlay) {
      overlay.remove();
    }
  }

  /**
   * Закрытие всех dropdown'ов
   */
  closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('.custom-dropdown');
    dropdowns.forEach(dropdown => {
      dropdown.classList.remove('active');
      dropdown.querySelector('.dropdown-selected')?.classList.remove('active');
      const options = dropdown.querySelector('.dropdown-options');
      const icon = dropdown.querySelector('.dropdown-icon');
      if (options) {
        options.classList.remove('open');
        // Восстанавливаем оригинальные стили (но они все равно скрыты через CSS)
        options.style.display = '';
        options.style.visibility = '';
        options.style.opacity = '';
        options.style.pointerEvents = '';
      }
      if (icon) {
        icon.style.transform = 'rotate(0deg)';
      }
    });
    this.closeDropdownPortal();
    this.removeOverlay();
  }

  /**
   * Обработка изменений в дропдаунах
   */
  handleDropdownChange(type, value) {
    // Добавляем в ожидающие изменения вместо немедленного сохранения
    this.setPendingChange(type, value);

    // Показываем уведомление о том, что изменение ожидает сохранения
    if (window.notificationManager) {
      const settingName = this.getDropdownDisplayName(type);
      window.notificationManager.info(`${settingName} изменено (не сохранено)`, {
        title: 'Изменение ожидает сохранения',
        icon: '📝',
        duration: 2000
      });
    }
  }

  /**
   * Получить отображаемое имя для дропдауна
   */
  getDropdownDisplayName(type) {
    const names = {
      'theme': 'Тема',
      'language': 'Язык',
      'font-size': 'Размер шрифта',
      'region': 'Регион'
    };
    return names[type] || type;
  }

  // Удалено: дублирующий метод applyTheme - используется основной метод выше

  /**
   * Применение языка
   */
  applyLanguage(language) {
    console.log(`🌍 Applying language: ${language}`);
    document.body.setAttribute('data-language', language);
    localStorage.setItem('language', language);
  }

  /**
   * Применение размера шрифта
   */
  applyFontSize(fontSize) {
    console.log(`📝 Applying font size: ${fontSize}`);
    const sizeMap = {
      'small': '14px',
      'medium': '16px',
      'large': '18px',
      'extra-large': '20px'
    };

    if (sizeMap[fontSize]) {
      document.body.style.fontSize = sizeMap[fontSize];
      localStorage.setItem('fontSize', fontSize);
    }
  }

  /**
   * Применение региона
   */
  applyRegion(region) {
    console.log(`🌎 Applying region: ${region}`);
    document.body.setAttribute('data-region', region);
    localStorage.setItem('region', region);
  }

  /**
   * Показать уведомление пользователю
   */
  showNotification(message) {
    // Используем глобальный NotificationManager
    if (window.notificationManager) {
      window.notificationManager.success(message, {
        title: 'Настройки обновлены',
        icon: '⚙️',
        duration: 3000
      });
    } else {
      // Fallback на простое уведомление
      this.showSimpleNotification(message, 'success');
    }
  }

  /**
   * Инициализация переключателей
   */
  initializeToggles() {
    // Список всех ползунков с их настройками
    const toggles = [
      { id: 'cache-toggle', setting: 'cacheEnabled', defaultValue: true },
      { id: 'debug-toggle', setting: 'debugMode', defaultValue: false },
      { id: 'animations-toggle', setting: 'animationsEnabled', defaultValue: true },
      { id: 'auto-optimize-toggle', setting: 'autoOptimize', defaultValue: true },
      { id: 'experimental-toggle', setting: 'experimentalFeatures', defaultValue: false },
      { id: 'notificationSounds-toggle', setting: 'notificationSounds', defaultValue: true }
    ];

    toggles.forEach(({ id, setting, defaultValue }) => {
      const toggle = document.getElementById(id);

      if (toggle) {
        // Устанавливаем состояние из настроек или значение по умолчанию
        const currentValue = this.getSetting(setting);
        const valueToUse = currentValue !== undefined ? currentValue : defaultValue;

        toggle.checked = valueToUse;

        // Добавляем обработчик изменения
        toggle.addEventListener('change', e => {
          const enabled = e.target.checked;

          // Добавляем в ожидающие изменения вместо немедленного сохранения
          this.setPendingChange(setting, enabled);

          // Показываем уведомление о том, что изменение ожидает сохранения
          if (window.notificationManager) {
            const settingName = this.getToggleDisplayName(setting);
            const status = enabled ? 'включено' : 'выключено';
            window.notificationManager.info(`${settingName} ${status} (не сохранено)`, {
              title: 'Изменение ожидает сохранения',
              icon: '📝',
              duration: 2000
            });
          }
        });

        // Добавляем обработчик клика на контейнер toggle
        const toggleContainer = toggle.closest('.modern-toggle');
        if (toggleContainer) {
          toggleContainer.addEventListener('click', (e) => {
            // Предотвращаем двойное срабатывание
            if (e.target === toggle) return;

            // Программно переключаем checkbox
            toggle.checked = !toggle.checked;

            // Вручную вызываем событие change
            const changeEvent = new Event('change', { bubbles: true });
            toggle.dispatchEvent(changeEvent);
          });
        }

        // Добавляем обработчик клика на slider
        const slider = toggle.nextElementSibling;
        if (slider && slider.classList.contains('modern-toggle-slider')) {
          slider.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Программно переключаем checkbox
            toggle.checked = !toggle.checked;

            // Вручную вызываем событие change
            const changeEvent = new Event('change', { bubbles: true });
            toggle.dispatchEvent(changeEvent);
          });
        }
      }
    });
  }

  /**
   * Получить отображаемое имя настройки
   */
  getToggleDisplayName(setting) {
    const names = {
      cacheEnabled: 'Кэширование данных',
      debugMode: 'Режим разработчика',
      animationsEnabled: 'Анимации',
      autoOptimize: 'Автоматическая оптимизация',
      experimentalFeatures: 'Экспериментальные функции',
      notificationSounds: 'Звук уведомлений'
    };
    return names[setting] || setting;
  }

  /**
   * Применить настройку ползунка
   */
  applyToggleSetting(setting, enabled) {
    switch (setting) {
      case 'cacheEnabled':
        // Логика кэширования
        break;
      case 'debugMode':
        // Логика режима разработчика
        break;
      case 'animationsEnabled':
        if (enabled) {
          document.body.classList.add('animations-enabled');
        } else {
          document.body.classList.remove('animations-enabled');
        }
        break;
      case 'autoOptimize':
        // Логика автоматической оптимизации
        break;
      case 'experimentalFeatures':
        // Логика экспериментальных функций
        break;
      case 'notificationSounds':
        if (window.notificationManager) {
          window.notificationManager.setSoundEnabled(enabled);
          console.log(`🔊 Notification sounds ${enabled ? 'enabled' : 'disabled'}`);
        }
        break;
    }
  }

  /**
   * Инициализация кнопок
   */
  initializeButtons() {
    const clearCacheBtn = document.getElementById('clear-cache-btn');
    if (clearCacheBtn) {
      clearCacheBtn.addEventListener('click', () => this.clearCache());
    }

    // Кнопка сохранения настроек
    const saveBtn = document.getElementById('save-settings-btn');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        this.saveAllPendingChanges();
      });
    }
  }

  /**
   * Применить все сохраненные настройки
   */
  applyAllSettings() {
    console.log('🔄 Applying all settings...');
    Object.keys(this.settings).forEach(key => {
      this.applySettingChange(key, this.settings[key]);
    });
    console.log('✅ All settings applied');
  }

  /**
   * Мгновенное применение критических стилей для предотвращения мигания
   */
  applyCriticalStyles() {
    // Добавляем класс для мгновенного применения стилей Settings
    document.body.classList.add('settings-styles-ready');

    // Мгновенно применяем правильный фон для предотвращения серого мигания
    const currentTheme = this.getSetting('theme') || 'dark';
    if (currentTheme === 'dark') {
      // Применяем стили более агрессивно для Settings
      document.body.style.setProperty('background-color', '#2a2155', 'important');
      document.body.style.setProperty('background-image', 'radial-gradient(circle at 30% 20%, rgba(85, 64, 174, 0.5) 0%, transparent 45%), radial-gradient(circle at 80% 80%, rgba(123, 31, 162, 0.4) 0%, transparent 50%)', 'important');
      document.body.style.setProperty('transition', 'none', 'important');
      document.body.style.setProperty('animation', 'none', 'important');
      document.body.classList.add('gray-bg');

      // Также применяем к app-container для гарантии
      const appContainer = document.querySelector('.app-container');
      if (appContainer) {
        appContainer.style.setProperty('transition', 'none', 'important');
        appContainer.style.setProperty('animation', 'none', 'important');
        appContainer.style.setProperty('background-color', 'transparent', 'important');
      }

      // Применяем к settings секции
      const settingsSection = document.querySelector('#settings-section');
      if (settingsSection) {
        settingsSection.style.setProperty('background-color', 'transparent', 'important');
      }
    }

    // Применяем критические стили через CSS переменные
    document.documentElement.style.setProperty('--settings-bg-primary', 'linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(51, 65, 85, 0.4) 100%)');
    document.documentElement.style.setProperty('--settings-border-primary', '1px solid rgba(139, 92, 246, 0.15)');
    document.documentElement.style.setProperty('--settings-item-bg', 'rgba(139, 92, 246, 0.05)');
  }

  /**
   * Простое уведомление без внешних зависимостей (fallback)
   */
  showSimpleNotification(message, type = 'info') {
    console.log('🔔 showSimpleNotification called:', message, type);

    // Находим или создаем контейнер для уведомлений
    let container = document.getElementById('simple-notifications-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'simple-notifications-container';
      container.style.cssText = `
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 999999;
        display: flex;
        flex-direction: column-reverse;
        gap: 0.75rem;
        pointer-events: none;
        max-width: 400px;
      `;
      document.body.appendChild(container);
    }

    // Создаем уведомление
    const notification = document.createElement('div');
    notification.className = `simple-notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      background: rgba(16, 185, 129, 0.95);
      color: white;
      padding: 1rem 1.25rem;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(16, 185, 129, 0.2), 0 8px 16px rgba(0, 0, 0, 0.3);
      font-size: 14px;
      font-weight: 500;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      transform: translateX(100%);
      opacity: 0;
      pointer-events: auto;
      cursor: pointer;
    `;

    container.appendChild(notification);

    // Анимация появления
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
      notification.style.opacity = '1';
    }, 10);

    // Клик для закрытия
    notification.addEventListener('click', () => {
      this.removeSimpleNotification(notification);
    });

    // Автоматическое скрытие
    setTimeout(() => {
      this.removeSimpleNotification(notification);
    }, 3000);
  }

  removeSimpleNotification(notification) {
    if (!notification || !notification.parentElement) return;

    notification.style.transform = 'translateX(120%)';
    notification.style.opacity = '0';

    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 400);
  }

  /**
   * Инициализация элементов "Coming Soon"
   */
  initializeComingSoonElements() {
    const comingSoonElements = document.querySelectorAll('.coming-soon');
    console.log('🔔 initializeComingSoonElements called, found elements:', comingSoonElements.length);

    comingSoonElements.forEach(element => {
      element.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        console.log('🔔 Coming Soon element clicked');

        // Показываем уведомление
        if (window.notificationManager) {
          console.log('🔔 Showing coming soon notification via NotificationManager');
          window.notificationManager.info('Эта функция скоро будет доступна!', {
            title: 'Coming Soon',
            icon: '🚀',
            duration: 3000
          });
        } else {
          console.log('🔔 Showing coming soon notification via fallback');
          this.showSimpleNotification('Эта функция скоро будет доступна!', 'info');
        }
      });
    });
  }
}

// Создаем глобальный экземпляр
export const settingsManager = new SettingsManager();

/**
 * Инициализация обработчиков настроек
 */
export function initializeSettingsHandlers() {
  settingsManager.initializeDropdowns();
  settingsManager.initializeToggles();
  settingsManager.initializeButtons();
  settingsManager.initializeComingSoonElements();
  settingsManager.applyAllSettings();

  console.log('✅ Settings handlers initialized');
}

/**
 * Инициализация менеджера настроек (для UltraAppInitializer)
 */
export function initializeSettingsManager() {
  return initializeSettingsHandlers();
}

/**
 * Обработчики для совместимости с существующим кодом
 */
export function handleThemeChange(theme) {
  settingsManager.applyTheme(theme);
}

export function handleLanguageChange(language) {
  settingsManager.applyLanguage(language);
}

export function handleFontSizeChange(fontSize) {
  settingsManager.applyFontSize(fontSize);
}
