/**
 * UNIFIED ANIMATIONS
 * Consolidated keyframe animations for the entire application
 */

/* ===== BASIC ANIMATIONS ===== */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ===== INTERACTION ANIMATIONS ===== */

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* ===== SLIDE ANIMATIONS ===== */

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes dropdownSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== SPECIALIZED ANIMATIONS ===== */

@keyframes glow {
  0% { filter: brightness(1); }
  100% { filter: brightness(1.2); }
}

@keyframes progressPulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

@keyframes progressShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes shimmerCard {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

@keyframes badgeShine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

/* ===== UTILITY CLASSES ===== */

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInScale {
  animation: fadeInScale 0.8s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 2s ease-in-out infinite;
}

.animate-slideInRight {
  animation: slideInRight 0.4s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.4s ease-out;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

@media (prefers-reduced-motion: reduce) {
  html *,
  html *::before,
  html *::after {
    animation-duration: 0.01ms;
    animation-iteration-count: 1;
    transition-duration: 0.01ms;
    scroll-behavior: auto;
  }
}

/* GPU acceleration for animations */
.animate-fadeInUp,
.animate-fadeInScale,
.animate-float,
.animate-pulse,
.animate-shimmer,
.animate-spin,
.animate-bounce,
.animate-slideInRight,
.animate-slideInLeft {
  will-change: transform, opacity;
  transform: translateZ(0);
}
