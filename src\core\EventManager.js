/**
 * Централизованный менеджер событий
 * Отвечает за обработку всех пользовательских взаимодействий
 */

import { state } from '../store/state.js';
// URLStateService удален - функциональность перенесена в FilterManager
// RenderCoordinator удален - используем прямые вызовы
// Простые утилиты
const getCachedElement = (selector) => document.querySelector(selector);
const getDebounced = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};
import { updateFilterSelection } from '../components/tank-list/index.js';
import {
  RUSSIAN_TO_INTERNAL_TYPE_MAP,
  ENGLISH_TO_INTERNAL_TYPE_MAP,
} from '../utils/constants.js';

/**
 * ОПТИМИЗИРОВАННЫЙ EVENT MANAGER
 * Упрощенная архитектура для максимальной производительности
 */
export class EventManager {
  constructor() {
    this.isInitialized = false;
    this.lastFilterTime = 0;
    this.filterDebounceDelay = 0; // МГНОВЕННЫЕ ФИЛЬТРЫ
    this.eventHandlers = new Map();

    // Дебаунсинг для критических операций
    this.debouncedOperations = new Map();
    this.setupDebouncedOperations();

    // Система очистки памяти
    this.cleanupTasks = [];

    // Кэш для обработчиков событий
    this.handlerCache = new Map();

    // Метрики производительности
    this.performanceMetrics = {
      ...this.performanceMetrics,
      filterOperations: 0,
      averageFilterTime: 0,
      lastOptimization: 0
    };
  }

  /**
   * НАСТРОЙКА ДЕБАУНСИРОВАННЫХ ОПЕРАЦИЙ
   */
  setupDebouncedOperations() {
    // Дебаунсированная фильтрация
    this.debouncedOperations.set('filter', getDebounced((type, value) => {
      this.performFilterOperation(type, value);
    }, this.filterDebounceDelay));

    // Дебаунсированное обновление URL
    this.debouncedOperations.set('urlUpdate', getDebounced((params) => {
      this.updateURL(params);
    }, 100));

    // Дебаунсированная очистка кэша
    this.debouncedOperations.set('cacheCleanup', getDebounced(() => {
      this.performCacheCleanup();
    }, 30000)); // 30 секунд
  }

  /**
   * Инициализация всех обработчиков событий
   */
  initialize() {
    // ЗАЩИТА ОТ МНОЖЕСТВЕННОЙ ИНИЦИАЛИЗАЦИИ
    if (this.isInitialized) {
      return;
    }


    const startTime = performance.now();

    // Инициализируем все обработчики
    this.cacheDOMElements();
    this.setupFilterHandlers();
    this.setupEventDelegation();
    this.setupSearchHandlers();
    this.setupModalHandlers();
    this.setupHashChangeHandler();

    this.isInitialized = true;
    this.performanceMetrics.initTime = performance.now() - startTime;
  }

  /**
   * Кэширование DOM элементов для событий
   */
  cacheDOMElements() {
    // Поле поиска пока не используется в текущем дизайне
    this.searchInput = null;
    this.countryFilters = document.querySelectorAll('#nation-filter .filter-item');
    this.categoryFilters = document.querySelectorAll('#type-filter .filter-item');
  }

  /**
   * Настройка делегирования событий
   */
  setupEventDelegation() {
    // ОТКЛЮЧАЕМ делегированный обработчик кликов по танкам
    // Используем прямые обработчики в UltraPerformanceRenderer

    // Глобальные обработчики
    document.addEventListener('click', this.handleGlobalClick.bind(this));
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
  }

  /**
   * Настройка обработчиков поиска
   */
  setupSearchHandlers() {
    // Поиск пока отключен в текущем дизайне
  }

  /**
   * Настройка обработчиков фильтров
   */
  setupFilterHandlers() {

    if (this.countryFilters) {
      this.countryFilters.forEach((filterItem, index) => {
        // Убираем избыточное логирование для производительности
        // console.log(`🔧 Setting up country filter ${index}: ${filterItem.dataset.country}`);

        // Удаляем старые обработчики если есть
        const oldHandler = filterItem._countryClickHandler;
        if (oldHandler) {
          filterItem.removeEventListener('click', oldHandler);
        }

        // Создаем новый обработчик
        const newHandler = (event) => {
          event.preventDefault();
          event.stopPropagation();
          const countryValue = filterItem.dataset.country;
          if (countryValue) {
            // Обычный клик теперь работает как множественный выбор
            filterItem.classList.toggle('selected');
            this.handleMultipleCountrySelection(countryValue);
          }
        };

        // Сохраняем ссылку на обработчик и добавляем его
        filterItem._countryClickHandler = newHandler;
        filterItem.addEventListener('click', newHandler);
      });
    }

    if (this.categoryFilters) {
      this.categoryFilters.forEach((filterItem, index) => {
        // Убираем избыточное логирование для производительности
        // console.log(`🔧 Setting up category filter ${index}: ${filterItem.dataset.category}`);

        // Удаляем старые обработчики если есть
        const oldHandler = filterItem._categoryClickHandler;
        if (oldHandler) {
          filterItem.removeEventListener('click', oldHandler);
        }

        // Создаем новый обработчик
        const newHandler = (event) => {
          event.preventDefault();
          event.stopPropagation();
          const categoryValue = filterItem.dataset.category;
          if (categoryValue) {
            // Обычный клик теперь работает как множественный выбор
            filterItem.classList.toggle('selected');
            this.handleMultipleCategorySelection(categoryValue);
          }
        };

        // Сохраняем ссылку на обработчик и добавляем его
        filterItem._categoryClickHandler = newHandler;
        filterItem.addEventListener('click', newHandler);
      });
    }


  }

  /**
   * Настройка обработчиков модальных окон
   */
  setupModalHandlers() {
    const modal = getCachedElement('#build-modal');
    const closeModalBtn = getCachedElement('.close-modal');

    if (modal && closeModalBtn) {
      closeModalBtn.addEventListener('click', this.closeModal.bind(this));
    }
  }

  /**
   * Настройка обработчика изменения URL (History API)
   */
  setupHashChangeHandler() {
    // ОТКЛЮЧЕНО: Роутер теперь обрабатывает popstate события
    // Сохраняем ссылку на обработчик для последующей очистки
    // this.popstateHandler = (event) => {
    //   this.handleUrlChange();
    // };

    // ОТКЛЮЧЕНО: Обработчик popstate для кнопок назад/вперед
    // window.addEventListener('popstate', this.popstateHandler);

    // Обработчик при загрузке страницы
    this.handleUrlChange();
  }

  /**
   * БЕЗОПАСНАЯ ОЧИСТКА РЕСУРСОВ - MEMORY LEAK PREVENTION
   */
  cleanup() {
    // Удаляем все event listeners
    if (this.popstateHandler) {
      window.removeEventListener('popstate', this.popstateHandler);
      this.popstateHandler = null;
    }

    // Очищаем обработчики фильтров
    if (this.countryFilters) {
      this.countryFilters.forEach(filterItem => {
        if (filterItem._countryClickHandler) {
          filterItem.removeEventListener('click', filterItem._countryClickHandler);
          filterItem._countryClickHandler = null;
        }
      });
    }

    if (this.categoryFilters) {
      this.categoryFilters.forEach(filterItem => {
        if (filterItem._categoryClickHandler) {
          filterItem.removeEventListener('click', filterItem._categoryClickHandler);
          filterItem._categoryClickHandler = null;
        }
      });
    }

    console.log('🧹 EventManager: All event listeners cleaned up');
  }

  /**
   * Обработка изменения URL
   */
  async handleUrlChange() {
    const pathname = window.location.pathname;

    if (pathname === '/' || pathname === '') {
      // Главная страница (overview)
      import('../utils/ui.js').then(module => {
        module.hideAllSections();
        module.showSection('overview');
      });
    } else if (pathname === '/vehicles') {
      // Вкладка vehicles - восстанавливаем фильтры и показываем список
      await this.restoreFiltersAndShowList();
    } else if (pathname.startsWith('/vehicles/')) {
      // Танк в секции vehicles: /vehicles/tank-name
      const shortId = pathname.substring('/vehicles/'.length);

      // Ждем загрузки данных если нужно
      if (!state.allTanks || !state.allTanks.length) {
        const maxWaitTime = 2000; // 2 секунды (уменьшено)
        const checkInterval = 50;
        let waitTime = 0;

        while ((!state.allTanks || !state.allTanks.length) && waitTime < maxWaitTime) {
          await new Promise(resolve => setTimeout(resolve, checkInterval));
          waitTime += checkInterval;
        }

        if (!state.allTanks || !state.allTanks.length) {
          console.error('❌ Tank data failed to load in handleUrlChange');
          history.replaceState(null, '', '/vehicles');
          await this.restoreFiltersAndShowList();
          return;
        }
      }

      // Находим танк по короткому ID
      const tankName = await this.findTankByShortId(shortId);

      if (tankName) {


        // ВАЖНО: Находим танк и устанавливаем фильтры по его данным
        const { createTankUrlId } = await import('../utils/helpers.js');
        const tank = state.allTanks.find(t => createTankUrlId(t.name) === shortId);

        if (tank) {
          // Устанавливаем фильтры по данным танка (НЕ как пользовательские!)
          state.selectedCountry = tank.country;
          state.selectedCategory = tank.type;
          // ВАЖНО: НЕ устанавливаем countrySelectedManually и categorySelectedManually
          // потому что это автоматические фильтры, а не пользовательские

          // Обновляем визуальное отображение фильтров
          const { updateFilterSelection } = await import('../components/tank-list/index.js');
          const countryForUI = tank.country === 'USSR' ? 'ussr' : tank.country.toLowerCase();
          updateFilterSelection('country', countryForUI);
          updateFilterSelection('category', tank.type);


        }

        // При обновлении страницы НЕ сохраняем фильтры заново
        import('../components/TankCharacteristics.js').then(module => {
          module.showTankCharacteristics(tankName, false); // false = не сохранять фильтры при обновлении
        });
      } else {
        console.warn('⚠️ Tank not found for shortId in URL:', shortId);
        // Если танк не найден, перенаправляем на vehicles
        history.replaceState(null, '', '/vehicles');
        await this.restoreFiltersAndShowList();
      }
    } else {
      // Другие вкладки (compare, settings и т.д.)
      const section = pathname.substring(1); // убираем ведущий слеш
      import('../utils/ui.js').then(module => {
        module.hideAllSections();
        module.showSection(section);
      });
    }
  }

  /**
   * Восстановление фильтров и показ списка (для браузер "Назад")
   */
  async restoreFiltersAndShowList() {
    // УСИЛЕННАЯ ЗАЩИТА ОТ ДУБЛИРУЮЩИХ ВЫЗОВОВ
    const now = Date.now();
    if (this.isRestoringFilters || (now - this.lastRestoreTime) < 200) {
      return;
    }

    this.isRestoringFilters = true;
    this.lastRestoreTime = now;



    try {
      // Импортируем необходимые модули
      const [stateModule, tankListModule, uiModule, tankManagerModule] = await Promise.all([
        import('../store/state.js'),
        import('../components/tank-list/index.js'),
        import('../utils/ui.js'),
        import('../core/TankManager.js')
      ]);

      const { state } = stateModule;
      const { updateFilterSelection } = tankListModule;

      // ВАЖНО: Переключаемся на вкладку vehicles
      state.currentMenuName = 'vehicles';
      localStorage.setItem('activeMenuItem', 'vehicles');

      // НЕ СКРЫВАЕМ СЕКЦИИ - просто обеспечиваем что vehicles видна
      // ПРИНУДИТЕЛЬНО ПОКАЗЫВАЕМ СЕКЦИЮ VEHICLES
      const vehiclesSection = document.getElementById('vehicles-section');
      if (vehiclesSection) {
        vehiclesSection.style.display = 'grid';
        vehiclesSection.style.visibility = 'visible';
        vehiclesSection.style.opacity = '1';
        vehiclesSection.classList.remove('hidden');
        vehiclesSection.classList.add('section-visible');
      }

      // Сбрасываем состояние танка
      state.selectedTank = null;
      localStorage.removeItem('selectedTank');

      // Убираем атрибут страницы танка
      document.body.removeAttribute('data-tank-page');

      // Устанавливаем активную вкладку vehicles
      state.currentMenuName = 'vehicles';
      localStorage.setItem('activeMenuItem', 'vehicles');

      // Обновляем активную вкладку в UI
      this.updateActiveMenuItem('vehicles');

      // 🧠 QUANTUM FILTER RESTORATION LOGIC - HARVARD LEVEL
      // ПРИОРИТЕТ 1: URL параметры (пользователь обновил страницу с фильтрами)
      const urlParams = new URLSearchParams(window.location.search);
      const urlCountry = urlParams.get('country');
      const urlCategory = urlParams.get('category');
      const urlSearch = urlParams.get('search');

      // ПРИОРИТЕТ 2: Сохраненные фильтры из localStorage
      const savedFilters = localStorage.getItem('filtersBeforeSelection');

      if (urlCountry || urlCategory || urlSearch) {
        // 🎯 URL ПАРАМЕТРЫ НАЙДЕНЫ - ВОССТАНАВЛИВАЕМ ИЗ URL
        console.log('🔄 QUANTUM: Восстанавливаем фильтры из URL:', { urlCountry, urlCategory, urlSearch });

        state.selectedCountry = urlCountry || 'all';
        state.selectedCategory = decodeURIComponent(urlCategory || 'all');
        state.searchQuery = urlSearch || '';
        state.countrySelectedManually = true; // URL означает пользовательский выбор
        state.categorySelectedManually = true;

        // Обновляем визуальное состояние фильтров
        const countryForUI = state.selectedCountry === 'all' ? 'all' :
          (state.selectedCountry === 'USSR' ? 'ussr' : state.selectedCountry.toLowerCase());
        updateFilterSelection('country', countryForUI);
        updateFilterSelection('category', state.selectedCategory);

        console.log('✅ QUANTUM: Фильтры восстановлены из URL - приоритет 1');

      } else if (savedFilters) {
        const filters = JSON.parse(savedFilters);
        console.log('🔄 Восстанавливаем фильтры из localStorage:', filters);

        // Проверяем, выбирал ли пользователь фильтры вручную
        if (filters.userSelectedFilters || filters.countrySelectedManually || filters.categorySelectedManually) {
          // 🎯 ВОССТАНАВЛИВАЕМ ПОЛЬЗОВАТЕЛЬСКИЕ ФИЛЬТРЫ ИЗ LOCALSTORAGE
          state.selectedCountry = filters.selectedCountry || 'all';
          state.selectedCategory = filters.selectedCategory || 'all';
          state.searchQuery = filters.searchQuery || '';
          state.countrySelectedManually = filters.countrySelectedManually || false;
          state.categorySelectedManually = filters.categorySelectedManually || false;

          // Обновляем визуальное состояние фильтров
          const countryForUI = state.selectedCountry === 'all' ? 'all' :
            (state.selectedCountry === 'USSR' ? 'ussr' : state.selectedCountry.toLowerCase());

          // Улучшенное сопоставление категорий
          const categoryMapping = {
            'heavyTank': 'ТТ',
            'mediumTank': 'СТ',
            'lightTank': 'ЛТ',
            'at-spg': 'ПТ-САУ',
            'spg': 'САУ'
          };
          const categoryForUI = state.selectedCategory === 'all' ? 'all' :
            (categoryMapping[state.selectedCategory] || state.selectedCategory);

          updateFilterSelection('country', countryForUI);
          updateFilterSelection('category', categoryForUI);

          // Восстанавливаем поле поиска
          const searchInput = document.querySelector('#search-input');
          if (searchInput) {
            searchInput.value = state.searchQuery;
          }

          console.log('✅ QUANTUM: Пользовательские фильтры восстановлены из localStorage - приоритет 2');
        } else {
          // 🔄 ПОЛЬЗОВАТЕЛЬ НE ВЫБИРАЛ ФИЛЬТРЫ ВРУЧНУЮ - СБРОС К DEFAULTS
          console.log('🔄 QUANTUM: Пользователь не выбирал фильтры, сброс к defaults');
          state.selectedCountry = 'all';
          state.selectedCategory = 'all';
          state.searchQuery = '';
          state.countrySelectedManually = false;
          state.categorySelectedManually = false;
          updateFilterSelection('country', 'all');
          updateFilterSelection('category', 'all');

          const searchInput = document.querySelector('#search-input');
          if (searchInput) {
            searchInput.value = '';
          }
        }

        // НЕ УДАЛЯЕМ сохраненные фильтры при браузер "Назад"
        // Они будут удалены только при кнопке "Назад к списку"

      } else {
        // 🎯 ПРИОРИТЕТ 3: НЕТ URL И НЕТ LOCALSTORAGE - DEFAULTS
        console.log('🔄 QUANTUM: Нет URL параметров и localStorage - используем defaults');
        state.selectedCountry = 'all';
        state.selectedCategory = 'all';
        state.searchQuery = '';
        state.countrySelectedManually = false;
        state.categorySelectedManually = false;
        updateFilterSelection('country', 'all');
        updateFilterSelection('category', 'all');

        // Поле поиска отключено в текущем дизайне
        // const searchInput = document.querySelector('#search-input');
        // if (searchInput) {
        //   searchInput.value = '';
        // }
      }

      // НЕ СКРЫВАЕМ СЕКЦИИ - просто обеспечиваем что vehicles видна
      // ПРИНУДИТЕЛЬНО ПОКАЗЫВАЕМ СЕКЦИЮ VEHICLES
      const restoreVehiclesSection = document.getElementById('vehicles-section');
      if (restoreVehiclesSection) {
        restoreVehiclesSection.style.display = 'grid';
        restoreVehiclesSection.style.visibility = 'visible';
        restoreVehiclesSection.style.opacity = '1';
        restoreVehiclesSection.classList.remove('hidden');
        restoreVehiclesSection.classList.add('section-visible');
      }

      // ВАЖНО: Обновляем активную вкладку в UI
      this.updateActiveMenuItem('vehicles');

      // Скрываем характеристики танка
      const characteristicsContainer = document.getElementById('tank-characteristics-container');
      if (characteristicsContainer) {
        characteristicsContainer.classList.add('hidden');
        characteristicsContainer.style.display = 'none';
        characteristicsContainer.style.opacity = '0';
        characteristicsContainer.style.visibility = 'hidden';
      }

      // Показываем tank-list элемент
      const tankListElement = document.getElementById('tank-list');
      if (tankListElement) {
        tankListElement.classList.remove('hidden');
        tankListElement.style.display = 'grid';
        tankListElement.style.visibility = 'visible';
        tankListElement.style.opacity = '1';
      }

      // Применяем фильтры и обновляем список танков
      tankManagerModule.tankManager.applyFiltersAndRenderTankList();

      // Синхронизируем бейджики сравнения после восстановления фильтров
      if (window.advancedCompareSystem) {
        window.advancedCompareSystem.syncAllBadges();
      }

      console.log('✅ restoreFiltersAndShowList: Фильтры восстановлены, список показан');

    } catch (error) {
      console.error('❌ restoreFiltersAndShowList: Ошибка:', error);

      // Fallback - показываем vehicles с базовыми фильтрами
      try {
        const [stateModule, uiModule, tankManagerModule] = await Promise.all([
          import('../store/state.js'),
          import('../utils/ui.js'),
          import('../core/TankManager.js')
        ]);

        const { state } = stateModule;

        // Переключаемся на vehicles
        state.currentMenuName = 'vehicles';
        localStorage.setItem('activeMenuItem', 'vehicles');

        // НЕ СКРЫВАЕМ СЕКЦИИ - просто обеспечиваем что vehicles видна
        // ПРИНУДИТЕЛЬНО ПОКАЗЫВАЕМ СЕКЦИЮ VEHICLES
        const fallbackVehiclesSection = document.getElementById('vehicles-section');
        if (fallbackVehiclesSection) {
          fallbackVehiclesSection.style.display = 'grid';
          fallbackVehiclesSection.style.visibility = 'visible';
          fallbackVehiclesSection.style.opacity = '1';
          fallbackVehiclesSection.classList.remove('hidden');
          fallbackVehiclesSection.classList.add('section-visible');
        }

        // Обновляем активную вкладку
        this.updateActiveMenuItem('vehicles');

        // Сбрасываем фильтры и показываем список
        state.selectedCountry = 'all';
        state.selectedCategory = 'all';
        state.searchQuery = '';

        tankManagerModule.tankManager.applyFiltersAndRenderTankList();

        // Синхронизируем бейджики сравнения после fallback
        if (window.advancedCompareSystem) {
          window.advancedCompareSystem.syncAllBadges();
        }

        console.log('✅ Fallback: Vehicles показан с базовыми фильтрами');
      } catch (fallbackError) {
        console.error('❌ Fallback также не сработал:', fallbackError);
      }
    } finally {
      // Всегда сбрасываем флаг
      this.isRestoringFilters = false;
    }
  }

  /**
   * Поиск танка по короткому ID
   */
  async findTankByShortId(shortId) {
    try {
      const { state } = await import('../store/state.js');
      const { createTankUrlId } = await import('../utils/helpers.js');

      if (!state.allTanks || !state.allTanks.length) {
        return null;
      }

      // Ищем танк, чей короткий ID совпадает с переданным
      const tank = state.allTanks.find(tank => {
        try {
          const tankShortId = createTankUrlId(tank.name);
          return tankShortId === shortId;
        } catch (error) {
          console.warn('Error creating URL ID for tank:', tank.name, error);
          return false;
        }
      });

      // Return tank if found, null otherwise

      return tank ? tank.name : null;
    } catch (error) {
      console.error('❌ Error in findTankByShortId:', error);
      return null;
    }
  }

  /**
   * Обновление активной вкладки меню
   */
  updateActiveMenuItem(activeMenu) {
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
      item.classList.toggle('active', item.dataset.menu === activeMenu);
    });
  }

  // УДАЛЕН handleTankListClick - теперь используем прямые обработчики в UltraPerformanceRenderer

  /**
   * Глобальный обработчик кликов
   */
  handleGlobalClick(event) {
    // Закрытие модальных окон при клике вне их
    const modal = getCachedElement('#build-modal');
    if (modal && event.target === modal) {
      this.closeModal();
    }

    // Закрытие дропдаунов
    const dropdowns = document.querySelectorAll('.custom-dropdown');
    dropdowns.forEach(dropdown => {
      if (!dropdown.contains(event.target)) {
        dropdown.classList.remove('active');
        dropdown.querySelector('.dropdown-selected')?.classList.remove('active');
      }
    });
  }

  /**
   * Обработка события перед закрытием страницы
   */
  handleBeforeUnload() {
    // Сохраняем состояние приложения
    const appState = {
      activeTab: state.currentMenuName || 'vehicles',
      vehiclesVisible: !getCachedElement('#tank-list')?.classList.contains('hidden'),
      timestamp: Date.now()
    };
    localStorage.setItem('appState', JSON.stringify(appState));
  }

  /**
   * Оптимизированная функция поиска
   */
  handleSearchOptimized(event) {
    const query = event.target.value.trim();

    if (state.searchQuery === query.toLowerCase()) return;

    console.log(`[Search] Optimized search for: "${query}"`);

    const startTime = performance.now();
    state.searchQuery = query.toLowerCase();

    // Применяем фильтры и обновляем список
    import('./TankManager.js').then(module => {
      module.tankManager.applyFiltersAndRenderTankList();
    });

    const endTime = performance.now();
    console.log(`[Search] Search completed in ${(endTime - startTime).toFixed(2)}ms`);
  }

  /**
   * Обработка выбора страны
   */
  handleCountrySelection(countryValue) {

    // Убеждаемся что мы на вкладке vehicles
    if (state.currentMenuName !== 'vehicles') {
      state.currentMenuName = 'vehicles';
      localStorage.setItem('activeMenuItem', 'vehicles');
    }

    // Обновляем URL на vehicles если были в характеристиках танка
    if (state.selectedTank) {
      history.replaceState(null, 'Vehicles', '/vehicles');
    }

    // Делегируем в FilterManager - МГНОВЕННО
    Promise.resolve().then(async () => {
      const { filterManager } = await import('./FilterManager.js');
      filterManager.toggleCountryFilter(countryValue);
      filterManager.applyFilters();
    });

    // Убираем focus с кнопки
    this.removeFocusFromButton(event);
  }

  /**
   * Обработка множественного выбора стран
   */
  handleMultipleCountrySelection(countryValue) {
    // Убеждаемся что мы на вкладке vehicles
    if (state.currentMenuName !== 'vehicles') {
      state.currentMenuName = 'vehicles';
      localStorage.setItem('activeMenuItem', 'vehicles');
    }

    // Обновляем URL на vehicles если были в характеристиках танка
    if (state.selectedTank) {
      history.replaceState(null, 'Vehicles', '/vehicles');
    }

    // Делегируем в FilterManager для множественного выбора
    Promise.resolve().then(async () => {
      const { filterManager } = await import('./FilterManager.js');
      filterManager.toggleMultipleCountryFilter(countryValue);
      filterManager.applyFilters();
    });

    // Убираем focus с кнопки
    this.removeFocusFromButton(event);
  }



  /**
   * Обработка выбора категории
   */
  handleCategorySelection(categoryValue) {

    // Убеждаемся что мы на вкладке vehicles
    if (state.currentMenuName !== 'vehicles') {
      state.currentMenuName = 'vehicles';
      localStorage.setItem('activeMenuItem', 'vehicles');
    }

    // Обновляем URL на vehicles если были в характеристиках танка
    if (state.selectedTank) {
      history.replaceState(null, 'Vehicles', '/vehicles');
    }

    // Делегируем в FilterManager - МГНОВЕННО
    Promise.resolve().then(async () => {
      const { filterManager } = await import('./FilterManager.js');
      filterManager.toggleCategoryFilter(categoryValue);
      filterManager.applyFilters();
    });

    // Убираем focus с кнопки
    this.removeFocusFromButton(event);
  }

  /**
   * Обработка множественного выбора категорий
   */
  handleMultipleCategorySelection(categoryValue) {
    // Убеждаемся что мы на вкладке vehicles
    if (state.currentMenuName !== 'vehicles') {
      state.currentMenuName = 'vehicles';
      localStorage.setItem('activeMenuItem', 'vehicles');
    }

    // Обновляем URL на vehicles если были в характеристиках танка
    if (state.selectedTank) {
      history.replaceState(null, 'Vehicles', '/vehicles');
    }

    // Делегируем в FilterManager для множественного выбора
    Promise.resolve().then(async () => {
      const { filterManager } = await import('./FilterManager.js');
      filterManager.toggleMultipleCategoryFilter(categoryValue);
      filterManager.applyFilters();
    });

    // Убираем focus с кнопки
    this.removeFocusFromButton(event);
  }


  /**
   * Убрать focus с кнопки фильтра
   */
  removeFocusFromButton(event) {
    if (event && event.target) {
      event.target.blur();
      const parentItem = event.target.closest('.flag-item, .type-item');
      if (parentItem) {
        parentItem.blur();
        parentItem.style.outline = 'none';
      }
    }
  }

  /**
   * Применение фильтров с debounce и анимацией
   */
  applyFiltersWithDebounce() {
    const now = Date.now();

    // Проверяем, не слишком ли часто вызываются фильтры
    if (now - this.lastFilterTime < this.filterDebounceDelay) {
      console.log('🚫 Filter debounced - too frequent calls');
      return;
    }

    // КРИТИЧЕСКАЯ ПРОВЕРКА: Если танк выбран, НЕ применяем фильтры к списку
    if (state.selectedTank) {
      console.log('🚫 Tank selected - skipping filter application to prevent list overlap');
      return;
    }

    this.lastFilterTime = now;

    // Добавляем анимацию загрузки для фильтров
    const filterButtons = document.querySelectorAll('.filter-button.active');
    filterButtons.forEach(button => {
      button.classList.add('loading-pulse');
    });

    console.log(`🔥 Applying filters with animation...`);

    // Прямой вызов рендеринга через TankManager (FORCE для фильтров)
    import('./TankManager.js').then(({ tankManager }) => {
      tankManager.applyFiltersAndRenderTankList(false, true).then(() => { // force=true для фильтров
        // Убираем анимацию загрузки после рендеринга
        setTimeout(() => {
          filterButtons.forEach(button => {
            button.classList.remove('loading-pulse');
          });
        }, 300);
      });
    });
  }

  /**
   * Закрытие модального окна
   */
  closeModal() {
    const modal = getCachedElement('#build-modal');
    if (modal) {
      modal.classList.add('hidden');
      modal.setAttribute('aria-hidden', 'true');
    }
  }
}

// Экспортируем единственный экземпляр
export const eventManager = new EventManager();
