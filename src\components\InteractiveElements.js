/**
 * СИСТЕМА ИНТЕРАКТИВНЫХ ЭЛЕМЕНТОВ
 * Всплывающие подсказки, анимации, цветовые выделения
 */

export class InteractiveElements {
  constructor() {
    this.tooltips = new Map();
    this.animations = new Map();
    this.highlights = new Map();
    this.particles = new Map();
    
    // Настройки анимаций
    this.animationSettings = {
      duration: 300,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      stagger: 50,
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    };
    
    // Настройки подсказок
    this.tooltipSettings = {
      delay: 500,
      duration: 200,
      maxWidth: 300,
      offset: 10
    };
    
    this.initializeSystem();
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ СИСТЕМЫ
   */
  initializeSystem() {
    this.createTooltipContainer();
    this.setupGlobalEventListeners();
    this.initializeAnimationObserver();
    this.setupParticleSystem();
    
    console.log('✨ Interactive Elements System initialized');
  }

  /**
   * СОЗДАНИЕ КОНТЕЙНЕРА ДЛЯ ПОДСКАЗОК
   */
  createTooltipContainer() {
    this.tooltipContainer = document.createElement('div');
    this.tooltipContainer.className = 'tooltip-container';
    this.tooltipContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      pointer-events: none;
      z-index: 10000;
      width: 100%;
      height: 100%;
    `;
    document.body.appendChild(this.tooltipContainer);
  }

  /**
   * НАСТРОЙКА ГЛОБАЛЬНЫХ ОБРАБОТЧИКОВ
   */
  setupGlobalEventListeners() {
    // Обработчики для подсказок
    document.addEventListener('mouseenter', this.handleMouseEnter.bind(this), true);
    document.addEventListener('mouseleave', this.handleMouseLeave.bind(this), true);
    document.addEventListener('mousemove', this.handleMouseMove.bind(this), true);
    
    // Обработчики для анимаций
    document.addEventListener('click', this.handleClick.bind(this), true);
    document.addEventListener('focus', this.handleFocus.bind(this), true);
    
    // Обработчики для выделений
    document.addEventListener('mousedown', this.handleMouseDown.bind(this), true);
    document.addEventListener('mouseup', this.handleMouseUp.bind(this), true);
  }

  /**
   * ОБРАБОТКА НАВЕДЕНИЯ МЫШИ
   */
  handleMouseEnter(event) {
    const element = event.target;
    
    // Проверяем наличие атрибута подсказки
    if (element.hasAttribute('data-tooltip')) {
      this.showTooltip(element);
    }
    
    // Добавляем эффект свечения для интерактивных элементов
    if (this.isInteractiveElement(element)) {
      this.addGlowEffect(element);
    }
    
    // Анимация появления для карточек танков
    if (element.classList.contains('tank-item')) {
      this.animateTankCardHover(element, true);
    }
  }

  /**
   * ОБРАБОТКА УХОДА МЫШИ
   */
  handleMouseLeave(event) {
    const element = event.target;
    
    // Скрываем подсказку
    if (element.hasAttribute('data-tooltip')) {
      this.hideTooltip(element);
    }
    
    // Убираем эффект свечения
    if (this.isInteractiveElement(element)) {
      this.removeGlowEffect(element);
    }
    
    // Анимация ухода для карточек танков
    if (element.classList.contains('tank-item')) {
      this.animateTankCardHover(element, false);
    }
  }

  /**
   * ОБРАБОТКА ДВИЖЕНИЯ МЫШИ
   */
  handleMouseMove(event) {
    // Обновляем позицию активных подсказок
    this.updateTooltipPosition(event.clientX, event.clientY);
    
    // Создаем частицы при движении по интерактивным элементам
    if (this.isInteractiveElement(event.target)) {
      this.createMouseParticles(event.clientX, event.clientY);
    }
  }

  /**
   * ОБРАБОТКА КЛИКА
   */
  handleClick(event) {
    const element = event.target;
    
    // Анимация клика
    this.animateClick(element);
    
    // Создаем взрыв частиц при клике
    this.createClickExplosion(event.clientX, event.clientY);
    
    // Специальные анимации для кнопок
    if (element.classList.contains('ultra-button') || element.classList.contains('compare-btn')) {
      this.animateButtonClick(element);
    }
  }

  /**
   * ПОКАЗ ПОДСКАЗКИ
   */
  showTooltip(element) {
    const tooltipText = element.getAttribute('data-tooltip');
    const tooltipType = element.getAttribute('data-tooltip-type') || 'default';
    
    if (!tooltipText) return;
    
    // Создаем подсказку
    const tooltip = document.createElement('div');
    tooltip.className = `interactive-tooltip tooltip-${tooltipType}`;
    tooltip.innerHTML = this.formatTooltipContent(tooltipText, tooltipType);
    
    // Добавляем в контейнер
    this.tooltipContainer.appendChild(tooltip);
    
    // Сохраняем ссылку
    this.tooltips.set(element, tooltip);
    
    // Анимация появления
    setTimeout(() => {
      tooltip.classList.add('visible');
    }, this.tooltipSettings.delay);
  }

  /**
   * СКРЫТИЕ ПОДСКАЗКИ
   */
  hideTooltip(element) {
    const tooltip = this.tooltips.get(element);
    if (!tooltip) return;
    
    tooltip.classList.remove('visible');
    
    setTimeout(() => {
      if (tooltip.parentElement) {
        tooltip.remove();
      }
      this.tooltips.delete(element);
    }, this.tooltipSettings.duration);
  }

  /**
   * ФОРМАТИРОВАНИЕ СОДЕРЖИМОГО ПОДСКАЗКИ
   */
  formatTooltipContent(text, type) {
    switch (type) {
      case 'tank':
        return this.formatTankTooltip(text);
      case 'characteristic':
        return this.formatCharacteristicTooltip(text);
      case 'comparison':
        return this.formatComparisonTooltip(text);
      default:
        return `<div class="tooltip-text">${text}</div>`;
    }
  }

  /**
   * ФОРМАТИРОВАНИЕ ПОДСКАЗКИ ТАНКА
   */
  formatTankTooltip(data) {
    const tankData = JSON.parse(data);
    return `
      <div class="tank-tooltip">
        <div class="tooltip-header">
          <img src="${tankData.icon}" alt="${tankData.name}" class="tooltip-tank-icon">
          <div class="tooltip-tank-info">
            <div class="tooltip-tank-name">${tankData.name}</div>
            <div class="tooltip-tank-meta">${tankData.tier} • ${tankData.type} • ${tankData.country}</div>
          </div>
        </div>
        <div class="tooltip-stats">
          <div class="tooltip-stat">
            <span class="stat-label">Damage:</span>
            <span class="stat-value">${tankData.damage || '—'}</span>
          </div>
          <div class="tooltip-stat">
            <span class="stat-label">Armor:</span>
            <span class="stat-value">${tankData.armor || '—'}</span>
          </div>
          <div class="tooltip-stat">
            <span class="stat-label">Speed:</span>
            <span class="stat-value">${tankData.speed || '—'}</span>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * ФОРМАТИРОВАНИЕ ПОДСКАЗКИ ХАРАКТЕРИСТИКИ
   */
  formatCharacteristicTooltip(data) {
    const charData = JSON.parse(data);
    return `
      <div class="characteristic-tooltip">
        <div class="tooltip-title">${charData.name}</div>
        <div class="tooltip-description">${charData.description}</div>
        ${charData.formula ? `<div class="tooltip-formula">Formula: ${charData.formula}</div>` : ''}
        <div class="tooltip-tips">
          <div class="tooltip-tip">💡 ${charData.tip}</div>
        </div>
      </div>
    `;
  }

  /**
   * ОБНОВЛЕНИЕ ПОЗИЦИИ ПОДСКАЗКИ
   */
  updateTooltipPosition(mouseX, mouseY) {
    this.tooltips.forEach(tooltip => {
      const rect = tooltip.getBoundingClientRect();
      const offset = this.tooltipSettings.offset;
      
      let x = mouseX + offset;
      let y = mouseY + offset;
      
      // Проверяем границы экрана
      if (x + rect.width > window.innerWidth) {
        x = mouseX - rect.width - offset;
      }
      
      if (y + rect.height > window.innerHeight) {
        y = mouseY - rect.height - offset;
      }
      
      tooltip.style.transform = `translate(${x}px, ${y}px)`;
    });
  }

  /**
   * ПРОВЕРКА ИНТЕРАКТИВНОГО ЭЛЕМЕНТА
   */
  isInteractiveElement(element) {
    const interactiveClasses = [
      'tank-item', 'ultra-button', 'compare-btn', 'tank-card',
      'characteristic-value', 'quantum-characteristic-value',
      'value-cell', 'tank-column', 'category-header'
    ];
    
    return interactiveClasses.some(className => element.classList.contains(className)) ||
           element.hasAttribute('data-interactive');
  }

  /**
   * ДОБАВЛЕНИЕ ЭФФЕКТА СВЕЧЕНИЯ
   */
  addGlowEffect(element) {
    element.classList.add('interactive-glow');
    
    // Создаем анимированное свечение
    const glowAnimation = element.animate([
      { boxShadow: '0 0 0 rgba(114, 84, 228, 0)' },
      { boxShadow: '0 0 20px rgba(114, 84, 228, 0.4)' }
    ], {
      duration: this.animationSettings.duration,
      easing: this.animationSettings.easing,
      fill: 'forwards'
    });
    
    this.animations.set(element, glowAnimation);
  }

  /**
   * УДАЛЕНИЕ ЭФФЕКТА СВЕЧЕНИЯ
   */
  removeGlowEffect(element) {
    element.classList.remove('interactive-glow');
    
    const animation = this.animations.get(element);
    if (animation) {
      animation.reverse();
      this.animations.delete(element);
    }
  }

  /**
   * АНИМАЦИЯ НАВЕДЕНИЯ НА КАРТОЧКУ ТАНКА
   */
  animateTankCardHover(element, isEntering) {
    const transforms = isEntering ? 
      { transform: 'translateY(-5px) scale(1.02)', filter: 'brightness(1.1)' } :
      { transform: 'translateY(0) scale(1)', filter: 'brightness(1)' };
    
    element.animate([
      { transform: element.style.transform || 'translateY(0) scale(1)', filter: element.style.filter || 'brightness(1)' },
      transforms
    ], {
      duration: this.animationSettings.duration,
      easing: this.animationSettings.bounce,
      fill: 'forwards'
    });
  }

  /**
   * АНИМАЦИЯ КЛИКА
   */
  animateClick(element) {
    // Создаем эффект пульсации
    const ripple = document.createElement('div');
    ripple.className = 'click-ripple';
    
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    
    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(114, 84, 228, 0.3), transparent);
      pointer-events: none;
      transform: scale(0);
      top: 50%;
      left: 50%;
      margin-top: -${size / 2}px;
      margin-left: -${size / 2}px;
    `;
    
    element.style.position = 'relative';
    element.appendChild(ripple);
    
    // Анимация расширения
    ripple.animate([
      { transform: 'scale(0)', opacity: 1 },
      { transform: 'scale(1)', opacity: 0 }
    ], {
      duration: 600,
      easing: 'ease-out'
    }).onfinish = () => ripple.remove();
  }

  /**
   * АНИМАЦИЯ КНОПКИ
   */
  animateButtonClick(element) {
    // Анимация нажатия
    element.animate([
      { transform: 'scale(1)' },
      { transform: 'scale(0.95)' },
      { transform: 'scale(1)' }
    ], {
      duration: 150,
      easing: 'ease-in-out'
    });
    
    // Добавляем временный класс для дополнительных эффектов
    element.classList.add('button-clicked');
    setTimeout(() => {
      element.classList.remove('button-clicked');
    }, 300);
  }

  /**
   * СОЗДАНИЕ ЧАСТИЦ ПРИ ДВИЖЕНИИ МЫШИ
   */
  createMouseParticles(x, y) {
    if (Math.random() > 0.7) { // Создаем частицы не всегда
      this.createParticle(x, y, {
        size: Math.random() * 3 + 1,
        color: `hsl(${Math.random() * 60 + 240}, 70%, 60%)`,
        life: 1000,
        velocity: { x: (Math.random() - 0.5) * 2, y: -Math.random() * 2 }
      });
    }
  }

  /**
   * СОЗДАНИЕ ВЗРЫВА ЧАСТИЦ ПРИ КЛИКЕ
   */
  createClickExplosion(x, y) {
    for (let i = 0; i < 8; i++) {
      setTimeout(() => {
        this.createParticle(x, y, {
          size: Math.random() * 4 + 2,
          color: `hsl(${Math.random() * 60 + 240}, 80%, 70%)`,
          life: 1500,
          velocity: {
            x: (Math.random() - 0.5) * 8,
            y: (Math.random() - 0.5) * 8
          }
        });
      }, i * 50);
    }
  }

  /**
   * СОЗДАНИЕ ЧАСТИЦЫ
   */
  createParticle(x, y, options) {
    const particle = document.createElement('div');
    particle.className = 'interactive-particle';
    
    particle.style.cssText = `
      position: fixed;
      width: ${options.size}px;
      height: ${options.size}px;
      background: ${options.color};
      border-radius: 50%;
      pointer-events: none;
      z-index: 9999;
      left: ${x}px;
      top: ${y}px;
      transform: translate(-50%, -50%);
    `;
    
    document.body.appendChild(particle);
    
    // Анимация частицы
    const animation = particle.animate([
      { 
        transform: 'translate(-50%, -50%) scale(1)',
        opacity: 1
      },
      { 
        transform: `translate(${-50 + options.velocity.x * 50}%, ${-50 + options.velocity.y * 50}%) scale(0)`,
        opacity: 0
      }
    ], {
      duration: options.life,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    });
    
    animation.onfinish = () => particle.remove();
  }

  /**
   * НАСТРОЙКА НАБЛЮДАТЕЛЯ АНИМАЦИЙ
   */
  initializeAnimationObserver() {
    if (window.IntersectionObserver) {
      this.animationObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.animateElementEntry(entry.target);
          }
        });
      }, {
        threshold: 0.1,
        rootMargin: '50px'
      });
      
      // Наблюдаем за элементами с анимацией
      document.querySelectorAll('[data-animate]').forEach(element => {
        this.animationObserver.observe(element);
      });
    }
  }

  /**
   * АНИМАЦИЯ ПОЯВЛЕНИЯ ЭЛЕМЕНТА
   */
  animateElementEntry(element) {
    const animationType = element.getAttribute('data-animate');
    
    switch (animationType) {
      case 'fade-in':
        this.animateFadeIn(element);
        break;
      case 'slide-up':
        this.animateSlideUp(element);
        break;
      case 'scale-in':
        this.animateScaleIn(element);
        break;
      case 'stagger':
        this.animateStagger(element);
        break;
    }
  }

  /**
   * АНИМАЦИЯ ЗАТУХАНИЯ
   */
  animateFadeIn(element) {
    element.animate([
      { opacity: 0 },
      { opacity: 1 }
    ], {
      duration: this.animationSettings.duration * 2,
      easing: this.animationSettings.easing,
      fill: 'forwards'
    });
  }

  /**
   * АНИМАЦИЯ СКОЛЬЖЕНИЯ ВВЕРХ
   */
  animateSlideUp(element) {
    element.animate([
      { transform: 'translateY(30px)', opacity: 0 },
      { transform: 'translateY(0)', opacity: 1 }
    ], {
      duration: this.animationSettings.duration * 2,
      easing: this.animationSettings.bounce,
      fill: 'forwards'
    });
  }

  /**
   * АНИМАЦИЯ МАСШТАБИРОВАНИЯ
   */
  animateScaleIn(element) {
    element.animate([
      { transform: 'scale(0.8)', opacity: 0 },
      { transform: 'scale(1)', opacity: 1 }
    ], {
      duration: this.animationSettings.duration * 2,
      easing: this.animationSettings.bounce,
      fill: 'forwards'
    });
  }

  /**
   * АНИМАЦИЯ С ЗАДЕРЖКОЙ
   */
  animateStagger(element) {
    const children = element.children;
    Array.from(children).forEach((child, index) => {
      setTimeout(() => {
        child.animate([
          { transform: 'translateY(20px)', opacity: 0 },
          { transform: 'translateY(0)', opacity: 1 }
        ], {
          duration: this.animationSettings.duration,
          easing: this.animationSettings.easing,
          fill: 'forwards'
        });
      }, index * this.animationSettings.stagger);
    });
  }

  /**
   * НАСТРОЙКА СИСТЕМЫ ЧАСТИЦ
   */
  setupParticleSystem() {
    // Создаем фоновые частицы
    this.createBackgroundParticles();
    
    // Обновляем частицы каждую секунду
    setInterval(() => {
      this.updateBackgroundParticles();
    }, 1000);
  }

  /**
   * СОЗДАНИЕ ФОНОВЫХ ЧАСТИЦ
   */
  createBackgroundParticles() {
    for (let i = 0; i < 20; i++) {
      setTimeout(() => {
        this.createBackgroundParticle();
      }, i * 200);
    }
  }

  /**
   * СОЗДАНИЕ ФОНОВОЙ ЧАСТИЦЫ
   */
  createBackgroundParticle() {
    const particle = document.createElement('div');
    particle.className = 'background-particle';
    
    const size = Math.random() * 3 + 1;
    const x = Math.random() * window.innerWidth;
    const y = window.innerHeight + 50;
    
    particle.style.cssText = `
      position: fixed;
      width: ${size}px;
      height: ${size}px;
      background: radial-gradient(circle, rgba(114, 84, 228, 0.3), transparent);
      border-radius: 50%;
      pointer-events: none;
      z-index: 1;
      left: ${x}px;
      top: ${y}px;
    `;
    
    document.body.appendChild(particle);
    
    // Анимация движения вверх
    particle.animate([
      { transform: 'translateY(0) rotate(0deg)', opacity: 0 },
      { transform: 'translateY(-100vh) rotate(360deg)', opacity: 1 }
    ], {
      duration: Math.random() * 10000 + 10000,
      easing: 'linear'
    }).onfinish = () => particle.remove();
  }

  /**
   * ОБНОВЛЕНИЕ ФОНОВЫХ ЧАСТИЦ
   */
  updateBackgroundParticles() {
    if (Math.random() > 0.7) {
      this.createBackgroundParticle();
    }
  }

  /**
   * ДОБАВЛЕНИЕ ПОДСКАЗКИ К ЭЛЕМЕНТУ
   */
  addTooltip(element, content, type = 'default') {
    element.setAttribute('data-tooltip', content);
    element.setAttribute('data-tooltip-type', type);
  }

  /**
   * ДОБАВЛЕНИЕ АНИМАЦИИ К ЭЛЕМЕНТУ
   */
  addAnimation(element, type) {
    element.setAttribute('data-animate', type);
    if (this.animationObserver) {
      this.animationObserver.observe(element);
    }
  }

  /**
   * ВЫДЕЛЕНИЕ ЛУЧШИХ/ХУДШИХ ЗНАЧЕНИЙ
   */
  highlightValues(container, values, type = 'best') {
    const cells = container.querySelectorAll('.value-cell, .quantum-characteristic-value, .comparison-value');
    
    cells.forEach(cell => {
      const value = parseFloat(cell.textContent);
      if (isNaN(value)) return;
      
      const isHighlight = type === 'best' ? 
        value === Math.max(...values) : 
        value === Math.min(...values);
      
      if (isHighlight) {
        cell.classList.add(`highlight-${type}`);
        this.addPulseAnimation(cell);
      }
    });
  }

  /**
   * ДОБАВЛЕНИЕ АНИМАЦИИ ПУЛЬСАЦИИ
   */
  addPulseAnimation(element) {
    element.animate([
      { transform: 'scale(1)' },
      { transform: 'scale(1.05)' },
      { transform: 'scale(1)' }
    ], {
      duration: 1000,
      iterations: 3,
      easing: 'ease-in-out'
    });
  }

  /**
   * ОЧИСТКА ВСЕХ ЭФФЕКТОВ
   */
  cleanup() {
    // Очищаем подсказки
    this.tooltips.clear();
    if (this.tooltipContainer) {
      this.tooltipContainer.remove();
    }
    
    // Останавливаем анимации
    this.animations.forEach(animation => animation.cancel());
    this.animations.clear();
    
    // Отключаем наблюдатель
    if (this.animationObserver) {
      this.animationObserver.disconnect();
    }
    
    // Удаляем частицы
    document.querySelectorAll('.interactive-particle, .background-particle').forEach(particle => {
      particle.remove();
    });
  }
}

// Создаем глобальный экземпляр
export const interactiveElements = new InteractiveElements();
