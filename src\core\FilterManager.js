/**
 * ОПТИМИЗИРОВАННЫЙ МЕНЕДЖЕР ФИЛЬТРОВ
 * Эффективная система фильтрации с дебаунсингом и кэшированием
 */

import { state } from '../store/state.js';
import { PERFORMANCE_THRESHOLDS } from '../utils/performance-constants.js';
// Заменяем на UltraSearchEngine
let ultraSearchEngine = null;

// Ленивая загрузка UltraSearchEngine
async function getUltraSearchEngine() {
  if (!ultraSearchEngine) {
    try {
      const { ultraSearchEngine: engine } = await import('../utils/UltraSearchEngine.js');
      ultraSearchEngine = engine;
    } catch (error) {
      console.warn('UltraSearchEngine not available, using fallback');
      // Создаем простую заглушку
      ultraSearchEngine = {
        search: () => [],
        filter: () => [],
        buildIndex: () => {}
      };
    }
  }
  return ultraSearchEngine;
}
import { domCache } from '../utils/CacheMigrationAdapter.js';

class FilterManager {
  constructor() {
    this.isUpdating = false;
    this.currentOperationKey = null;
    this.debounceTimeout = null;
    this.updateQueue = [];
    this.lastUpdateTime = 0;

    // Используем унифицированную систему кэширования DOM

    // Метрики производительности
    this.metrics = {
      filterOperations: 0,
      cacheHits: 0,
      averageUpdateTime: 0
    };

    // Инициализация поискового индекса
    this.searchIndexReady = false;
    this.initializeSearchIndex();
  }

  /**
   * ИНИЦИАЛИЗАЦИЯ ОПТИМИЗИРОВАННОГО ПОИСКОВОГО ИНДЕКСА
   */
  async initializeSearchIndex() {
    try {
      const searchEngine = await getUltraSearchEngine();

      // Ждем загрузки данных танков
      if (state.allTanks && state.allTanks.length > 0) {
        searchEngine.buildIndex(state.allTanks);
        this.searchIndexReady = true;
        if (window.DEBUG_MODE) console.log('🔍 Ultra search index initialized');
      } else {
        // Подписываемся на изменения данных танков
        const unsubscribe = state.subscribe('allTanks', async (tanks) => {
          if (tanks && tanks.length > 0) {
            const searchEngine = await getUltraSearchEngine();
            searchEngine.buildIndex(tanks);
            this.searchIndexReady = true;
            console.log('🔍 Ultra search index built for', tanks.length, 'tanks');
            unsubscribe();
          }
        });
      }
    } catch (error) {
      console.error('Error initializing search index:', error);
    }
  }

  /**
   * Получить текущее состояние фильтров
   */
  getCurrentFilters() {
    return {
      country: state.selectedCountry || 'all',
      category: state.selectedCategory || 'all',
      countries: state.selectedCountries || [],
      categories: state.selectedCategories || [],
      search: state.searchQuery || ''
    };
  }

  /**
   * Проверить активен ли фильтр
   */
  isFilterActive(type, value) {
    const current = this.getCurrentFilters();
    return current[type] === value && current[type] !== 'all';
  }

  /**
   * Переключить фильтр страны - МГНОВЕННАЯ ВЕРСИЯ
   */
  toggleCountryFilter(countryValue) {
    if (this.isFilterActive('country', countryValue)) {
      this.setCountryFilterInstant('all');
    } else {
      this.setCountryFilterInstant(countryValue);
    }
  }

  /**
   * Переключить множественный выбор страны
   */
  toggleMultipleCountryFilter(countryValue) {
    if (this.isUpdating) return;

    // Предотвращаем повторные обновления
    this.isUpdating = true;

    const currentCountries = state.selectedCountries || [];
    let newCountries;

    if (countryValue === 'all') {
      // Если выбрали "все", очищаем массив
      newCountries = [];
    } else {
      // Переключаем страну в массиве
      if (currentCountries.includes(countryValue)) {
        newCountries = currentCountries.filter(c => c !== countryValue);
        console.log(`🔄 Убрали страну ${countryValue}, осталось:`, newCountries);
      } else {
        newCountries = [...currentCountries, countryValue];
        console.log(`✅ Добавили страну ${countryValue}, теперь:`, newCountries);
      }
    }

    state.selectedCountries = newCountries;

    // Обновляем старое состояние для совместимости
    if (newCountries.length === 0) {
      state.selectedCountry = 'all';
    } else if (newCountries.length === 1) {
      state.selectedCountry = newCountries[0];
    } else {
      state.selectedCountry = 'multiple';
    }

    // МГНОВЕННОЕ ОБНОВЛЕНИЕ для лучшей отзывчивости
    this.updateUIInstant();
    this.saveToURL();
    this.applyFilters();
    this.isUpdating = false;
  }

  /**
   * Переключить множественный выбор типа танка
   */
  toggleMultipleCategoryFilter(categoryValue) {
    if (this.isUpdating) return;

    // Предотвращаем повторные обновления
    this.isUpdating = true;

    const currentCategories = state.selectedCategories || [];
    let newCategories;

    if (categoryValue === 'all') {
      // Если выбрали "все", очищаем массив
      newCategories = [];
    } else {
      // Переключаем тип в массиве
      if (currentCategories.includes(categoryValue)) {
        newCategories = currentCategories.filter(c => c !== categoryValue);
        console.log(`🔄 Убрали тип ${categoryValue}, осталось:`, newCategories);
      } else {
        newCategories = [...currentCategories, categoryValue];
        console.log(`✅ Добавили тип ${categoryValue}, теперь:`, newCategories);
      }
    }

    state.selectedCategories = newCategories;

    // Обновляем старое состояние для совместимости
    if (newCategories.length === 0) {
      state.selectedCategory = 'all';
    } else if (newCategories.length === 1) {
      state.selectedCategory = newCategories[0];
    } else {
      state.selectedCategory = 'multiple';
    }

    // МГНОВЕННОЕ ОБНОВЛЕНИЕ для лучшей отзывчивости
    this.updateUIInstant();
    this.saveToURL();
    this.applyFilters();
    this.isUpdating = false;
  }

  /**
   * Переключить фильтр категории - МГНОВЕННАЯ ВЕРСИЯ
   */
  toggleCategoryFilter(categoryValue) {
    if (this.isFilterActive('category', categoryValue)) {
      this.setCategoryFilterInstant('all');
    } else {
      this.setCategoryFilterInstant(categoryValue);
    }
  }

  /**
   * ПОЛУЧЕНИЕ КЭШИРОВАННОГО DOM ЭЛЕМЕНТА (делегируется к унифицированной системе)
   */
  getCachedElement(selector) {
    const element = domCache.getCachedElement(selector);
    if (element) {
      this.metrics.cacheHits++;
    }
    return element;
  }

  /**
   * МГНОВЕННОЕ ОБНОВЛЕНИЕ (БЕЗ DEBOUNCE)
   */
  debouncedUpdate(callback, delay = PERFORMANCE_THRESHOLDS.DEBOUNCE_DELAY) {
    clearTimeout(this.debounceTimeout);
    // Мгновенное выполнение для максимальной отзывчивости
    if (delay === 0) {
      callback();
    } else {
      this.debounceTimeout = setTimeout(callback, delay);
    }
  }

  /**
   * Установить фильтр страны
   */
  setCountryFilter(value) {
    if (this.isUpdating) return;

    state.selectedCountry = value;
    this.debouncedUpdate(() => {
      this.updateUI();
      this.saveToURL();
    });
  }

  /**
   * Установить фильтр категории
   */
  setCategoryFilter(value) {
    if (this.isUpdating) return;

    state.selectedCategory = value;
    this.debouncedUpdate(() => {
      this.updateUI();
      this.saveToURL();
    });
  }

  /**
   * Установить фильтр страны - МГНОВЕННАЯ ВЕРСИЯ
   */
  setCountryFilterInstant(value) {
    if (this.isUpdating) return;

    state.selectedCountry = value;
    // Мгновенное обновление UI и URL
    this.updateUIInstant();
    this.saveToURL();
  }

  /**
   * Установить фильтр категории - МГНОВЕННАЯ ВЕРСИЯ
   */
  setCategoryFilterInstant(value) {
    if (this.isUpdating) return;

    state.selectedCategory = value;
    // Мгновенное обновление UI и URL
    this.updateUIInstant();
    this.saveToURL();
  }

  /**
   * Мгновенное обновление UI без задержек
   */
  updateUIInstant() {
    import('../components/tank-list/index.js').then(module => {
      const filters = this.getCurrentFilters();

      // Обновляем множественный выбор если есть выбранные фильтры
      if (filters.countries && filters.countries.length > 0) {
        module.updateMultipleFilterSelection('country', filters.countries);
        console.log(`🎨 MULTIPLE: Countries synchronized:`, filters.countries);
      } else {
        // Обратная совместимость для одиночного выбора
        const countryForUI = filters.country === 'all' ? 'all' :
          (filters.country === 'USSR' ? 'ussr' : filters.country.toLowerCase());
        module.updateFilterSelection('country', countryForUI);
        console.log(`🎨 SINGLE: Country synchronized: ${countryForUI}`);
      }

      if (filters.categories && filters.categories.length > 0) {
        module.updateMultipleFilterSelection('category', filters.categories);
        console.log(`🎨 MULTIPLE: Categories synchronized:`, filters.categories);
      } else {
        // Обратная совместимость для одиночного выбора
        const categoryForUI = filters.category === 'all' ? 'all' : filters.category;
        module.updateFilterSelection('category', categoryForUI);
        console.log(`🎨 SINGLE: Category synchronized: ${categoryForUI}`);
      }
    });
  }

  /**
   * Установить фильтры для танка (при выборе танка)
   */
  setFiltersForTank(tank) {
    if (this.isUpdating) return;
    
    console.log(`🔧 FilterManager: Set filters for tank '${tank.name}': ${tank.country}/${tank.type}`);
    
    this.isUpdating = true;
    state.selectedCountry = tank.country;
    state.selectedCategory = tank.type;
    this.updateUI();
    this.isUpdating = false;
  }

  /**
   * Сбросить все фильтры
   */
  resetFilters() {
    console.log(`🔧 FilterManager: Reset all filters`);
    
    this.isUpdating = true;
    state.selectedCountry = 'all';
    state.selectedCategory = 'all';
    state.searchQuery = '';
    this.updateUI();
    this.saveToURL();
    this.isUpdating = false;
  }

  /**
   * 🧠 QUANTUM URL RESTORATION - HARVARD LEVEL
   * Восстановить фильтры из URL с максимальной точностью
   */
  restoreFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    const country = urlParams.get('country') || 'all';
    const category = urlParams.get('category') || 'all';
    const search = urlParams.get('search') || '';

    if (window.DEBUG_MODE) console.log(`🧠 QUANTUM FilterManager: URL restoration: ${country}/${category}/${search}`);

    // АТОМАРНОЕ ОБНОВЛЕНИЕ СОСТОЯНИЯ
    this.isUpdating = true;

    // Устанавливаем состояние с правильной обработкой
    state.selectedCountry = country === 'ussr' ? 'USSR' : country; // Нормализация USSR
    state.selectedCategory = decodeURIComponent(category);
    state.searchQuery = search;

    // ВАЖНО: Помечаем как выбранные пользователем если есть URL параметры
    if (country !== 'all' || category !== 'all' || search !== '') {
      state.countrySelectedManually = true;
      state.categorySelectedManually = true;
    }

    this.updateUI();
    this.isUpdating = false;

    const result = {
      country: state.selectedCountry,
      category: state.selectedCategory,
      search: state.searchQuery,
      hasFilters: state.selectedCountry !== 'all' || state.selectedCategory !== 'all' || state.searchQuery !== ''
    };

    console.log(`✅ QUANTUM: URL restoration complete:`, result);
    return result;
  }

  /**
   * 🧠 QUANTUM URL SYNCHRONIZATION - HARVARD LEVEL
   * Сохранить фильтры в URL с максимальной точностью
   */
  saveToURL() {
    if (this.isUpdating) return;

    try {
      const url = new URL(window.location);

      // Очищаем старые параметры
      ['country', 'category', 'search'].forEach(param => url.searchParams.delete(param));

      // Получаем текущие фильтры
      const filters = this.getCurrentFilters();

      // QUANTUM NORMALIZATION: Правильная обработка USSR
      if (filters.country !== 'all') {
        const normalizedCountry = filters.country === 'USSR' ? 'ussr' : filters.country.toLowerCase();
        url.searchParams.set('country', normalizedCountry);
      }

      if (filters.category !== 'all') {
        url.searchParams.set('category', encodeURIComponent(filters.category));
      }

      if (filters.search) {
        url.searchParams.set('search', filters.search);
      }

      // АТОМАРНОЕ ОБНОВЛЕНИЕ URL
      const newUrl = url.toString();
      if (window.location.href !== newUrl) {
        window.history.replaceState(null, '', newUrl);
        // QUANTUM: URL synchronized
      }
    } catch (error) {
      console.error('🚨 QUANTUM: URL sync failed:', error);
    }
  }

  /**
   * 🧠 QUANTUM UI SYNCHRONIZATION - HARVARD LEVEL
   * Обновить UI фильтров с максимальной точностью
   */
  updateUI() {
    import('../components/tank-list/index.js').then(module => {
      const filters = this.getCurrentFilters();

      // QUANTUM NORMALIZATION: Правильная обработка стран для UI
      const countryForUI = filters.country === 'all' ? 'all' :
        (filters.country === 'USSR' ? 'ussr' : filters.country.toLowerCase());

      // QUANTUM CATEGORY MAPPING: Правильная обработка категорий
      const categoryForUI = filters.category;

      // АТОМАРНОЕ ОБНОВЛЕНИЕ UI
      module.updateFilterSelection('country', countryForUI);
      module.updateFilterSelection('category', categoryForUI);

      // QUANTUM: UI synchronized

      // ДОПОЛНИТЕЛЬНАЯ ВАЛИДАЦИЯ: Проверяем что UI действительно обновился
      // ОПТИМИЗАЦИЯ: Убираем избыточную проверку синхронизации
      // Проверка выполняется только если действительно нужна
      if (countryForUI !== 'all' || categoryForUI !== 'all') {
        // МГНОВЕННАЯ СИНХРОНИЗАЦИЯ БЕЗ ЗАДЕРЖКИ
        const countryElement = document.querySelector(`[data-country="${countryForUI}"]`);
        const categoryElement = document.querySelector(`[data-category="${categoryForUI}"]`);

        // Проверяем только если элемент существует и не синхронизирован
        if (countryForUI !== 'all' && countryElement && !countryElement.classList.contains('selected')) {
          console.log('🔧 QUANTUM: Syncing country UI state...');
          module.updateFilterSelection('country', countryForUI);
        }

        if (categoryForUI !== 'all' && categoryElement && !categoryElement.classList.contains('selected')) {
          console.log('🔧 QUANTUM: Syncing category UI state...');
          module.updateFilterSelection('category', categoryForUI);
        }
      }
    });
  }

  /**
   * 🧠 QUANTUM FILTER APPLICATION - IQ 500 HARVARD LEVEL
   * КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Предотвращение дублирования танков
   */
  applyFilters() {
    const startTime = performance.now();

    // УСИЛЕННАЯ QUANTUM PROTECTION: Атомарная защита от дублирования
    if (this.isUpdating) {
      // console.log('🚫 QUANTUM: Already updating, CANCELLING previous operation');
      return Promise.resolve();
    }

    // console.log(`🧠 QUANTUM FilterManager: Applying filters with IQ 500...`);

    const operationKey = `filter_${Date.now()}_${Math.random()}`;
    this.isUpdating = true;
    this.currentOperationKey = operationKey;

    return new Promise((resolve) => {
      (async () => {
      try {
        // АТОМАРНАЯ ОПЕРАЦИЯ СБРОСА СОСТОЯНИЯ
        this.resetTankSelection();

        // АСИНХРОННОЕ СКРЫТИЕ ХАРАКТЕРИСТИК
        await this.hideTankCharacteristicsAsync();

        // ПОКАЗ СПИСКА ТАНКОВ
        this.showTankList();

        // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Проверяем, что операция не отменена
        if (this.currentOperationKey === operationKey) {
          // ПРИМЕНЕНИЕ ФИЛЬТРОВ С МОНИТОРИНГОМ (FORCE для фильтров)
          const { tankManager } = await import('./TankManager.js');
          await tankManager.applyFiltersAndRenderTankList(false, true); // force=true для фильтров

          // ЗАПИСЬ МЕТРИК ПРОИЗВОДИТЕЛЬНОСТИ
          const filterTime = performance.now() - startTime;
          // console.log(`⚡ FilterManager: Filters applied in ${filterTime.toFixed(2)}ms`);
        } else {
          console.log('🚫 FilterManager: Operation cancelled');
        }

        resolve();
      } catch (error) {
        console.error('🚨 FilterManager: Critical error in applyFilters:', error);
        resolve(); // Не блокируем выполнение
      } finally {
        // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Проверяем, что это та же операция
        if (this.currentOperationKey === operationKey) {
          this.isUpdating = false;
          this.currentOperationKey = null;
        }
      }
      })();
    });
  }

  /**
   * ПРОФЕССИОНАЛЬНЫЙ СБРОС ВЫБОРА ТАНКА
   */
  resetTankSelection() {
    if (state.selectedTank) {
      state.selectedTank = null;
      localStorage.removeItem('selectedTank');
      document.body.removeAttribute('data-tank-page');
    }
  }

  /**
   * АСИНХРОННОЕ СКРЫТИЕ ХАРАКТЕРИСТИК
   */
  async hideTankCharacteristicsAsync() {
    try {
      const module = await import('../components/TankCharacteristics.js');
      module.hideTankCharacteristics();
    } catch (error) {
      // Failed to hide tank characteristics
    }
  }

  /**
   * Показать список танков
   */
  showTankList() {
    // КРИТИЧНО: Показываем секцию vehicles ТОЛЬКО если мы на vehicles странице
    const currentPath = window.location.pathname;
    const activeMenuItem = localStorage.getItem('activeMenuItem');

    if (currentPath === '/vehicles' || activeMenuItem === 'vehicles') {
      // Показываем секцию vehicles
      import('../utils/ui.js').then(module => {
        module.showSection('vehicles');
      });
    } else {
      console.log('🚫 FilterManager: Skipping vehicles section show - not on vehicles page');
      return; // Не показываем tank-list если не на vehicles странице
    }

    // Показываем tank-list
    const tankListElement = document.getElementById('tank-list');
    if (tankListElement) {
      tankListElement.style.display = 'grid';
      tankListElement.style.visibility = 'visible';
      tankListElement.style.opacity = '1';
      tankListElement.classList.remove('hidden');
    }

    // Скрываем кнопку "Назад к списку"
    const backButton = document.getElementById('back-to-list-btn');
    if (backButton) {
      backButton.classList.add('hidden');
    }
  }

  /**
   * Получить информацию о состоянии фильтров
   */
  getFilterInfo() {
    const filters = this.getCurrentFilters();
    return {
      hasActiveFilters: filters.country !== 'all' || filters.category !== 'all' || filters.search !== '',
      activeCount: [filters.country, filters.category, filters.search].filter(f => f && f !== 'all').length,
      filters
    };
  }
}

// Создаем единственный экземпляр
export const filterManager = new FilterManager();
