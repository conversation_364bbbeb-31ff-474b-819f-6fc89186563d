/**
 * УПРОЩЕННАЯ СИСТЕМА УПРАВЛЕНИЯ UI
 * Эффективная система управления интерфейсом без избыточной сложности
 */
import { state } from '../store/state.js';

// Простые флаги состояния
let isVehiclesRendering = false;
let isStabilizing = false;
let lastStabilizeTime = 0;
let isShowingSection = false;
let lastSectionShown = null;
let lastOperationTime = 0;
let sectionOperationKey = null;

// Минимальный интервал между операциями
const OPERATION_COOLDOWN = 50;

// Упрощенные метрики
const uiMetrics = {
  sectionSwitches: 0,
  lastSwitchTime: 0
};

// Состояние системы вращения
let rotationState = {
  isActive: false,
  currentDirection: 1,
  animationFrame: null,
  rotation: 0,
  speed: 0,
  targetSpeed: 0,
  lastMouseX: 0,
  currentMenuName: null,
  isMenuSwitching: false,
  lastClickTime: 0
};

// Упрощенные константы конфигурации
const CONFIG = {
  NORMAL_SPEED: 6.0,
  FAST_SPEED: 9.0,
  ACCELERATION: 0.2,
  DECELERATION: 0.95,
  MOUSE_THRESHOLD: 3,
  SWIPE_THRESHOLD: 10,
  STOP_THRESHOLD: 0.1
};

// Упрощенный менеджер списка танков
const tankListManager = {
  showTankList(tankListElement) {
    console.log('📋 Показываем tank-list МГНОВЕННО');
    tankListElement.style.display = 'grid';
    tankListElement.style.visibility = 'visible';
    tankListElement.style.opacity = '1';
    tankListElement.classList.remove('hidden', 'tank-list-hidden');
    tankListElement.classList.add('tank-list-visible');
    // УБИРАЕМ requestAnimationFrame - показываем мгновенно
  },

  hideTankList(tankListElement) {
    console.log('🙈 Скрываем tank-list');
    tankListElement.classList.remove('tank-list-visible');
    tankListElement.classList.add('tank-list-hidden');

    setTimeout(() => {
      tankListElement.style.display = 'none';
      tankListElement.style.visibility = 'hidden';
      tankListElement.classList.add('hidden');
    }, 300);
  }
};

// Map sidebar menu keys to their corresponding container IDs/classes.
// НЕ включаем main-content-container - он должен оставаться стабильным для скроллбара
export const SECTION_MAP = {
  overview: 'overview-section', // General info page
  vehicles: 'tank-list', // Tank list grid (main content columns)
  compare: 'compare-section', // Compare tanks UI
  tactics: 'tactics-section', // Tactics drawing page
  settings: 'settings-section', // Settings/config page
  'tank-characteristics': 'tank-characteristics-container', // Tank characteristics page
};



/**
 * Стабилизация контейнера - НЕ трогаем его при переключении вкладок
 */
export function stabilizeMainContainer() {
  // Защита от частых вызовов
  const now = Date.now();
  if (isStabilizing || (now - lastStabilizeTime) < 50) {
    return;
  }

  isStabilizing = true;
  lastStabilizeTime = now;

  const container = document.querySelector('.main-content-container');
  if (container) {
    // Гарантируем стабильность контейнера - НЕ скрываем его никогда
    container.style.display = 'block';
    container.style.visibility = 'visible';
    container.style.opacity = '1';
    // НЕ ТРОГАЕМ overflow - у него своя стабилизация в scrollbar.js
    container.classList.remove('hidden');
  }

  isStabilizing = false;
}

/**
 * Упрощенное восстановление состояния приложения
 */
export function restoreAppState() {
  try {
    const savedState = localStorage.getItem('appState');
    if (!savedState) {
      return null;
    }

    const appState = JSON.parse(savedState);

    // Инициализируем значения по умолчанию
    return {
      selectedCountry: appState.selectedCountry || 'all',
      selectedCategory: appState.selectedCategory || 'all',
      searchQuery: appState.searchQuery || '',
      countrySelectedManually: Boolean(appState.countrySelectedManually),
      categorySelectedManually: Boolean(appState.categorySelectedManually),
      selectedTank: appState.selectedTank,
      currentMenuName: appState.currentMenuName
    };
  } catch (error) {
    console.warn('Failed to restore app state:', error);
    return null;
  }
}

/**
 * Упрощенное сохранение состояния
 */
export function saveAppState() {
  const now = Date.now();
  if ((now - uiMetrics.lastSaveTime) < 200) {
    return;
  }
  uiMetrics.lastSaveTime = now;

  try {
    import('../store/state.js').then(stateModule => {
      const { state } = stateModule;

      const appState = {
        selectedTank: state.selectedTank,
        selectedCountry: state.selectedCountry || 'all',
        selectedCategory: state.selectedCategory || 'all',
        searchQuery: state.searchQuery || '',
        countrySelectedManually: Boolean(state.countrySelectedManually),
        categorySelectedManually: Boolean(state.categorySelectedManually),
        currentMenuName: state.currentMenuName,
        timestamp: now
      };

      localStorage.setItem('appState', JSON.stringify(appState));
    });
  } catch (error) {
    console.error('Failed to save app state:', error);
  }
}

/**
 * Упрощенное скрытие всех секций
 */
export function hideAllSections(targetMenu = null) {
  lastSectionShown = null;

  const hasSelectedTank = localStorage.getItem('selectedTank');
  const isOnTankPage = window.location.pathname.startsWith('/vehicles/');

  Object.values(SECTION_MAP).forEach(id => {
    // Сохраняем характеристики танка на вкладке vehicles
    if (id === 'tank-characteristics-container') {
      if (targetMenu === 'vehicles' && (hasSelectedTank || isOnTankPage)) {
        return;
      }
    }

    const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
    if (el) {
      el.style.display = 'none';
      el.style.visibility = 'hidden';
      el.classList.add('hidden');
      el.classList.remove('section-visible', 'tank-list-visible');
    }
  });

  // МГНОВЕННО скрываем флаги при переходе с Vehicles
  const flagSection = document.getElementById('flag-section');
  if (flagSection && targetMenu !== 'vehicles') {
    flagSection.classList.remove('open');
    // Принудительно отключаем transition для мгновенного сворачивания
    flagSection.style.transition = 'none';
    flagSection.style.cssText = 'max-height: 0; opacity: 0; overflow: hidden; visibility: hidden; pointer-events: none; transition: none;';

    // Восстанавливаем transition через микротаск
    setTimeout(() => {
      flagSection.style.transition = '';
    }, 0);
  }

  // МГНОВЕННО скрываем tier-filter при переходе с Vehicles
  const tierFilterSidebar = document.getElementById('tier-filter-container');
  if (tierFilterSidebar && targetMenu !== 'vehicles') {
    tierFilterSidebar.style.display = 'none';
    tierFilterSidebar.style.visibility = 'hidden';
    tierFilterSidebar.style.opacity = '0';
  }

  // МГНОВЕННО скрываем tier-separator при переходе с Vehicles
  const tierSeparator = document.getElementById('tier-separator');
  if (tierSeparator && targetMenu !== 'vehicles') {
    tierSeparator.style.display = 'none';
    tierSeparator.style.visibility = 'hidden';
    tierSeparator.style.opacity = '0';
  }
}

/**
 * Close any open tank detail windows/panels
 */
function closeTankDetailWindows() {
  // Hide tank characteristics container
  const tankCharacteristicsContainer = document.getElementById('tank-characteristics-container');
  if (tankCharacteristicsContainer) {
    tankCharacteristicsContainer.style.display = 'none';
    tankCharacteristicsContainer.style.opacity = '0';
    tankCharacteristicsContainer.style.visibility = 'hidden';
    tankCharacteristicsContainer.classList.add('hidden');
  }

  // Старый контейнер деталей танка удален, больше не нужно его скрывать

  // Hide any modal windows
  const modals = document.querySelectorAll('.modal:not(.hidden)');
  modals.forEach(modal => {
    modal.classList.add('hidden');
    modal.style.display = 'none';
  });

  // Reset tank selection state if available (legacy support)
  if (typeof window !== 'undefined' && window.appState) {
    window.appState.selectedTank = null;
  }

  // Clear localStorage tank selection
  if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('selectedTank');
  }

  // Clear URL hash if it contains tank name
  if (typeof window !== 'undefined' && window.location.hash) {
    window.history.replaceState(null, '', window.location.pathname + window.location.search);
  }
}

/**
 * Упрощенный показ секции
 */
export function showSection(menu) {
  const now = Date.now();
  const operationKey = `${menu}_${now}`;

  // УМНАЯ защита от частых вызовов - разрешаем переключение между разными секциями
  if (isShowingSection || (lastSectionShown === menu && (now - lastOperationTime) < 100)) {
    console.log(`🚫 showSection: Skipping ${menu} - already showing or too frequent (${now - lastOperationTime}ms)`);
    return;
  }

  isShowingSection = true;
  lastSectionShown = menu;
  lastOperationTime = now;
  sectionOperationKey = operationKey;

  // Скрываем все секции
  hideAllSections(menu);

  // Стабилизируем контейнер
  stabilizeMainContainer();

  const id = SECTION_MAP[menu];
  if (!id) {
    isShowingSection = false;
    return;
  }

  const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
  console.log(`🔍 showSection: menu=${menu}, id=${id}, element found:`, !!el);

  if (el) {
    // Устанавливаем активную секцию на body для CSS правил
    document.body.setAttribute('data-active-section', menu);

    // HARVARD-LEVEL УМНОЕ УПРАВЛЕНИЕ СКРОЛЛБАРОМ
    const mainContainer = document.querySelector('.main-content-container');
    if (mainContainer) {
      // Убираем все классы активности
      mainContainer.classList.remove('overview-active', 'vehicles-active', 'compare-active', 'tactics-active', 'settings-active', 'auto-scroll');

      // Добавляем класс для текущей секции
      mainContainer.classList.add(`${menu}-active`);

      // Запускаем проверку необходимости скроллбара через небольшую задержку
      setTimeout(() => {
        if (typeof checkAndEnableScrollbar === 'function') {
          checkAndEnableScrollbar(mainContainer, menu);
        } else {
          console.log('🚫 checkAndEnableScrollbar not available yet');
        }
      }, 100);


    }

    // ПРОФЕССИОНАЛЬНОЕ ПОКАЗЫВАНИЕ ТОЛЬКО НУЖНОЙ СЕКЦИИ
    el.style.display = menu === 'vehicles' ? 'grid' : 'block';
    el.style.visibility = 'visible';
    el.classList.remove('hidden');
    el.classList.add('section-visible');

    // Скрываем tank-list для не-vehicles секций
    const tankList = document.getElementById('tank-list');
    if (tankList && menu !== 'vehicles') {
      tankList.style.display = 'none';
      tankList.style.visibility = 'hidden';
      tankList.style.opacity = '0';
      tankList.classList.add('hidden');
      tankList.classList.remove('section-visible', 'tank-list-visible');
    }



    console.log(`✅ Section ${menu} shown:`, {
      display: el.style.display,
      visibility: el.style.visibility,
      classes: el.className
    });

    // Специальная анимация для разных секций
    if (menu === 'vehicles') {
      el.style.opacity = '0';
      el.style.transform = 'translateY(10px)';
      el.style.transition = 'opacity 0.2s ease-out, transform 0.2s ease-out';
      el.classList.add('tank-list-visible');

      setTimeout(() => {
        el.style.opacity = '1';
        el.style.transform = 'translateY(0)';
      }, 20);
    } else {
      // Для всех остальных секций - мгновенное появление
      el.style.opacity = '1';
      el.style.transform = 'translateY(0)';
      el.style.transition = 'opacity 0.15s ease-out';
    }


  }

  // КРИТИЧЕСКАЯ ЗАЩИТА: Проверяем, что это та же операция
  if (sectionOperationKey === operationKey) {
    isShowingSection = false;
    sectionOperationKey = null;
  }

  // НЕ УПРАВЛЯЕМ tank-list здесь - это делается в onMenuSelected для предотвращения двойного дергания
  if (menu === 'vehicles') {


    // НЕ СКРЫВАЕМ характеристики танка автоматически - это должно управляться логикой выбора танка
    // Характеристики должны оставаться видимыми при обновлении страницы


    // КРИТИЧЕСКАЯ ЗАЩИТА: Восстанавливаем характеристики танка если они были скрыты
    const hasSelectedTank = localStorage.getItem('selectedTank');
    const isOnTankPage = window.location.pathname.startsWith('/vehicles/');

    if (hasSelectedTank || isOnTankPage) {
      // ПРИНУДИТЕЛЬНО восстанавливаем характеристики танка
      const characteristicsElement = document.getElementById('tank-characteristics-container');
      if (characteristicsElement) {
        characteristicsElement.classList.remove('hidden');
        characteristicsElement.style.display = 'block';
        characteristicsElement.style.opacity = '1';
        characteristicsElement.style.visibility = 'visible';
        console.log('🛡️ ВОССТАНОВЛЕНЫ характеристики танка (танк выбран или страница танка)');
      }
    } else {
      // Только если нет выбранного танка, скрываем характеристики
      const characteristicsElement = document.getElementById('tank-characteristics-container');
      if (characteristicsElement && !characteristicsElement.classList.contains('hidden')) {
        characteristicsElement.classList.add('hidden');
        characteristicsElement.style.display = 'none';
        console.log('🔧 showSection: vehicles - hiding characteristics (no selected tank)');
      }
    }

    // ВАЖНО: Всегда открываем секцию фильтров для vehicles
    const flagSection = document.getElementById('flag-section');
    if (flagSection) {
      flagSection.classList.add('open');
      flagSection.style.maxHeight = '1000px';
      flagSection.style.opacity = '1';
      flagSection.style.visibility = 'visible';
      flagSection.style.pointerEvents = 'auto';
    }

    // Показываем tier-filter в сайдбаре
    const tierFilterRow = document.getElementById('tier-filter-container');
    if (tierFilterRow) {
      tierFilterRow.style.display = 'flex';
      tierFilterRow.style.opacity = '1';
      tierFilterRow.style.visibility = 'visible';
      tierFilterRow.style.pointerEvents = 'auto';
    }

    // Показываем разделительную линию
    const tierSeparator = document.getElementById('tier-separator');
    if (tierSeparator) {
      tierSeparator.style.display = 'block';
      tierSeparator.style.opacity = '1';
      tierSeparator.style.visibility = 'visible';
      tierSeparator.style.pointerEvents = 'auto';
    }

    // Синхронизируем состояние системы сравнения при переключении на vehicles
    if (window.advancedCompareSystem) {
      window.advancedCompareSystem.syncAllBadges();
    }
  } else {
    // Для всех остальных секций просто скрываем tank-list
    const tankListElement = document.getElementById('tank-list');
    if (tankListElement) {
      // Полное скрытие - overflow: auto сам решит нужен ли скроллбар
      tankListElement.classList.remove('tank-list-visible');
      tankListElement.classList.add('tank-list-hidden', 'hidden');
      tankListElement.style.display = 'none';
      tankListElement.style.visibility = 'hidden';
      tankListElement.style.opacity = '0';

    }

    // Синхронизируем состояние системы сравнения при переключении на compare
    if (menu === 'compare') {
      if (window.advancedCompareSystem) {
        window.advancedCompareSystem.syncAllBadges();
      }
      if (window.newCompareSystem) {
        window.newCompareSystem.syncWithOldSystem();
      }
    }
  }
}

/**
 * Setup sidebar menu items with click handlers
 */
export function setupSidebarMenuItems() {
  const sidebar = document.querySelector('.sidebar');
  if (!sidebar) {
    console.warn('[sidebar] .sidebar element not found.');
    return;
  }

  // Качественная система вращения иконки с нуля
  const icon = sidebar.querySelector('.sidebar-logo-icon');
  if (!icon) {
    console.error('❌ Иконка не найдена');
    return;
  }

  console.log('✅ Настраиваем качественную систему вращения');

  // ПРОСТЫЕ СТИЛИ БЕЗ !important
  icon.style.transformOrigin = 'center center';
  icon.style.willChange = 'transform';
  icon.style.transition = 'none';
  icon.style.transform = 'rotate(0deg)';

  // ИСПОЛЬЗУЕМ ГЛОБАЛЬНОЕ СОСТОЯНИЕ СИСТЕМЫ ВРАЩЕНИЯ
  // rotationState уже определен в глобальной области

  // ИСПОЛЬЗУЕМ ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ
  // tankListManager и CONFIG уже определены в глобальной области

  // Естественная анимация с постепенной остановкой - УЛУЧШЕННАЯ
  function animate() {
    if (!rotationState.isActive) {
      // УЛУЧШЕННОЕ ЕСТЕСТВЕННОЕ ТОРМОЖЕНИЕ
      rotationState.speed *= CONFIG.DECELERATION; // Используем конфигурируемое торможение

      // Когда скорость очень мала, находим ближайшее положение остановки
      if (Math.abs(rotationState.speed) < CONFIG.STOP_THRESHOLD) {
        // Возможные положения остановки: 0°, 90°, 180°, 270°
        const stopPositions = [0, 90, 180, 270];

        // Нормализуем текущий угол к диапазону 0-360°
        const normalizedRotation = ((rotationState.rotation % 360) + 360) % 360;

        // Находим ближайшее положение остановки
        let closestPosition = stopPositions[0];
        let minDistance = Math.abs(normalizedRotation - stopPositions[0]);

        for (const position of stopPositions) {
          const distance = Math.abs(normalizedRotation - position);
          if (distance < minDistance) {
            minDistance = distance;
            closestPosition = position;
          }
        }

        // Также проверяем расстояние через 360° (для 0°)
        if (closestPosition === 0) {
          const distanceThrough360 = Math.abs(normalizedRotation - 360);
          if (distanceThrough360 < minDistance) {
            closestPosition = 360;
          }
        }

        // Если очень близко к ближайшему положению, останавливаемся
        if (minDistance < 5) { // В пределах 5 градусов
          rotationState.speed = 0;
          rotationState.rotation = closestPosition;
          icon.style.transform = `rotate(${closestPosition}deg)`;
          rotationState.animationFrame = null;
          return;
        }

        // Плавно доводим до ближайшего положения
        const targetDirection = closestPosition > normalizedRotation ? 1 : -1;
        if (closestPosition === 360 && normalizedRotation > 180) {
          // Особый случай для перехода через 360° к 0°
          rotationState.rotation += 0.5;
        } else if (closestPosition === 0 && normalizedRotation > 180) {
          // Переход к 0° через 360°
          rotationState.rotation += 0.5;
        } else {
          rotationState.rotation += targetDirection * 0.5;
        }
      }
    } else {
      // Плавное изменение скорости к целевой
      const diff = rotationState.targetSpeed - rotationState.speed;
      rotationState.speed += diff * CONFIG.ACCELERATION;
    }

    // Обновляем поворот
    rotationState.rotation += rotationState.speed * rotationState.currentDirection;
    icon.style.transform = `rotate(${rotationState.rotation}deg)`;

    // Продолжаем анимацию
    rotationState.animationFrame = requestAnimationFrame(animate);
  }

  // Запуск вращения
  function startRotation(speed = CONFIG.NORMAL_SPEED) {
    // Останавливаем предыдущую анимацию если она есть
    if (rotationState.animationFrame) {
      cancelAnimationFrame(rotationState.animationFrame);
      rotationState.animationFrame = null;
    }

    rotationState.isActive = true;
    rotationState.targetSpeed = speed;
    rotationState.currentDirection = Math.random() < 0.5 ? 1 : -1;

    // 40% шанс супер-быстрого вращения
    if (Math.random() < 0.4) {
      rotationState.targetSpeed = CONFIG.FAST_SPEED;
    }

    // Всегда запускаем новую анимацию
    animate();
  }

  // Остановка вращения
  function stopRotation() {
    rotationState.isActive = false;
    rotationState.targetSpeed = 0;
    // НЕ останавливаем анимацию здесь - она остановится сама в animate()
  }

  // Смена направления
  function changeDirection(mouseDirection) {
    if (!rotationState.isActive) return;

    // mouseDirection: 1 = вправо, -1 = влево
    // Логика: движение вправо = против часовой (-1), влево = по часовой (1)
    const newDirection = -mouseDirection;

    if (newDirection !== rotationState.currentDirection) {
      rotationState.currentDirection = newDirection;
    }
  }

  // Обработка свайпа - ПРОСТАЯ ВЕРСИЯ БЕЗ БЫСТРЫХ СВАЙПОВ
  function handleSwipe(deltaX) {
    if (rotationState.isActive) return;

    // Останавливаем предыдущую анимацию если она есть
    if (rotationState.animationFrame) {
      cancelAnimationFrame(rotationState.animationFrame);
      rotationState.animationFrame = null;
    }

    const swipeDirection = deltaX > 0 ? -1 : 1; // вправо = против часовой, влево = по часовой

    rotationState.isActive = true;
    rotationState.currentDirection = swipeDirection;
    rotationState.targetSpeed = CONFIG.NORMAL_SPEED; // ВСЕГДА ОБЫЧНАЯ СКОРОСТЬ

    // Всегда запускаем новую анимацию
    animate();

    // Автостоп через 4 секунды для свайпов
    setTimeout(() => {
      if (rotationState.isActive) {
        stopRotation();
      }
    }, 4000);
  }

  // События мыши
  icon.addEventListener('mouseenter', (e) => {
    rotationState.lastMouseX = e.clientX;
    startRotation();
  });

  icon.addEventListener('mousemove', (e) => {
    const deltaX = e.clientX - rotationState.lastMouseX;

    if (Math.abs(deltaX) > CONFIG.MOUSE_THRESHOLD) {
      const mouseDirection = deltaX > 0 ? 1 : -1;
      changeDirection(mouseDirection);
      rotationState.lastMouseX = e.clientX;
    }
  });

  icon.addEventListener('mouseleave', () => {
    stopRotation();
  });

  // Глобальный обработчик для свайпов
  let globalLastX = 0;
  document.addEventListener('mousemove', (e) => {
    if (rotationState.isActive) return; // Не мешаем активному вращению

    const rect = icon.getBoundingClientRect();
    const margin = 30;

    // Проверяем область вокруг иконки
    if (e.clientX >= rect.left - margin &&
        e.clientX <= rect.right + margin &&
        e.clientY >= rect.top - margin &&
        e.clientY <= rect.bottom + margin) {

      const deltaX = e.clientX - globalLastX;

      if (Math.abs(deltaX) > CONFIG.SWIPE_THRESHOLD) {
        handleSwipe(deltaX);
      }
    }

    globalLastX = e.clientX;
  });

  // Attach delegated click handler
  sidebar.addEventListener('click', e => {
    const item = e.target.closest('.sidebar-menu-item');
    if (!item) return;
    e.preventDefault();
    const section = item.getAttribute('data-section');
    if (!section) return;

    // ULTRA FAST CLICK PROTECTION - MINIMAL DELAY
    const now = Date.now();
    if ((now - rotationState.lastClickTime) < 5) { // Уменьшили с 10ms до 5ms
      console.log('🚫 Быстрый клик заблокирован');
      return;
    }
    rotationState.lastClickTime = now;
    // Убрали isMenuSwitching для мгновенной отзывчивости

    // Получаем текущую активную вкладку из localStorage
    const currentActive = localStorage.getItem('activeMenuItem') || 'overview';
    let menu = section;

    // console.log(`🔥 CLICK DEBUG: clicked=${menu}, currentActive=${currentActive}`);

    // Toggle logic: если кликнули по уже активной вкладке (кроме Overview), переходим на overview
    if (menu === currentActive && menu !== 'overview') {
      console.log(`🔄 TOGGLE ACTIVATED: ${menu} is already active - switching to overview`);
      menu = 'overview';
    } else {
      // console.log(`➡️ NORMAL SWITCH: Going to ${menu}`);
    }

    onMenuSelected(menu);
  });
}

// HARVARD-LEVEL ГЛОБАЛЬНАЯ ФУНКЦИЯ onMenuSelected
export function onMenuSelected(menu, _initial = false) {
    const startTime = performance.now();
    const operationKey = `menu_${menu}_${Date.now()}`;

    // ОТЛАДОЧНОЕ ЛОГИРОВАНИЕ ОТКЛЮЧЕНО ДЛЯ ПРОИЗВОДИТЕЛЬНОСТИ
    // if (window.location.hostname === 'localhost') {
    //   console.log(`🔥 CLICK DEBUG: clicked=${menu}, currentActive=${state.currentMenuName}`);
    // }

    // ПРОФЕССИОНАЛЬНАЯ ВАЛИДАЦИЯ
    if (!menu || typeof menu !== 'string') {
      console.error('🚨 Invalid menu parameter:', menu);
      return;
    }

    // INSTANT SWITCH PROTECTION - NO DELAY FOR VISUAL UPDATES
    const now = Date.now();
    if (rotationState.currentMenuName === menu &&
        !_initial &&
        (now - uiMetrics.lastSwitchTime) < 5) { // Minimal delay only for duplicate prevention
      console.log(`🚫 QUANTUM: Menu switch blocked - too frequent`);
      return;
    }

    // Получаем текущую активную вкладку из localStorage СНАЧАЛА
    const currentActive = localStorage.getItem('activeMenuItem') || 'overview';

    // ВАЛИДАЦИЯ: проверяем, что menu содержит только валидные символы для CSS селектора
    const validMenuName = menu.replace(/[^a-zA-Z0-9-_]/g, '');
    if (validMenuName !== menu) {
      console.log(`⚠️ Invalid menu name "${menu}", using fallback logic`);
      // Для невалидных имен (например, "vehicles/t-100-lt") используем fallback
      if (menu.startsWith('vehicles/')) {
        showSection('vehicles');
        return;
      }
    }

    // UPDATE ACTIVE CLASS IMMEDIATELY - BEFORE ANYTHING ELSE
    updateActiveClass(validMenuName);

    // БЫСТРАЯ ПРОВЕРКА: если секция уже показана, не делаем тяжелую обработку
    const targetSection = document.querySelector(`#${validMenuName}-section, .${validMenuName}-section`);
    const isAlreadyVisible = targetSection && !targetSection.classList.contains('hidden') &&
                             targetSection.style.display !== 'none';

    if (isAlreadyVisible && validMenuName === currentActive) {
      console.log(`⚡ FAST EXIT: ${validMenuName} already visible, skipping heavy processing`);
      return;
    }

    localStorage.setItem('activeMenuItem', validMenuName);
    rotationState.currentMenuName = validMenuName;

    // ОБНОВЛЕНИЕ МЕТРИК ПРОИЗВОДИТЕЛЬНОСТИ
    uiMetrics.sectionSwitches++;
    uiMetrics.lastSwitchTime = Date.now();

    // ОБНОВЛЯЕМ URL ПРИ ПЕРЕКЛЮЧЕНИИ ВКЛАДОК
    if (!_initial) {
      if (validMenuName === 'vehicles') {
        history.pushState({ section: 'vehicles' }, 'Vehicles', '/vehicles');
      } else if (validMenuName === 'overview') {
        history.pushState({ section: 'overview' }, 'Overview', '/');
      } else {
        history.pushState({ section: validMenuName }, validMenuName.charAt(0).toUpperCase() + validMenuName.slice(1), `/${validMenuName}`);
      }
    }

    // Уведомляем Premium UI о смене секции
    if (window.premiumUI && window.premiumUI.logActivity) {
      const sectionNames = {
        overview: 'Обзор',
        vehicles: 'Техника',
        compare: 'Сравнение',
        tactics: 'Тактики',
        settings: 'Настройки'
      };
      window.premiumUI.logActivity(`Переход в секцию: ${sectionNames[validMenuName] || validMenuName}`, '🔄');
    }

    // Сохраняем состояние перед переключением
    saveAppState();

    // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Вызываем showSection только если это не дублирующий вызов
    if (!isShowingSection && lastSectionShown !== validMenuName) {
      showSection(validMenuName);
    } else {
      console.log(`🚫 QUANTUM: Skipping duplicate showSection call for ${validMenuName}`);
    }

    // ЕДИНСТВЕННОЕ МЕСТО УПРАВЛЕНИЯ TANK-LIST И РЕНДЕРИНГА
    if (validMenuName === 'vehicles') {


      // ВАЖНО: Восстанавливаем состояние приложения СНАЧАЛА
      const savedAppState = restoreAppState();

      // СНАЧАЛА проверяем, есть ли выбранный танк
      const savedTankData = localStorage.getItem('selectedTank');
      const hasSelectedTank = state.selectedTank ||
                             (savedTankData && savedTankData !== 'null') ||
                             (savedAppState && savedAppState.selectedTank);

      if (hasSelectedTank) {
        console.log('🔧 Tank is selected, will show characteristics - NOT showing tank list');
        // НЕ показываем tank-list если есть выбранный танк
        const tankListElement = document.getElementById('tank-list');
        if (tankListElement) {
          tankListElement.style.display = 'none';
          tankListElement.style.visibility = 'hidden';
          tankListElement.classList.add('hidden');
        }


      } else {
  
        // Показываем tank-list только если нет выбранного танка
        const tankListElement = document.getElementById('tank-list');
        if (tankListElement) {
          // Убираем выделение с танков
          const selectedTanks = tankListElement.querySelectorAll('.tank-item.selected, .tank-card.selected');
          selectedTanks.forEach(tank => tank.classList.remove('selected'));

          // АБСОЛЮТНО МГНОВЕННОЕ появление БЕЗ ЛЮБЫХ эффектов
          tankListElement.classList.remove('hidden', 'tank-list-hidden');
          tankListElement.classList.add('tank-list-visible');
          tankListElement.style.cssText = 'display: grid; visibility: visible; opacity: 1; transition: none; transform: none;';

    
        }
      }

      // Сбрасываем флаг рендеринга при переключении на vehicles
      isVehiclesRendering = false;

      // НЕ ТРОГАЕМ КОНТЕЙНЕР - у него своя стабилизация в scrollbar.js

      // ВАЖНО: Состояние уже восстановлено выше

      import('../store/state.js').then(stateModule => {
        const { state } = stateModule;

        // Восстанавливаем состояние из сохраненного
        if (savedAppState) {
      

          // Восстанавливаем фильтры
          state.selectedCountry = savedAppState.selectedCountry || 'all';
          state.selectedCategory = savedAppState.selectedCategory || 'all';
          state.searchQuery = savedAppState.searchQuery || '';
          state.countrySelectedManually = savedAppState.countrySelectedManually || false;
          state.categorySelectedManually = savedAppState.categorySelectedManually || false;

          // Восстанавливаем танк если был выбран
          if (savedAppState.selectedTank) {
            state.selectedTank = savedAppState.selectedTank;
            localStorage.setItem('selectedTank', JSON.stringify(savedAppState.selectedTank));

            console.log('🔧 Vehicles tab: Restoring tank from saved state:', savedAppState.selectedTank.name);

            // Обновляем URL на танк и показываем характеристики НЕМЕДЛЕННО
            import('../utils/helpers.js').then(helpers => {
              const shortId = helpers.createTankUrlId(savedAppState.selectedTank.name);
              history.replaceState(null, `Tank: ${savedAppState.selectedTank.name}`, `/vehicles/${shortId}`);

              // Показываем характеристики НЕМЕДЛЕННО без задержки
              import('../components/TankCharacteristics.js').then(module => {
                module.showTankCharacteristics(savedAppState.selectedTank.name, false);
              });
            });

            return; // Выходим, не показываем список
          }
        }

        // Если нет сохраненного танка, проверяем текущее состояние
        if (state.selectedTank) {
          console.log('🔧 Vehicles tab clicked: Tank is selected in current state');

          // Показываем характеристики текущего танка НЕМЕДЛЕННО
          import('../components/TankCharacteristics.js').then(module => {
            module.showTankCharacteristics(state.selectedTank.name, false);
          });
        } else {
    
          // Если танк не выбран, обновляем URL на vehicles
          if (window.location.pathname !== '/vehicles') {
            history.replaceState(null, 'Vehicles', '/vehicles');
          }
        }
      });

      // EventManager уже инициализирован в UltraAppInitializer
  

      // ПОКАЗЫВАЕМ СПИСОК ТАНКОВ ЕСЛИ НЕТ ВЫБРАННОГО ТАНКА
      if (!hasSelectedTank) {

        optimizedVehiclesShow();
      }

      // Инициализируем TierFilter только для vehicles страницы
      const tierContainer = document.querySelector('#tier-filter-container');
      if (tierContainer && !tierContainer.hasAttribute('data-tier-initialized')) {
        import('../components/TierFilter.js').then(({ tierFilter }) => {
          tierFilter.init('#tier-filter-container', (selectedTiers) => {
            // Обновляем состояние при изменении Tier фильтра
            import('../store/state.js').then(({ state }) => {
              state.selectedTiers = selectedTiers;
              window.state.selectedTiers = selectedTiers;

              // Применяем фильтры
              if (window.tankManager) {
                window.tankManager.applyFiltersAndRenderTankList();
              }

              console.log('🏆 Tier filter changed:', selectedTiers);
            });
          });

          // Помечаем как инициализированный
          tierContainer.setAttribute('data-tier-initialized', 'true');
          console.log('🏆 Tier filter initialized for vehicles navigation');
        });
      }
    }

    // Show/hide flag section with INSTANT animation
    const flagSection = document.getElementById('flag-section');
    if (flagSection) {
      if (menu === 'vehicles') {
        flagSection.classList.add('open');
      } else {
        // МГНОВЕННОЕ сворачивание без анимации
        flagSection.style.transition = 'none';
        flagSection.classList.remove('open');
        flagSection.style.maxHeight = '0';
        flagSection.style.opacity = '0';
        flagSection.style.visibility = 'hidden';
        flagSection.style.pointerEvents = 'none';

        // Восстанавливаем transition через микротаск
        setTimeout(() => {
          flagSection.style.transition = '';
        }, 0);
      }
    }

    // Show/hide tier-filter section with INSTANT animation
    const tierFilterSidebar = document.getElementById('tier-filter-container');
    if (tierFilterSidebar) {
      if (menu === 'vehicles') {
        tierFilterSidebar.style.display = 'flex';
        tierFilterSidebar.style.opacity = '1';
        tierFilterSidebar.style.visibility = 'visible';
        tierFilterSidebar.style.pointerEvents = 'auto';
      } else {
        tierFilterSidebar.style.display = 'none';
        tierFilterSidebar.style.opacity = '0';
        tierFilterSidebar.style.visibility = 'hidden';
        tierFilterSidebar.style.pointerEvents = 'none';
      }
    }

    // Show/hide tier-separator with INSTANT animation
    const tierSeparator = document.getElementById('tier-separator');
    if (tierSeparator) {
      if (menu === 'vehicles') {
        tierSeparator.style.display = 'block';
        tierSeparator.style.opacity = '1';
        tierSeparator.style.visibility = 'visible';
        tierSeparator.style.pointerEvents = 'auto';
      } else {
        tierSeparator.style.display = 'none';
        tierSeparator.style.opacity = '0';
        tierSeparator.style.visibility = 'hidden';
        tierSeparator.style.pointerEvents = 'none';
      }
    }

    // INSTANT UNLOCK - NO DELAY (isMenuSwitching больше не используется)


  }

/**
 * HARVARD-LEVEL УМНАЯ ФУНКЦИЯ АВТОМАТИЧЕСКОГО СКРОЛЛБАРА
 * Проверяет, нужен ли скроллбар для текущего контента
 */
function checkAndEnableScrollbar(container, sectionName) {
  try {
    // Для Vehicles всегда показываем скроллбар (много танков)
    if (sectionName === 'vehicles') {
      return; // Уже установлен через vehicles-active класс
    }

    // Получаем активную секцию
    const activeSection = document.querySelector(`#${sectionName}-section, .${sectionName}-section`);
    if (!activeSection || activeSection.classList.contains('hidden')) {

      return;
    }

    // Проверяем, превышает ли контент высоту контейнера
    const containerHeight = container.clientHeight;
    const contentHeight = activeSection.scrollHeight;
    const needsScrollbar = contentHeight > containerHeight;

    if (needsScrollbar) {
      // Контент больше контейнера - включаем скроллбар
      container.classList.add('auto-scroll');
    } else {
      // Контент помещается - скроллбар не нужен
      container.classList.remove('auto-scroll');
    }

    // Дополнительная проверка через ResizeObserver для динамического контента
    if (!container._scrollbarObserver) {
      container._scrollbarObserver = new ResizeObserver(() => {
        // Повторная проверка при изменении размеров (ускорено)
        setTimeout(() => checkAndEnableScrollbar(container, sectionName), 10);
      });
      container._scrollbarObserver.observe(activeSection);
    }

  } catch (error) {
    console.error(`❌ Error in checkAndEnableScrollbar for ${sectionName}:`, error);
    // В случае ошибки включаем скроллбар для безопасности
    container.classList.add('auto-scroll');
  }
}

/**
 * Оптимизированный метод для показа вкладки Vehicles
 */
function optimizedVehiclesShow() {


  const tankListElement = document.getElementById('tank-list');
  if (tankListElement) {


    // Принудительно показываем элемент
    tankListElement.style.display = 'grid';
    tankListElement.style.visibility = 'visible';
    tankListElement.style.opacity = '1';
    tankListElement.classList.remove('hidden', 'tank-list-hidden');
    tankListElement.classList.add('tank-list-visible');

    // МГНОВЕННО используем TankManager для корректного отображения
    import('../core/TankManager.js').then(module => {
      // ПРИНУДИТЕЛЬНЫЙ БЫСТРЫЙ РЕНДЕРИНГ
      module.tankManager.forceShowTankList();
    }).catch(error => {
      console.error('🚨 Failed to import TankManager:', error);
    });
  } else {
    console.error('🚨 Tank list element not found in optimizedVehiclesShow');
  }
}

/**
 * Обновить активную кнопку меню (экспортируемая функция для роутера)
 */
export function updateActiveMenuButton(menu) {
  console.log(`🔄 Router: Updating active menu button to ${menu}`);
  updateActiveClass(menu);
}

// NUCLEAR INSTANT MENU ACTIVE CLASS UPDATE - ABSOLUTE ZERO DELAYS
function updateActiveClass(menu) {
  console.log(`🎯 NUCLEAR: Updating active class to ${menu}`);

  // ЯДЕРНЫЙ подход - полное отключение всех transition и мгновенное обновление
  const allItems = document.querySelectorAll('.sidebar-menu-item');

  // Шаг 1: Принудительно отключаем ВСЕ transition и animation
  const style = document.createElement('style');
  style.id = 'instant-update-override';
  style.textContent = `
    body .sidebar-menu-item, body .sidebar-menu-item * {
      transition: none;
      animation: none;
      -webkit-transition: none;
      -moz-transition: none;
      -o-transition: none;
    }
  `;
  document.head.appendChild(style);

  // Шаг 2: Мгновенно убираем все активные состояния
  allItems.forEach(item => {
    item.classList.remove('active');
    item.style.transition = 'none';
    item.style.animation = 'none';
  });

  // Шаг 3: Принудительная перерисовка
  document.body.offsetHeight;

  // Шаг 4: Мгновенно добавляем активное состояние
  allItems.forEach(item => {
    const section = item.getAttribute('data-section');
    if (section === menu) {
      item.classList.add('active');
      console.log(`✅ NUCLEAR: Active class added to ${menu}`);
    }
  });

  // Шаг 5: Еще одна принудительная перерисовка
  document.body.offsetHeight;
  document.documentElement.offsetHeight;

  // Шаг 6: Удаляем временный стиль через микротаск для мгновенности
  setTimeout(() => {
    const tempStyle = document.getElementById('instant-update-override');
    if (tempStyle) tempStyle.remove();
  }, 0);
}

// HARVARD-LEVEL ИНИЦИАЛИЗАЦИЯ МЕНЮ
function initializeMenu() {
  // Initial activation based on saved or default menu
  const saved = localStorage.getItem('activeMenuItem') || 'overview';
  onMenuSelected(saved, true);
}

// HARVARD-LEVEL АВТОМАТИЧЕСКАЯ ПРОВЕРКА СКРОЛЛБАРА ПРИ ИЗМЕНЕНИИ КОНТЕНТА
function setupGlobalScrollbarWatcher() {
  try {
    // Наблюдаем за изменениями в основном контейнере
    const mainContainer = document.querySelector('.main-content-container');
    if (!mainContainer) {
      return;
    }

    if (mainContainer._globalScrollbarWatcher) {
      return;
    }

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' || mutation.type === 'attributes') {
          // Контент изменился - проверяем скроллбар
          const activeSection = mainContainer.className.match(/(\w+)-active/);
          if (activeSection) {
            const sectionName = activeSection[1];
            // Безопасная проверка функции
            setTimeout(() => {
              if (window.checkAndEnableScrollbar && typeof window.checkAndEnableScrollbar === 'function') {
                window.checkAndEnableScrollbar(mainContainer, sectionName);
              }
            }, 100);
          }
        }
      });
    });

    observer.observe(mainContainer, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style']
    });

    mainContainer._globalScrollbarWatcher = observer;

  } catch (error) {
    console.error('❌ Error in setupGlobalScrollbarWatcher:', error);
  }
}



/**
 * Инициализация обработчиков для кнопок Overview
 */
export function initializeOverviewHandlers() {
  try {
    // Обработчик для кнопки "Просмотр техники"
    const vehiclesCard = document.querySelector('[data-action="vehicles"]');
    if (vehiclesCard) {
      vehiclesCard.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('🚗 Overview: Navigating to vehicles');
        onMenuSelected('vehicles');
      });
    }

    // Обработчик для кнопки "Сравнение"
    const compareCard = document.querySelector('[data-action="compare"]');
    if (compareCard) {
      compareCard.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('⚖️ Overview: Navigating to compare');
        onMenuSelected('compare');
      });
    }



    // Обработчик для кнопки "Тактики"
    const tacticsCard = document.querySelector('[data-action="tactics"]');
    if (tacticsCard) {
      tacticsCard.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('🎯 Overview: Navigating to tactics');
        onMenuSelected('tactics');
      });
    }

    console.log('✅ Overview handlers initialized');
  } catch (error) {
    console.error('❌ Error initializing overview handlers:', error);
  }
}

// Экспортируем функции для глобального использования
window.showSection = showSection;
window.onMenuSelected = onMenuSelected;
window.checkAndEnableScrollbar = checkAndEnableScrollbar;
window.setupGlobalScrollbarWatcher = setupGlobalScrollbarWatcher;
window.updateActiveClass = updateActiveClass;
window.initializeMenu = initializeMenu;

// HARVARD-LEVEL БЕЗОПАСНАЯ ИНИЦИАЛИЗАЦИЯ - ОТЛОЖЕННАЯ
// Инициализация будет вызвана из UltraAppInitializer после полной загрузки
