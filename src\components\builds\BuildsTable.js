// Таблица сборок танков с трехуровневой сортировкой

let currentBuildsData = [];
let currentTankName = null; // Отслеживаем имя текущего танка для предотвращения смешивания данных
const sortStates = {}; // Хранит состояния сортировки для каждой колонки

// Цвета для названий сборок (соответствуют цветам в примечаниях)
const BUILD_COLORS = {
  Штурм: '#ef4444', // Красный - как урон
  Универсальная: '#22c55e', // Зеленый - как вклад/помощь
  Снайперская: '#3b82f6', // Синий - как обзор
  Разведка: '#8b5cf6', // Фиолетовый - как эффективность
  'Активная разведка': '#ec4899', // Розовый - как точность
};

export function renderBuildsTable(tankObject) {
  console.log('🔧 BuildsTable: renderBuildsTable called with:', tankObject?.name);

  // Ищем элемент таблицы сборок
  const tableBody = document.querySelector('#builds-table-body');
  console.log('🔧 BuildsTable: Found tableBody:', !!tableBody);

  if (!tableBody) {
    console.error('🚨 BuildsTable: Table body not found! Looking for #builds-table-body');
    return;
  }

  // ПРИНУДИТЕЛЬНАЯ ОЧИСТКА таблицы и данных
  tableBody.innerHTML = '';

  // КРИТИЧЕСКАЯ ОЧИСТКА: Если танк изменился, полностью очищаем все данные
  if (currentTankName !== tankObject?.name) {
    console.log(`🧹 BuildsTable: Tank changed from ${currentTankName} to ${tankObject?.name} - FULL DATA RESET`);
    currentBuildsData = [];
    currentTankName = null;
    // Сбрасываем состояние сортировки при смене танка
    Object.keys(sortStates).forEach(key => {
      sortStates[key] = 'default';
    });
  }

  // КРИТИЧЕСКАЯ ПРОВЕРКА: Проверяем данные танка
  if (!tankObject || !tankObject.name) {
    console.error('🚨 BuildsTable: Invalid tank object');
    tableBody.innerHTML =
      '<tr><td colspan="13" style="color: white; padding: 20px; text-align: center;">Ошибка: некорректные данные танка</td></tr>';
    return;
  }

  const buildsArray = Array.isArray(tankObject.bestBuilds) ? tankObject.bestBuilds : [];
  console.log(`🔧 BuildsTable: Tank ${tankObject.name} has ${buildsArray.length} builds`);

  if (buildsArray.length === 0) {
    console.log('🔧 BuildsTable: No builds found for tank - showing empty table');
    tableBody.innerHTML = '<tr><td colspan="13" style="color: rgba(255,255,255,0.6); padding: 20px; text-align: center; font-style: italic;">Сборки для этого танка пока не добавлены</td></tr>';
    return;
  }

  // ВАЖНО: Проверяем смену танка и сбрасываем состояние
  if (currentTankName !== tankObject.name) {
    console.log(`🔧 BuildsTable: Tank changed from ${currentTankName} to ${tankObject.name} - resetting sort state`);
    // Сбрасываем состояние сортировки при смене танка
    Object.keys(sortStates).forEach(key => {
      sortStates[key] = 'default';
    });
  }

  // Сохраняем данные ТОЛЬКО для текущего танка
  currentBuildsData = [...buildsArray];
  currentTankName = tankObject.name;

  // Инициализируем сортировку
  initializeSorting();

  // Рендерим таблицу
  renderTable(currentBuildsData);

  console.log(`✅ BuildsTable: Builds table rendered successfully for ${tankObject.name}`);
}

/**
 * Принудительная очистка всех данных сборок
 * Используется при смене танка для предотвращения утечки данных
 */
export function clearBuildsData() {
  console.log('🧹 BuildsTable: Clearing all builds data');

  // Очищаем все глобальные переменные
  currentBuildsData = [];
  currentTankName = null;

  // Сбрасываем состояние сортировки
  Object.keys(sortStates).forEach(key => {
    sortStates[key] = 'default';
  });

  // Очищаем таблицу
  const tableBody = document.querySelector('#builds-table-body');
  if (tableBody) {
    tableBody.innerHTML = '';
  }

  console.log('✅ BuildsTable: All data cleared');
}

// Инициализация обработчиков сортировки
function initializeSorting() {
  const headers = document.querySelectorAll('.builds-table-daisy th');

  headers.forEach((header, index) => {
    // Пропускаем первую колонку (название сборки)
    if (index === 0) return;

    const columnKey = getColumnKey(index);
    if (!columnKey) return;

    // Инициализируем состояние сортировки
    sortStates[columnKey] = 'default';

    // Удаляем старые обработчики
    header.removeEventListener('click', handleSort);

    // Добавляем новый обработчик
    header.addEventListener('click', () => handleSort(columnKey, header));
  });
}

// Получение ключа колонки по индексу
function getColumnKey(index) {
  const keys = [
    'name',
    'efficiency',
    'contribution',
    'damage',
    'assist',
    'dpm',
    'spread',
    'dynSpread',
    'aimTime',
    'viewRange',
    'dispersion',
    'cv',
    'accuracy',
  ];
  return keys[index];
}

// Обработчик сортировки без визуальных индикаторов
function handleSort(columnKey, _headerElement) {
  // КРИТИЧЕСКАЯ ПРОВЕРКА: Убеждаемся что данные принадлежат текущему танку
  if (!currentBuildsData || currentBuildsData.length === 0) {
    console.error('🚨 BuildsTable: No builds data for sorting');
    return;
  }

  console.log(`🔧 BuildsTable: Sorting column ${columnKey} for tank ${currentTankName}`);

  // Сбрасываем состояния других колонок
  Object.keys(sortStates).forEach(key => {
    if (key !== columnKey) {
      sortStates[key] = 'default';
    }
  });

  // Переключаем состояние текущей колонки: default -> asc -> desc -> default
  switch (sortStates[columnKey]) {
    case 'default':
      sortStates[columnKey] = 'asc';
      break;
    case 'asc':
      sortStates[columnKey] = 'desc';
      break;
    case 'desc':
      sortStates[columnKey] = 'default';
      break;
  }

  // Сортируем данные
  const sortedData = sortData(currentBuildsData, columnKey, sortStates[columnKey]);

  // Перерендериваем таблицу
  renderTable(sortedData);
}

// Функция сортировки данных
function sortData(data, columnKey, sortState) {
  if (sortState === 'default') {
    // Возвращаем исходный порядок
    return [...currentBuildsData];
  }

  return [...data].sort((a, b) => {
    let valueA = a[columnKey];
    let valueB = b[columnKey];

    // Обработка null/undefined значений
    if (valueA === null && valueB === null) return 0;
    if (valueA === null) return 1;
    if (valueB === null) return -1;

    // Преобразуем в числа если возможно
    const numA = parseFloat(valueA);
    const numB = parseFloat(valueB);

    if (!isNaN(numA) && !isNaN(numB)) {
      valueA = numA;
      valueB = numB;
    }

    let result = 0;
    if (valueA < valueB) result = -1;
    else if (valueA > valueB) result = 1;

    return sortState === 'asc' ? result : -result;
  });
}

// Рендеринг таблицы с DaisyUI классами
function renderTable(buildsData) {
  const tableBody = document.querySelector('#builds-table-body');
  if (!tableBody) return;

  const rowsHTML = buildsData
    .map(build => {
      // Получаем цвет для названия сборки
      const buildColor = BUILD_COLORS[build.name] || '#60a5fa'; // Дефолтный цвет если не найден

      return `
      <tr class="transition-all duration-300">
        <td class="text-left font-semibold">
          <a class="build-link link" href="#" onclick="return false;" style="color: ${buildColor};">${build.name}</a>
        </td>
        <td class="text-center">${build.efficiency?.toFixed(1) ?? 'N/A'}</td>
        <td class="text-center">${build.contribution ?? 'N/A'}</td>
        <td class="text-center">${build.damage ?? 'N/A'}</td>
        <td class="text-center">${build.assist ?? 'N/A'}</td>
        <td class="text-center">${build.dpm ?? 'N/A'}</td>
        <td class="text-center">${build.spread?.toFixed(3) ?? 'N/A'}</td>
        <td class="text-center">${build.dynSpread?.toFixed(3) ?? 'N/A'}</td>
        <td class="text-center">${build.aimTime?.toFixed(1) ?? 'N/A'}</td>
        <td class="text-center">${build.viewRange?.toFixed(0) ?? 'N/A'}</td>
        <td class="text-center">${build.dispersion?.toFixed(2) ?? 'N/A'}</td>
        <td class="text-center">${build.cv?.toFixed(1) ?? 'N/A'}</td>
        <td class="text-center">${build.accuracy?.toFixed(1) ?? 'N/A'}</td>
      </tr>
    `;
    })
    .join('');

  tableBody.innerHTML = rowsHTML;
}
