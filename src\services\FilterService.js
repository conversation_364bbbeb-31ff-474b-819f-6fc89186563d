// src/filters.js
// Centralised filter utilities used across the application

import {
  RUSSIAN_TO_INTERNAL_TYPE_MAP,
  INTERNAL_TO_RUSSIAN_TYPE_MAP,
  TANK_TYPE_SORT_ORDER as TANK_TYPE_ORDER,
  ENGLISH_TO_INTERNAL_TYPE_MAP,
  INTERNAL_TO_ENGLISH_TYPE_MAP,
  COUNTRY_ALIASES,
  normalizeCountry
} from '../utils/constants.js';

/**
 * Check if a tank satisfies the active country and category filters.
 *
 * @param {Object} tank                Tank object from state.allTanks
 * @param {Object} filters             { country: string, type: string }
 * @returns {boolean}                  True if the tank matches all filters
 */
export function matchesFilters(tank, filters) {
  // Compare countries using normalized aliases to bridge UI values (e.g., "international")
  // with dataset keys (e.g., "International") and ignore case.
  const filterCountry = normalizeCountry(filters.country);
  const tankCountry = normalizeCountry(tank.country);

  if (filterCountry && filterCountry !== 'all' && tankCountry !== filterCountry) {
    return false;
  }

  // Category / type filter – compare internal identifiers
  const tankInternalType =
    RUSSIAN_TO_INTERNAL_TYPE_MAP[tank.type] || ENGLISH_TO_INTERNAL_TYPE_MAP[tank.type];
  if (filters.type && filters.type !== 'all' && tankInternalType !== filters.type) {
    return false;
  }

  // Tier filter – проверяем уровень танка
  if (filters.tiers && filters.tiers.length > 0 && filters.tiers.length < 5) {
    if (!tank.tier || !filters.tiers.includes(tank.tier)) {
      return false;
    }
  }

  return true;
}

// Используем унифицированную систему кэширования
import { filterCache } from '../utils/CacheMigrationAdapter.js';

/**
 * Produce a sorted list of tanks that satisfy the current filters and search query.
 * Оптимизированная версия с кэшированием и улучшенными алгоритмами
 *
 * @param {Object} state         Global reactive state object
 * @param {TankSearchIndex} searchIndex  Trie-based search index instance
 * @returns {Array<Object>}      Array of tank objects ready for rendering
 */
export function getFilteredTanks(state, searchIndex) {
  const { selectedCountry, selectedCategory, searchQuery, selectedTiers, allTanks } = state;

  // Создаем ключ кэша с учетом Tier фильтра
  const tiersKey = selectedTiers ? selectedTiers.join(',') : 'all';
  const cacheKey = `${selectedCountry}_${selectedCategory}_${searchQuery}_${tiersKey}_${allTanks.length}`;

  // Проверяем кэш через унифицированную систему
  const cached = filterCache.getFilteredTanks(cacheKey);
  if (cached) {
    return cached;
  }

  let filtered;

  if (searchQuery) {
    // Get tanks whose names match the query first, then apply filters
    const searchResults = searchIndex ? Array.from(searchIndex.search(searchQuery)) : [];
    filtered = searchResults.filter(tank =>
      matchesFilters(tank, { country: selectedCountry, type: selectedCategory })
    );
  } else {
    // No search term – simply filter all tanks
    // Оптимизация: используем более эффективную фильтрацию
    if (selectedCountry === 'all' && selectedCategory === 'all' && (!selectedTiers || selectedTiers.length === 5)) {
      filtered = allTanks.slice(); // Просто копируем массив
    } else {
      filtered = allTanks.filter(tank =>
        matchesFilters(tank, { country: selectedCountry, type: selectedCategory, tiers: selectedTiers })
      );
    }
  }

  // Sort primarily by type (Russian abbreviation order) and secondarily by name
  filtered.sort((a, b) => {
    const orderA = TANK_TYPE_ORDER[a.type] ?? 999;
    const orderB = TANK_TYPE_ORDER[b.type] ?? 999;

    if (orderA !== orderB) return orderA - orderB;
    return a.name.localeCompare(b.name);
  });

  // Кэшируем результат через унифицированную систему
  filterCache.setFilteredTanks(cacheKey, filtered);

  return filtered;
}

/**
 * Очистка кэша фильтрации
 */
export function clearFilterCache() {
  filterCache.clearFilterCache();
  console.log('🧹 Filter cache cleared');
}

/**
 * Получение статистики кэша
 */
export function getFilterCacheStats() {
  return filterCache.getFilterCacheStats();
}
