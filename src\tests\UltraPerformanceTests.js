/**
 * УЛЬТРА-ТЕСТЫ ПРОИЗВОДИТЕЛЬНОСТИ
 * Комплексное тестирование всех оптимизаций
 */

class UltraPerformanceTests {
  constructor() {
    this.results = new Map();
    this.benchmarks = new Map();
    this.thresholds = {
      appInitTime: 2000,      // 2 секунды
      searchTime: 50,         // 50мс
      filterTime: 30,         // 30мс
      renderTime: 16,         // 16мс (60fps)
      memoryUsage: 50,        // 50MB
      cacheHitRate: 0.8       // 80%
    };
  }

  /**
   * ЗАПУСК ВСЕХ ТЕСТОВ ПРОИЗВОДИТЕЛЬНОСТИ
   */
  async runAllTests() {
    const startTime = performance.now();
    
    const tests = [
      this.testAppInitialization(),
      this.testSearchPerformance(),
      this.testFilterPerformance(),
      this.testRenderPerformance(),
      this.testMemoryUsage(),
      this.testCacheEfficiency(),
      this.testCompareSystemPerformance(),
      this.testResponsiveness()
    ];
    
    const results = await Promise.allSettled(tests);
    const totalTime = performance.now() - startTime;
    
    this.generateReport(results, totalTime);
    return this.results;
  }

  /**
   * ТЕСТ ИНИЦИАЛИЗАЦИИ ПРИЛОЖЕНИЯ
   */
  async testAppInitialization() {
    const testName = 'App Initialization';
    const startTime = performance.now();
    
    try {
      // Симулируем инициализацию
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const initTime = performance.now() - startTime;
      const passed = initTime < this.thresholds.appInitTime;
      
      this.results.set(testName, {
        passed,
        time: initTime,
        threshold: this.thresholds.appInitTime,
        score: this.calculateScore(initTime, this.thresholds.appInitTime, true)
      });
      
      console.log(`✅ ${testName}: ${initTime.toFixed(2)}ms`);
    } catch (error) {
      this.results.set(testName, { passed: false, error: error.message });
      console.error(`❌ ${testName} failed:`, error);
    }
  }

  /**
   * ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ ПОИСКА
   */
  async testSearchPerformance() {
    const testName = 'Search Performance';
    
    try {
      // Создаем тестовые данные
      const testTanks = this.generateTestTanks(1000);
      
      // Импортируем поисковый движок
      const { ultraSearchEngine } = await import('../utils/UltraSearchEngine.js');
      ultraSearchEngine.buildIndex(testTanks);
      
      // Тестируем различные запросы
      const queries = ['T-34', 'Tiger', 'IS', 'Leopard', 'M1A2'];
      const times = [];
      
      for (const query of queries) {
        const startTime = performance.now();
        ultraSearchEngine.search(query);
        times.push(performance.now() - startTime);
      }
      
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const passed = avgTime < this.thresholds.searchTime;
      
      this.results.set(testName, {
        passed,
        time: avgTime,
        threshold: this.thresholds.searchTime,
        score: this.calculateScore(avgTime, this.thresholds.searchTime, true)
      });
      
      console.log(`✅ ${testName}: ${avgTime.toFixed(2)}ms avg`);
    } catch (error) {
      this.results.set(testName, { passed: false, error: error.message });
      console.error(`❌ ${testName} failed:`, error);
    }
  }

  /**
   * ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ ФИЛЬТРАЦИИ
   */
  async testFilterPerformance() {
    const testName = 'Filter Performance';
    
    try {
      const testTanks = this.generateTestTanks(1000);
      const { ultraSearchEngine } = await import('../utils/UltraSearchEngine.js');
      ultraSearchEngine.buildIndex(testTanks);
      
      const filters = [
        { country: 'USSR', type: 'all', level: 'all' },
        { country: 'all', type: 'СТ', level: 'all' },
        { country: 'Germany', type: 'ТТ', level: 'all' },
        { country: 'all', type: 'all', level: 'X' }
      ];
      
      const times = [];
      
      for (const filter of filters) {
        const startTime = performance.now();
        ultraSearchEngine.filter(filter);
        times.push(performance.now() - startTime);
      }
      
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const passed = avgTime < this.thresholds.filterTime;
      
      this.results.set(testName, {
        passed,
        time: avgTime,
        threshold: this.thresholds.filterTime,
        score: this.calculateScore(avgTime, this.thresholds.filterTime, true)
      });
      
      console.log(`✅ ${testName}: ${avgTime.toFixed(2)}ms avg`);
    } catch (error) {
      this.results.set(testName, { passed: false, error: error.message });
      console.error(`❌ ${testName} failed:`, error);
    }
  }

  /**
   * ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ РЕНДЕРИНГА
   */
  async testRenderPerformance() {
    const testName = 'Render Performance';
    
    try {
      const { ultraRenderer } = await import('../utils/UltraRenderer.js');
      
      // Создаем тестовый контейнер
      const container = document.createElement('div');
      container.style.width = '1000px';
      container.style.height = '600px';
      document.body.appendChild(container);
      
      const startTime = performance.now();
      
      // Тестируем батчированные обновления
      for (let i = 0; i < 100; i++) {
        const element = document.createElement('div');
        ultraRenderer.batchDOMUpdate(element, {
          styles: { width: '100px', height: '100px' },
          content: { text: `Item ${i}` }
        });
      }
      
      // Ждем выполнения батча
      await new Promise(resolve => setTimeout(resolve, 50));
      
      const renderTime = performance.now() - startTime;
      const passed = renderTime < this.thresholds.renderTime * 10; // Умножаем на 10 для батча
      
      document.body.removeChild(container);
      
      this.results.set(testName, {
        passed,
        time: renderTime,
        threshold: this.thresholds.renderTime * 10,
        score: this.calculateScore(renderTime, this.thresholds.renderTime * 10, true)
      });
      
      console.log(`✅ ${testName}: ${renderTime.toFixed(2)}ms`);
    } catch (error) {
      this.results.set(testName, { passed: false, error: error.message });
      console.error(`❌ ${testName} failed:`, error);
    }
  }

  /**
   * ТЕСТ ИСПОЛЬЗОВАНИЯ ПАМЯТИ
   */
  async testMemoryUsage() {
    const testName = 'Memory Usage';
    
    try {
      const initialMemory = performance.memory?.usedJSHeapSize || 0;
      
      // Создаем нагрузку
      const testData = this.generateTestTanks(5000);
      const { ultraSearchEngine } = await import('../utils/UltraSearchEngine.js');
      ultraSearchEngine.buildIndex(testData);
      
      // Ждем стабилизации
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const finalMemory = performance.memory?.usedJSHeapSize || 0;
      const memoryUsed = (finalMemory - initialMemory) / 1024 / 1024; // MB
      
      const passed = memoryUsed < this.thresholds.memoryUsage;
      
      this.results.set(testName, {
        passed,
        memory: memoryUsed,
        threshold: this.thresholds.memoryUsage,
        score: this.calculateScore(memoryUsed, this.thresholds.memoryUsage, true)
      });
      
      console.log(`✅ ${testName}: ${memoryUsed.toFixed(2)}MB`);
    } catch (error) {
      this.results.set(testName, { passed: false, error: error.message });
      console.error(`❌ ${testName} failed:`, error);
    }
  }

  /**
   * ТЕСТ ЭФФЕКТИВНОСТИ КЭША
   */
  async testCacheEfficiency() {
    const testName = 'Cache Efficiency';
    
    try {
      const { ultraSearchEngine } = await import('../utils/UltraSearchEngine.js');
      const testTanks = this.generateTestTanks(500);
      ultraSearchEngine.buildIndex(testTanks);
      
      // Выполняем повторяющиеся запросы
      const queries = ['T-34', 'Tiger', 'IS'];
      
      // Первый проход (заполняем кэш)
      queries.forEach(query => ultraSearchEngine.search(query));
      
      // Второй проход (используем кэш)
      queries.forEach(query => ultraSearchEngine.search(query));
      
      const metrics = ultraSearchEngine.getMetrics();
      const hitRate = metrics.hitRate || 0;
      const passed = hitRate >= this.thresholds.cacheHitRate;
      
      this.results.set(testName, {
        passed,
        hitRate,
        threshold: this.thresholds.cacheHitRate,
        score: this.calculateScore(hitRate, this.thresholds.cacheHitRate, false)
      });
      
      console.log(`✅ ${testName}: ${(hitRate * 100).toFixed(1)}% hit rate`);
    } catch (error) {
      this.results.set(testName, { passed: false, error: error.message });
      console.error(`❌ ${testName} failed:`, error);
    }
  }

  /**
   * ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ СРАВНЕНИЯ
   */
  async testCompareSystemPerformance() {
    const testName = 'Compare System Performance';
    
    try {
      const startTime = performance.now();
      
      // Симулируем добавление 8 танков в сравнение
      const testTanks = this.generateTestTanks(8);
      
      // Симулируем генерацию таблицы сравнения
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const compareTime = performance.now() - startTime;
      const passed = compareTime < 100; // 100мс для генерации таблицы
      
      this.results.set(testName, {
        passed,
        time: compareTime,
        threshold: 100,
        score: this.calculateScore(compareTime, 100, true)
      });
      
      console.log(`✅ ${testName}: ${compareTime.toFixed(2)}ms`);
    } catch (error) {
      this.results.set(testName, { passed: false, error: error.message });
      console.error(`❌ ${testName} failed:`, error);
    }
  }

  /**
   * ТЕСТ ОТЗЫВЧИВОСТИ ИНТЕРФЕЙСА
   */
  async testResponsiveness() {
    const testName = 'UI Responsiveness';
    
    try {
      const startTime = performance.now();
      
      // Симулируем быстрые действия пользователя
      for (let i = 0; i < 10; i++) {
        await new Promise(resolve => setTimeout(resolve, 1));
      }
      
      const responseTime = performance.now() - startTime;
      const passed = responseTime < 50; // 50мс для 10 действий
      
      this.results.set(testName, {
        passed,
        time: responseTime,
        threshold: 50,
        score: this.calculateScore(responseTime, 50, true)
      });
      
      console.log(`✅ ${testName}: ${responseTime.toFixed(2)}ms`);
    } catch (error) {
      this.results.set(testName, { passed: false, error: error.message });
      console.error(`❌ ${testName} failed:`, error);
    }
  }

  /**
   * ГЕНЕРАЦИЯ ТЕСТОВЫХ ДАННЫХ
   */
  generateTestTanks(count) {
    const countries = ['USSR', 'Germany', 'USA', 'China', 'France'];
    const types = ['ЛТ', 'СТ', 'ТТ', 'ПТ-САУ', 'САУ'];
    const levels = [6, 7, 8, 9, 10];
    
    return Array.from({ length: count }, (_, i) => ({
      name: `Test Tank ${i}`,
      country: countries[i % countries.length],
      type: types[i % types.length],
      level: levels[i % levels.length],
      characteristics: {
        hp: 1000 + Math.random() * 2000,
        damage: 200 + Math.random() * 500,
        penetration: 150 + Math.random() * 200
      }
    }));
  }

  /**
   * РАСЧЕТ ОЦЕНКИ
   */
  calculateScore(actual, threshold, lowerIsBetter) {
    if (lowerIsBetter) {
      return Math.max(0, Math.min(100, (threshold / actual) * 100));
    } else {
      return Math.max(0, Math.min(100, (actual / threshold) * 100));
    }
  }

  /**
   * ГЕНЕРАЦИЯ ОТЧЕТА
   */
  generateReport(results, totalTime) {
    console.log('\n🎯 ULTRA PERFORMANCE TEST RESULTS');
    console.log('=====================================');
    
    let passedTests = 0;
    let totalScore = 0;
    
    for (const [testName, result] of this.results) {
      if (result.passed) {
        passedTests++;
        console.log(`✅ ${testName}: PASSED (Score: ${result.score?.toFixed(1) || 'N/A'})`);
      } else {
        console.log(`❌ ${testName}: FAILED`);
      }
      
      if (result.score) {
        totalScore += result.score;
      }
    }
    
    const avgScore = totalScore / this.results.size;
    const passRate = (passedTests / this.results.size) * 100;
    
    console.log('\n📊 SUMMARY:');
    console.log(`Tests Passed: ${passedTests}/${this.results.size} (${passRate.toFixed(1)}%)`);
    console.log(`Average Score: ${avgScore.toFixed(1)}/100`);
    console.log(`Total Test Time: ${totalTime.toFixed(2)}ms`);
    
    // Определяем общую оценку
    let grade = 'F';
    if (avgScore >= 90) grade = 'A+';
    else if (avgScore >= 80) grade = 'A';
    else if (avgScore >= 70) grade = 'B';
    else if (avgScore >= 60) grade = 'C';
    else if (avgScore >= 50) grade = 'D';
    
    console.log(`Overall Grade: ${grade}`);
    console.log('=====================================\n');
  }
}

// Создаем и экспортируем экземпляр
export const ultraPerformanceTests = new UltraPerformanceTests();

export default ultraPerformanceTests;
