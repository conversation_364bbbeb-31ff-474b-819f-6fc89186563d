/**
 * PREMIUM UI STYLES
 * Современный дизайн для Overview и Settings
 */

/* ===== OVERVIEW STYLES ===== */

/* Убираем outline со всех интерактивных элементов */
button,
input,
select,
textarea,
.modern-button,
.settings-button,
.dropdown-selected,
.modern-toggle,
.action-card,
.feature-card,
.setting-item {
  outline: none;
  -webkit-tap-highlight-color: transparent;
}

/* Убираем outline только с интерактивных элементов при всех состояниях */
button:focus,
button:active,
input:focus,
input:active,
.modern-button:focus,
.modern-button:active,
.settings-button:focus,
.settings-button:active,
.dropdown-selected:focus,
.dropdown-selected:active {
  outline: none;
}

/* Emoji support */
.emoji-icon {
  font-family: 'Inter', 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', 'Segoe UI Symbol', sans-serif;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}

.overview-hero {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.08) 0%,
    rgba(59, 130, 246, 0.06) 25%,
    rgba(139, 92, 246, 0.08) 50%,
    rgba(236, 72, 153, 0.06) 75%,
    rgba(245, 158, 11, 0.08) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 32px;
  padding: 4rem 2rem;
  margin-bottom: 3rem;
  text-align: center;
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Animated Background */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.hero-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.15), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(59, 130, 246, 0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(16, 185, 129, 0.15), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(139, 92, 246, 0.1), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: particlesFloat 20s linear infinite;
}

/* Метеориты для Hero окна - истинно бесконечные без циклов */
.hero-background::after {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background:
    radial-gradient(1px 1px at 33% 25%, rgba(255, 255, 255, 0.9), transparent 3px),
    radial-gradient(1px 1px at 66% 50%, rgba(96, 165, 250, 0.7), transparent 3px),
    radial-gradient(1px 1px at 50% 75%, rgba(52, 211, 153, 0.6), transparent 3px),
    radial-gradient(1px 1px at 75% 33%, rgba(139, 92, 246, 0.5), transparent 3px),
    radial-gradient(1px 1px at 25% 66%, rgba(236, 72, 153, 0.6), transparent 3px);
  background-size: 420px 210px, 280px 140px, 380px 190px, 240px 120px, 350px 175px;
  background-repeat: repeat;
  animation: heroMeteorFlow 120s linear infinite;
  z-index: 2;
  opacity: 1;
  pointer-events: none;
}

.hero-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.6;
  animation: orbFloat 8s ease-in-out infinite;
}

.hero-gradient-orb-1 {
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  top: -100px;
  left: -100px;
  animation-delay: 0s;
}

.hero-gradient-orb-2 {
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.25) 0%, transparent 70%);
  top: 50%;
  right: -75px;
  animation-delay: -3s;
}

.hero-gradient-orb-3 {
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.2) 0%, transparent 70%);
  bottom: -60px;
  left: 30%;
  animation-delay: -6s;
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.25) 0%,
    rgba(16, 185, 129, 0.25) 50%,
    rgba(139, 92, 246, 0.25) 100%);
  border: 2px solid rgba(59, 130, 246, 0.4);
  border-radius: 20px;
  margin-bottom: 1.5rem;
  color: #3b82f6;
  box-shadow:
    0 10px 30px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg,
    #3b82f6 0%,
    #10b981 25%,
    #8b5cf6 50%,
    #ec4899 75%,
    #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  line-height: 1.2;
  text-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.hero-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg,
    #3b82f6 0%,
    #10b981 50%,
    #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  text-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Quick Actions */
.overview-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.action-card {
  /* Базовые стили (background, border, border-radius, position, overflow) наследуются из critical.css */
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Единый эффект частиц для всех окон - как в AI Efficiency */
.action-card::before,
.feature-card::before,
.settings-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.25), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(59, 130, 246, 0.35), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(16, 185, 129, 0.25), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(139, 92, 246, 0.2), transparent),
    radial-gradient(3px 3px at 60px 20px, rgba(236, 72, 153, 0.15), transparent),
    radial-gradient(2px 2px at 110px 60px, rgba(245, 158, 11, 0.2), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: particlesFloat 20s linear infinite;
  z-index: 1;
  opacity: 1;
  pointer-events: none;
}

/* Метеориты для всех окон Overview - истинно бесконечные без циклов */
.action-card::after,
.feature-card::after,
.settings-category::after {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background:
    radial-gradient(1px 1px at 33% 25%, rgba(255, 255, 255, 0.9), transparent 3px),
    radial-gradient(1px 1px at 66% 50%, rgba(96, 165, 250, 0.7), transparent 3px),
    radial-gradient(1px 1px at 50% 75%, rgba(52, 211, 153, 0.6), transparent 3px),
    radial-gradient(1px 1px at 75% 33%, rgba(139, 92, 246, 0.5), transparent 3px),
    radial-gradient(1px 1px at 25% 66%, rgba(236, 72, 153, 0.6), transparent 3px);
  background-size: 300px 150px, 350px 180px, 280px 140px, 320px 160px, 290px 145px;
  background-repeat: repeat;
  animation: meteorFlow 125s linear infinite;
  z-index: 2;
  opacity: 1;
  pointer-events: none;
}

/* Hover эффекты убраны для окон Settings */

.action-card.coming-soon {
  opacity: 0.7;
  cursor: pointer;
  user-select: none;
  position: relative;
  overflow: hidden;
}

/* Убран убогий золотой блик */

/* Hover эффект для coming-soon */
.action-card.coming-soon:hover {
  opacity: 0.9;
  transform: translateY(-4px);
  box-shadow:
    0 15px 40px rgba(245, 158, 11, 0.2),
    0 0 0 1px rgba(245, 158, 11, 0.3);
}

.action-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.25) 0%,
    rgba(16, 185, 129, 0.25) 50%,
    rgba(139, 92, 246, 0.25) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  color: #3b82f6;
  margin-bottom: 1rem;
  box-shadow:
    0 4px 15px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.emoji-icon {
  font-size: 24px;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(16, 185, 129, 0.15) 50%,
    rgba(139, 92, 246, 0.15) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover эффекты для иконок убраны */

.action-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.action-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  line-height: 1.5;
}

.coming-soon-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg,
    #f59e0b 0%,
    #f97316 50%,
    #ef4444 100%);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow:
    0 4px 15px rgba(245, 158, 11, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Features Grid */
.overview-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.feature-card {
  /* Базовые стили (background, border, border-radius, position, overflow) наследуются из critical.css */
  padding: 2rem;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

/* Стили для feature-card::before теперь объединены выше */

/* Hover стили для feature-card теперь объединены выше */

.feature-card.coming-soon {
  opacity: 0.6;
  cursor: pointer;
  user-select: none;
  position: relative;
  overflow: hidden;
}

/* Убран убогий золотой блик */

/* Hover эффект для feature-card coming-soon */
.feature-card.coming-soon:hover {
  opacity: 0.8;
  transform: translateY(-4px);
  box-shadow:
    0 15px 40px rgba(245, 158, 11, 0.2),
    0 0 0 1px rgba(245, 158, 11, 0.3);
}

/* Hover анимации для coming-soon иконок */
.action-card.coming-soon:hover .action-icon,
.feature-card.coming-soon:hover .feature-icon {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

/* Hover эффект для Coming Soon badge */
.action-card.coming-soon:hover .coming-soon-badge,
.feature-card.coming-soon:hover .coming-soon-badge {
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
  transform: scale(1.02);
  transition: all 0.3s ease;
}

.feature-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.2) 0%,
    rgba(59, 130, 246, 0.2) 50%,
    rgba(139, 92, 246, 0.2) 100%);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 16px;
  color: #10b981;
  margin-bottom: 1.5rem;
  box-shadow:
    0 8px 25px rgba(16, 185, 129, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.feature-icon.emoji-icon {
  font-size: 32px;
}

.feature-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.75rem;
}

.feature-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  line-height: 1.6;
}



/* ===== TACTICS PREMIUM STYLES ===== */

.tactics-premium-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.tactics-premium-header {
  text-align: center;
  margin-bottom: 4rem;
}

.tactics-hero {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.tactics-hero-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(168, 85, 247, 0.2));
  border-radius: 50%;
  color: #8b5cf6;
  animation: float 3s ease-in-out infinite;
  box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
}

.tactics-hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin: 0;
  background: linear-gradient(135deg, #8b5cf6, #a855f7, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(139, 92, 246, 0.3);
}

.tactics-hero-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  max-width: 600px;
  line-height: 1.6;
}

/* Interface Preview */
.tactics-interface-preview {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  margin-bottom: 4rem;
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.9) 0%,
    rgba(30, 41, 59, 0.9) 100%);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(139, 92, 246, 0.3);
  position: relative;
  overflow: hidden;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
}

.tactics-interface-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(139, 92, 246, 0.1),
    transparent);
  animation: shimmer 4s ease-in-out infinite;
}

/* Left Panel Preview */
.tactics-panel-preview {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.panel-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
}

.panel-header h3 {
  font-size: 1.1rem;
  color: #ffffff;
  margin: 0;
  font-weight: 600;
}

.panel-tools {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.tool-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.tool-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: default;
  user-select: none;
}

.tool-icon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

/* Map Preview */
.tactics-map-preview {
  background: rgba(0, 0, 0, 0.4);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(139, 92, 246, 0.2);
  position: relative;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
}

.map-title {
  font-size: 1.1rem;
  color: #ffffff;
  font-weight: 600;
}

.map-controls {
  display: flex;
  gap: 0.5rem;
}

.map-btn {
  padding: 0.5rem 1rem;
  background: rgba(139, 92, 246, 0.2);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.8rem;
  transition: all 0.3s ease;
  cursor: default;
  user-select: none;
}

/* Hover эффект для map-btn убран */

.map-canvas {
  height: 300px;
  background: linear-gradient(135deg,
    rgba(34, 197, 94, 0.1) 0%,
    rgba(22, 163, 74, 0.1) 50%,
    rgba(21, 128, 61, 0.1) 100%);
  position: relative;
  overflow: hidden;
}

.map-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.demo-tank {
  position: absolute;
  font-size: 1.5rem;
  animation: pulse 2s ease-in-out infinite;
}

.tank-1 {
  top: 30%;
  left: 20%;
}

.tank-2 {
  top: 60%;
  right: 25%;
}

.demo-arrow {
  position: absolute;
  top: 45%;
  left: 50%;
  font-size: 2rem;
  animation: bounce 2s ease-in-out infinite;
}

.demo-position {
  position: absolute;
  top: 70%;
  left: 30%;
  font-size: 1.2rem;
  animation: glow 3s ease-in-out infinite;
}

/* Coming Soon Overlay */
.coming-soon-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  z-index: 10;
  transition: all 0.3s ease;
  cursor: default;
  user-select: none;
}

/* Hover эффект для coming-soon-overlay */
.coming-soon-overlay:hover {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
}

.coming-soon-badge-corner {
  position: absolute;
  top: 2rem;
  right: 2rem;
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  color: #ffffff;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  animation: pulse 2s ease-in-out infinite;
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
  transition: all 0.3s ease;
  cursor: default;
  user-select: none;
}

/* Hover эффект для coming-soon-badge-corner */
.coming-soon-badge-corner:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(245, 158, 11, 0.6);
  animation-play-state: paused;
}

/* Features Grid */
.tactics-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 4rem; /* Увеличиваем отступ сверху */
}

.feature-card-premium {
  background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.8) 0%,
    rgba(51, 65, 85, 0.6) 100%);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  backdrop-filter: blur(10px);
}

.feature-card-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(139, 92, 246, 0.1),
    transparent);
  transition: left 0.6s ease;
}

/* Hover эффекты для feature-card-premium убраны */

.feature-icon-premium {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
  animation: float 3s ease-in-out infinite;
}

.feature-card-premium h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.feature-card-premium p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .tactics-interface-preview {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .tactics-panel-preview {
    order: 2;
  }
}

@media (max-width: 768px) {
  .tactics-premium-container {
    padding: 0 1rem;
  }

  .tactics-hero-title {
    font-size: 2.5rem;
  }

  .tactics-features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .coming-soon-badge-corner {
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Coming Soon Styles */
.coming-soon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.coming-soon-content {
  text-align: center;
  max-width: 600px;
  padding: 3rem;
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.8) 0%,
    rgba(30, 41, 59, 0.8) 100%);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.coming-soon-icon {
  margin-bottom: 2rem;
  color: #8b5cf6;
}

.coming-soon-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.coming-soon-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2.5rem;
}

.coming-soon-features {
  display: grid;
  gap: 1rem;
  margin-bottom: 2.5rem;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.feature-item svg {
  color: #8b5cf6;
  flex-shrink: 0;
}

.coming-soon-progress {
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #8b5cf6, #a855f7);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* ===== SETTINGS STYLES ===== */

.settings-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.settings-header {
  text-align: center;
  margin-bottom: 3rem;
}

.settings-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(168, 85, 247, 0.2));
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-radius: 20px;
  margin-bottom: 1.5rem;
  color: #8b5cf6;
}

.settings-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #8b5cf6, #a855f7, #c084fc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.settings-subtitle {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 500px;
  margin: 0 auto;
}

.settings-categories {
  display: grid;
  grid-template-columns: minmax(600px, 1fr) minmax(600px, 1fr); /* Возвращаем оригинальную ширину окон */
  grid-template-rows: auto auto auto; /* Три ряда: верхний (2 больших окна), нижний (2 маленьких), кнопка */
  gap: 2rem; /* Возвращаем оригинальный большой отступ между окнами */
  margin-top: 2rem; /* Добавляем отступ сверху от заголовка */
  margin-bottom: 1rem; /* Уменьшаем отступ снизу чтобы окно версии было ближе */
  justify-content: center; /* Центрируем по горизонтали */
  align-content: center; /* Центрируем по вертикали */
}

/* Расположение согласно запросу: Внешний вид (левая) | Производительность (правая) | Нижний ряд (2 окна) */
.settings-categories > .settings-category:nth-child(1) {
  grid-column: 1; /* Внешний вид - левая колонка */
  grid-row: 1;
}

.settings-categories > .settings-category:nth-child(3) {
  grid-column: 2; /* Производительность - правая колонка */
  grid-row: 1;
}

.settings-categories > .settings-category:nth-child(2) {
  grid-column: 1; /* Нижний ряд - левое окно */
  grid-row: 2;
}

.settings-categories > .settings-category:nth-child(4) {
  grid-column: 2; /* Нижний ряд - правое окно */
  grid-row: 2;
}

.settings-category {
  /* Базовые стили (background, border, border-radius, backdrop-filter, position, overflow) наследуются из critical.css */
  padding: 1rem; /* Еще более компактный вид */
  z-index: 1;
  /* Убираем margin-bottom - отступы управляются через grid gap */
}

/* Стили для settings-category::before теперь объединены выше */

.category-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem; /* Уменьшаем отступ снизу */
  padding-bottom: 0.5rem; /* Уменьшаем padding снизу */
  border-bottom: 1px solid rgba(139, 92, 246, 0.1);
  cursor: default;
  pointer-events: none;
}

.category-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border-radius: 10px;
  color: #8b5cf6;
  cursor: default;
  pointer-events: none;
}

.category-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  cursor: default;
  pointer-events: none;
}

/* Убираем любые hover эффекты с заголовков категорий Settings */
.category-header:hover,
.category-icon:hover,
.category-title:hover {
  background: none;
  box-shadow: none;
  transform: none;
  border: none;
  outline: none;
}

.settings-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem; /* Уменьшаем gap между элементами */
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem; /* Уменьшаем gap */
  padding: 0.75rem; /* Уменьшаем padding для компактности */
  background: rgba(139, 92, 246, 0.05);
  border: 1px solid rgba(139, 92, 246, 0.1);
  border-radius: 12px;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
  margin-bottom: 0.4rem; /* Уменьшаем отступ снизу */
}

/* Hover эффект для setting-item (окна настроек типа "Тема интерфейса") */
.setting-item:hover {
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.08) 0%,
    rgba(59, 130, 246, 0.06) 100%);
  border-color: rgba(139, 92, 246, 0.25);
  box-shadow:
    0 4px 15px rgba(139, 92, 246, 0.15),
    0 0 0 1px rgba(139, 92, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.setting-info {
  flex: 1;
}

.setting-label {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
  display: block;
}

.setting-description {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
}

.setting-control {
  flex-shrink: 0;
}

/* Custom Dropdown */
.custom-dropdown {
  position: relative;
  min-width: 180px;
  z-index: 10;
}

/* Когда dropdown открыт, убираем z-index с родителя чтобы не создавать stacking context */
.custom-dropdown.active {
  z-index: auto;
}

/* Специальные z-index для каждого dropdown по порядку */
.custom-dropdown[data-dropdown="theme"] {
  z-index: 5000;
}

.custom-dropdown[data-dropdown="font-size"] {
  z-index: 4000;
}

.custom-dropdown[data-dropdown="language"] {
  z-index: 3000;
}

.custom-dropdown[data-dropdown="region"] {
  z-index: 2000;
}

/* Когда dropdown активен, поднимаем его еще выше */
body .custom-dropdown.active,
body .custom-dropdown.dropdown-active {
  z-index: 999999;
}

.dropdown-selected {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  user-select: none; /* Убираем выделение текста */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Hover эффект для dropdown-selected убран */

/* Активное состояние dropdown */
.custom-dropdown .dropdown-options.open + .dropdown-selected,
.custom-dropdown:has(.dropdown-options.open) .dropdown-selected {
  border-color: rgba(139, 92, 246, 0.5);
  background: rgba(30, 41, 59, 0.95);
  box-shadow:
    0 0 0 1px rgba(139, 92, 246, 0.3),
    0 4px 15px rgba(139, 92, 246, 0.2);
}

.dropdown-text {
  color: #ffffff;
  font-size: 0.875rem;
  font-weight: 500;
}

.dropdown-icon {
  width: 19.2px;
  height: 19.2px;
  color: rgba(255, 255, 255, 0.6);
  transition: transform 0.3s ease;
}

/* Скрываем оригинальные dropdown-options - используем только портал */
body .dropdown-options:not(.dropdown-portal) {
  display: none;
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
}

/* Убираем стили для .dropdown-options.open - используем только портал */
body .dropdown-options.open:not(.dropdown-portal) {
  display: none;
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
}

/* Портал для dropdown - ПОЛНОСТЬЮ зафиксированный */
body .dropdown-portal {
  position: fixed;
  z-index: 999999;
  background: linear-gradient(135deg,
    rgb(15, 23, 42) 0%,
    rgb(30, 41, 59) 100%);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 8px;
  padding: 0.3rem 0; /* Минимальный padding для компактности */
  backdrop-filter: blur(20px);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 30px rgba(139, 92, 246, 0.4);
  max-height: 250px;
  min-height: 50px;
  overflow-y: auto;
  overflow-x: hidden;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: none;
  pointer-events: auto;
  box-sizing: border-box;
  /* КРИТИЧНО: предотвращаем движение при скролле */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  will-change: auto;
  /* Принудительно фиксируем позицию */
  top: var(--dropdown-top);
  left: var(--dropdown-left);
  width: var(--dropdown-width);
  /* Дополнительная защита от движения */
  contain: layout style paint;
  isolation: isolate;
}

body .dropdown-portal.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Стили для опций в портале - наследуют от .dropdown-option */
.dropdown-portal .dropdown-option {
  /* Все стили наследуются от базового .dropdown-option */
}

/* Красивый скроллбар для dropdown портала */
.dropdown-portal::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dropdown-portal::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 4px;
  margin: 2px;
}

.dropdown-portal::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.8) 0%,
    rgba(168, 85, 247, 0.8) 100%);
  border-radius: 4px;
  transition: all 0.2s ease;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

/* Hover эффект для scrollbar убран */

.dropdown-portal::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox скроллбар */
.dropdown-portal {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.8) rgba(15, 23, 42, 0.5);
}

/* Блокирующий overlay для dropdown */
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2147483646;
  background: transparent;
  pointer-events: auto;
}

/* Анимация появления dropdown */
@keyframes dropdownSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Убираем pointer-events с элементов под активным dropdown */
.custom-dropdown.dropdown-active ~ .setting-item,
.custom-dropdown.dropdown-active ~ * .setting-item,
.settings-category:has(.dropdown-active) .setting-item:not(:has(.dropdown-active)) {
  pointer-events: none;
}

/* Возвращаем pointer-events для активного dropdown */
.custom-dropdown.dropdown-active {
  pointer-events: auto;
  position: relative;
  z-index: 999999;
}

/* Убираем затемнение фона - оно мешает */

.dropdown-option {
  padding: 0.75rem 1rem;
  color: rgba(255, 255, 255, 0.95);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  /* Убираем transition полностью чтобы избежать дергания */
  border-radius: 6px;
  margin: 3px 0.25rem; /* Увеличенный отступ между функциями */
  position: relative;
  background: transparent;
  display: block;
  width: calc(100% - 0.5rem);
  box-sizing: border-box;
  user-select: none; /* Убираем выделение текста */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  pointer-events: auto; /* Убеждаемся что клики работают */
  z-index: 1;
}

/* Hover эффект для dropdown-option убран */

.dropdown-option:active {
  background: rgba(139, 92, 246, 0.4);
  /* Убираем все анимации */
}

/* Выделение активного элемента */
.dropdown-option.selected {
  background: rgba(114, 84, 228, 0.25);
  color: #ffffff;
  font-weight: 600;
  border-left: 3px solid #8b5cf6; /* Левый контур для активной функции */
  padding-left: calc(1rem - 3px); /* Компенсируем border чтобы текст не сдвигался */
}

.dropdown-option.selected::after {
  content: '✓';
  position: absolute;
  right: 1rem;
  color: #8b5cf6;
  font-weight: bold;
  font-size: 1rem;
}

/* Modern Toggle */
.modern-toggle {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 28px;
  z-index: 1;
}

.modern-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
  outline: none;
}

.modern-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(51, 65, 85, 0.9) 0%,
    rgba(71, 85, 105, 0.9) 100%);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Hover эффект для modern-toggle убран */

.modern-toggle-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 3px;
  bottom: 3px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

.modern-toggle input:checked + .modern-toggle-slider {
  background: linear-gradient(135deg,
    #8b5cf6 0%,
    #a855f7 100%);
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow:
    inset 0 2px 4px rgba(139, 92, 246, 0.2),
    0 2px 8px rgba(139, 92, 246, 0.3);
}

.modern-toggle input:checked + .modern-toggle-slider:before {
  transform: translateX(24px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.modern-toggle input:focus + .modern-toggle-slider {
  box-shadow:
    0 0 0 3px rgba(139, 92, 246, 0.2),
    inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Modern Button */
.modern-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.2) 0%,
    rgba(220, 38, 38, 0.2) 100%);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  color: #ef4444;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  box-shadow:
    0 2px 8px rgba(239, 68, 68, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  outline: none;
}

/* Hover эффект для modern-button убран */

.modern-button:active {
  transform: translateY(0);
  box-shadow:
    0 2px 8px rgba(239, 68, 68, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Центральная кнопка сохранения */
.settings-save-center {
  grid-column: 1 / -1; /* Занимает всю ширину */
  display: flex;
  justify-content: center;
  margin-top: 1rem; /* Уменьшаем для симметрии */
  padding-top: 1rem; /* Уменьшаем для симметрии */
  border-top: 1px solid rgba(139, 92, 246, 0.1);
}

.settings-save-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem; /* Увеличиваем размер как было при столбике */
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
  border: 1px solid transparent;
  border-radius: 12px; /* Увеличиваем радиус как было при столбике */
  font-size: 1rem; /* Увеличиваем размер шрифта как было при столбике */
  font-weight: 600; /* Увеличиваем вес шрифта как было при столбике */
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
  position: relative;
  z-index: 1;
  outline: none;
}

/* Hover эффект для settings-save-button убран */

.settings-save-button:active {
  transform: translateY(0);
}

/* Settings Footer - упрощенный */
.settings-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1.5rem; /* Уменьшаем для красивого вида */
  background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.6) 0%,
    rgba(51, 65, 85, 0.4) 100%);
  border: 1px solid rgba(139, 92, 246, 0.15);
  border-radius: 16px;
  width: fit-content; /* Размер точно по содержимому без лишних отступов */
  margin: 0 auto; /* Центрируем */
}

.settings-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.version-info,
.build-info {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.settings-actions {
  display: flex;
  gap: 1rem;
}

.settings-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
  position: relative;
  z-index: 1;
  outline: none;
}

.settings-button.secondary {
  background: linear-gradient(135deg,
    rgba(51, 65, 85, 0.7) 0%,
    rgba(71, 85, 105, 0.7) 100%);
  color: rgba(255, 255, 255, 0.9);
  border-color: rgba(139, 92, 246, 0.2);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Hover эффект для settings-button.secondary убран */

.settings-button.primary {
  background: linear-gradient(135deg,
    #8b5cf6 0%,
    #a855f7 100%);
  color: #ffffff;
  box-shadow:
    0 4px 15px rgba(139, 92, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Hover эффект для settings-button.primary убран */

.settings-button:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-stats {
    gap: 2rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .overview-actions {
    grid-template-columns: 1fr;
  }

  .overview-features {
    grid-template-columns: 1fr;
  }

  .settings-title {
    font-size: 2rem;
  }

  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .setting-control {
    width: 100%;
  }

  .custom-dropdown {
    width: 100%;
  }

  .settings-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .settings-actions {
    width: 100%;
    justify-content: stretch;
  }

  .settings-button {
    flex: 1;
    justify-content: center;
  }
}

/* ===== NOTIFICATIONS ===== */

.coming-soon-notification {
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notification-icon {
  font-size: 1.7rem;
}

.notification-text h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.notification-text p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
}

/* ===== ANIMATIONS MOVED TO BASE/ANIMATIONS.CSS ===== */

.overview-hero {
  animation: fadeInUp 0.6s ease-out;
}

.action-card {
  animation: fadeInScale 0.2s ease-out; /* Ускоренная анимация */
  animation-fill-mode: both;
}

/* Убираем задержки анимации - все карточки показываются сразу */
.action-card:nth-child(1) { animation-delay: 0s; }
.action-card:nth-child(2) { animation-delay: 0s; }
.action-card:nth-child(3) { animation-delay: 0s; }

.feature-card {
  animation: fadeInUp 0.2s ease-out; /* Ускоренная анимация */
  animation-fill-mode: both;
}

/* Убираем задержки анимации - все карточки показываются сразу */
.feature-card:nth-child(1) { animation-delay: 0s; }
.feature-card:nth-child(2) { animation-delay: 0s; }
.feature-card:nth-child(3) { animation-delay: 0s; }
.feature-card:nth-child(4) { animation-delay: 0s; }

.settings-category {
  animation: fadeInUp 0.2s ease-out; /* Ускоренная анимация */
  animation-fill-mode: both;
}

/* Убираем задержки анимации - все окна показываются сразу */
.settings-category:nth-child(1) { animation-delay: 0s; }
.settings-category:nth-child(2) { animation-delay: 0s; }
.settings-category:nth-child(3) { animation-delay: 0s; }
.settings-category:nth-child(4) { animation-delay: 0s; }

/* ===== ADVANCED EFFECTS ===== */

/* Убираем белые glassmorphism эффекты - используем только частицы */

/* Убираем hover эффект с settings-category */

/* Gradient borders */
.action-card,
.feature-card,
.settings-category {
  background-clip: padding-box;
}

/* Floating animation moved to base/animations.css */

.hero-icon {
  animation: float 3s ease-in-out infinite;
}

.settings-icon {
  animation: float 4s ease-in-out infinite;
}

/* Pulse effect moved to base/animations.css */

.coming-soon-badge {
  animation: pulse 2s ease-in-out infinite;
}

/* Shimmer effect moved to base/animations.css */

/* Дублирующие стили .stat-number удалены - используются из основного определения выше */

/* Smooth transitions for all interactive elements */
.action-card,
.feature-card,
.setting-item,
.dropdown-selected,
.modern-button,
.settings-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover effects для Overview окон */
.action-card:hover {
  transform: translateY(-6px) scale(1.02);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow:
    0 25px 50px rgba(59, 130, 246, 0.15),
    0 0 0 1px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.feature-card:hover {
  transform: translateY(-6px) scale(1.02);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow:
    0 25px 50px rgba(59, 130, 246, 0.15),
    0 0 0 1px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Hover эффекты для иконок в Overview окнах */
.action-card:hover .emoji-icon,
.feature-card:hover .emoji-icon {
  transform: scale(1.1);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Micro-interactions for buttons */
.modern-button:active,
.settings-button:active {
  transform: translateY(1px) scale(0.98);
}

/* Loading states moved to base/animations.css */

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-radius: 50%;
  border-top-color: #8b5cf6;
  animation: spin 1s ease-in-out infinite;
}

/* Убираем focus states - они создают нежелательные контуры */

/* Smooth scrolling for settings categories */
.settings-categories {
  scroll-behavior: smooth;
}

/* Enhanced backdrop blur */
.overview-hero,
.settings-category {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
}

/* New Hero Animations */
@keyframes particlesFloat {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(-150px) translateY(-100px); }
}

@keyframes orbFloat {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-20px) scale(1.1); }
}

@keyframes iconGlow {
  0% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Истинно бесконечная анимация метеоритов для Hero окна - без видимых циклов */
@keyframes heroMeteorFlow {
  0% {
    transform: translateX(0) translateY(0);
  }
  100% {
    transform: translateX(-33.33%) translateY(-33.33%);
  }
}



/* Истинно бесконечная анимация метеоритов для других окон - без видимых циклов */
@keyframes meteorFlow {
  0% {
    transform: translateX(0) translateY(0);
  }
  100% {
    transform: translateX(-33.33%) translateY(-33.33%);
  }
}

/* Additional animations - дублирующая анимация удалена */

/* All duplicate animations moved to base/animations.css */



/* Notification animations */
.coming-soon-notification {
  animation: slideInRight 0.4s ease-out;
}

/* Interactive elements glow - объединено с основными hover эффектами выше */

/* Smooth color transitions */
.hero-title,
.settings-title {
  transition: all 0.3s ease;
}

/* Performance optimizations */
.action-card,
.feature-card,
.setting-item {
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms;
    animation-iteration-count: 1;
    transition-duration: 0.01ms;
  }

  .hero-icon,
  .settings-icon,
  .coming-soon-badge {
    animation: none;
  }
}
