/**
 * УПРОЩЕННЫЙ МЕНЕДЖЕР ПРОИЗВОДИТЕЛЬНОСТИ
 * Эффективная система управления производительностью без избыточной сложности
 */

import { PERFORMANCE_THRESHOLDS } from '../utils/performance-constants.js';
import { domCache } from '../utils/CacheMigrationAdapter.js';

class PerformanceManager {
  constructor() {
    this.isInitialized = false;
    this.optimizationLevel = this.calculateOptimizationLevel();
    this.frameId = null;

    // Простая защита от частых оптимизаций
    this.lastOptimization = 0;
    this.optimizationCooldown = 3000; // 3 секунды

    // Упрощенные метрики
    this.metrics = {
      frameTime: 0,
      memoryUsage: 0,
      optimizationsCount: 0,
      lastUpdate: Date.now()
    };

    // Используем унифицированную систему кэширования
  }

  /**
   * БЫСТРАЯ ИНИЦИАЛИЗАЦИЯ СИСТЕМЫ
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Применение базовых оптимизаций
      this.applyBasicOptimizations();

      // Настройка легкого мониторинга
      this.setupLightweightMonitoring();

      this.isInitialized = true;
      console.log('✅ Performance manager initialized');

    } catch (error) {
      console.error('🚨 Performance manager initialization failed:', error);
    }
  }

  /**
   * ЛЕГКИЙ МОНИТОРИНГ ПРОИЗВОДИТЕЛЬНОСТИ
   */
  setupLightweightMonitoring() {
    // Простой мониторинг памяти
    if ('memory' in performance) {
      setInterval(() => {
        this.metrics.memoryUsage = performance.memory.usedJSHeapSize;
        this.metrics.lastUpdate = Date.now();

        // Проверяем критический порог памяти
        if (this.metrics.memoryUsage > PERFORMANCE_THRESHOLDS.MEMORY_THRESHOLD) {
          this.triggerOptimization('high_memory');
        }
      }, 15000); // 15 секунд - реже проверяем
    }

    // Периодическая легкая очистка
    setInterval(() => {
      this.performLightCleanup();
    }, 180000); // 3 минуты
  }

  /**
   * ПРИМЕНЕНИЕ БАЗОВЫХ ОПТИМИЗАЦИЙ К DOM
   */
  applyBasicOptimizations() {
    // Кэшируем и оптимизируем основные элементы
    const tankList = this.getCachedElement('#tank-list');
    if (tankList) {
      tankList.style.contain = 'layout style paint';
      tankList.style.contentVisibility = 'auto';
    }

    // Оптимизация скроллинга для основного контейнера
    const mainContainer = this.getCachedElement('.main-content-container');
    if (mainContainer) {
      mainContainer.style.scrollBehavior = 'smooth';
      mainContainer.style.overflowAnchor = 'none';
    }
  }

  /**
   * КЭШИРОВАННЫЙ ДОСТУП К DOM ЭЛЕМЕНТАМ (делегируется к унифицированной системе)
   */
  getCachedElement(selector) {
    return domCache.getCachedElement(selector);
  }

  /**
   * УПРОЩЕННЫЙ ТРИГГЕР ОПТИМИЗАЦИЙ
   */
  triggerOptimization(type) {
    const now = Date.now();

    // Проверяем cooldown
    if (now - this.lastOptimization < this.optimizationCooldown) {
      return;
    }

    this.lastOptimization = now;
    this.metrics.optimizationsCount++;

    switch (type) {
      case 'high_memory':
        this.performMemoryOptimization();
        break;
      case 'dom_cleanup':
        this.performDOMCleanup();
        break;
      default:
        this.performLightCleanup();
    }
  }

  /**
   * ОПТИМИЗАЦИЯ ПАМЯТИ
   */
  performMemoryOptimization() {
    // Memory optimization triggered

    // Очистка DOM кэша (используем унифицированную систему)
    domCache.clearElementCache();

    // Оптимизированная очистка неиспользуемых изображений
    const allImages = document.getElementsByTagName('img');
    for (let i = 0; i < allImages.length; i++) {
      const img = allImages[i];
      if (img.style.display === 'none' && img.src && !img.dataset.important) {
        img.removeAttribute('src');
      }
    }

    // Принудительная сборка мусора (если доступна)
    if (window.gc) {
      window.gc();
    }
  }

  /**
   * ОЧИСТКА DOM
   */
  performDOMCleanup() {
    // DOM cleanup triggered

    // Удаляем временные стили
    const tempStyles = document.querySelectorAll('style[data-temp="true"]');
    tempStyles.forEach(style => style.remove());

    // Очищаем кэш элементов (используем унифицированную систему)
    domCache.clearElementCache();
  }

  /**
   * ЛЕГКАЯ ОЧИСТКА (менее агрессивная для стабильности)
   */
  performLightCleanup() {
    // НЕ очищаем DOM кэш при легкой очистке - это ломает функциональность
    // domCache.clearElementCache();
    console.log('🧹 Light cleanup performed (DOM cache preserved)');
  }









  /**
   * РАСЧЕТ УРОВНЯ ОПТИМИЗАЦИИ
   */
  calculateOptimizationLevel() {
    const deviceMemory = navigator.deviceMemory || 4;
    const hardwareConcurrency = navigator.hardwareConcurrency || 4;
    const connection = navigator.connection;

    // Учитываем качество соединения
    let connectionScore = 1;
    if (connection) {
      connectionScore = connection.effectiveType === '4g' ? 1 :
                      connection.effectiveType === '3g' ? 0.7 : 0.5;
    }

    const score = (deviceMemory / 8) * (hardwareConcurrency / 8) * connectionScore;

    if (score >= 0.8) return 'high';
    if (score >= 0.5) return 'medium';
    return 'low';
  }

  /**
   * ПОЛУЧЕНИЕ ОТЧЕТА О ПРОИЗВОДИТЕЛЬНОСТИ
   */
  getPerformanceReport() {
    return {
      optimizationLevel: this.optimizationLevel,
      isInitialized: this.isInitialized,
      metrics: { ...this.metrics },
      memoryInfo: this.getMemoryInfo()
    };
  }

  /**
   * ПОЛУЧЕНИЕ ИНФОРМАЦИИ О ПАМЯТИ
   */
  getMemoryInfo() {
    if ('memory' in performance) {
      return {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
      };
    }
    return null;
  }

  /**
   * ОЧИСТКА РЕСУРСОВ
   */
  destroy() {
    if (this.frameId) {
      cancelAnimationFrame(this.frameId);
    }
    domCache.clearElementCache();
    this.isInitialized = false;
    this.metrics = { frameTime: 0, memoryUsage: 0, optimizationsCount: 0, lastUpdate: Date.now() };
  }
}

// Экспортируем синглтон
export const performanceManager = new PerformanceManager();

// Обратная совместимость
export const quantumPerformanceManager = performanceManager;

export default performanceManager;
