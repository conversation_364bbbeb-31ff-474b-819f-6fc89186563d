/* ========================================
   ULTRA-OPTIMIZED CSS IMPORTS
   ======================================== */

/* Базовые стили и переменные */
@import './ultra-variables.css';
@import './base/reset.css';
@import './critical.css';

/* Эффекты и анимации */
@import './base/animations.css';
@import './effects/glassmorphism.css';
@import './effects/rgb-animations.css';

/* Компоненты */
@import './components/sidebar.css';
@import './components/tank-list.css';
@import './components/tank-characteristics.css';
@import './components/tank-details.css';
@import './components/equipment.css';
@import './components/premium-ui.css';
@import './components/advanced-compare.css';
@import './components/ultra-compare.css';
@import './components/adaptive-compare.css';
@import './components/interactive-elements.css';
@import './components/filter-sort.css';
@import './components/responsive.css';
@import './components/daisyui-overrides.css';

/* Оптимизации и скроллбар */
@import './performance-optimizations.css';
@import './scrollbar-optimized.css';

/* Переменные перенесены в base/variables.css для консолидации */

html,
body {
  font-family: 'Inter', sans-serif;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  letter-spacing: 0.015em;
  font-weight: 400;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  background-color: #2a2155;
  background-image: radial-gradient(circle at 30% 20%, rgba(85, 64, 174, 0.5) 0%, transparent 45%),
                    radial-gradient(circle at 80% 80%, rgba(123, 31, 162, 0.4) 0%, transparent 50%);
  position: relative;
  min-height: 100vh;
  transition: none;
  /* Оптимизация для производительности */
  will-change: auto;
  contain: layout style;
}

#bg-gradient-layer {
  position: fixed;
  inset: 0;
  z-index: -20;
  pointer-events: none;
  background: radial-gradient(circle at top right, rgba(45, 35, 75, 0.4) 0%, rgba(35, 28, 60, 0.3) 30%, rgba(25, 20, 45, 0.2) 60%, rgba(15, 12, 30, 0.1) 100%);
}

#bg-pattern-layer {
  position: fixed;
  inset: 0;
  z-index: -10;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0.1;
}

.app-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  min-height: 100vh;
  position: relative;
  background-color: transparent;
  box-sizing: border-box;
  overflow-x: hidden;
  z-index: 10;
  transition: none;
  /* Оптимизация производительности */
  contain: layout style;
  will-change: auto;
}

/* СТИЛИ ПОЗИЦИОНИРОВАНИЯ КОНТЕЙНЕРА УПРАВЛЯЮТСЯ В scrollbar-new.css */

/* ========================================
   CONTENT SECTIONS
   ======================================== */

.content-section {
  width: 100%;
  height: auto;
  overflow-y: visible;
  min-height: 0;
  padding: 1rem;
  animation: fadeIn 0.3s ease-in-out;
}

#tank-list.content-section {
  height: auto;
  min-height: 200px;
  overflow: visible;
  display: grid;
}

.content-section.hidden {
  display: none;
  opacity: 0;
  visibility: hidden;
}

.content-section.tank-characteristics {
  min-height: calc(100vh + 500px);
}

.section-container {
  background: var(--glass-bg);
  border-radius: 1rem;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin: 0;
}

/* ========================================
   BUTTONS
   ======================================== */

.btn {
  display: inline-block;
  background-color: var(--accent-color);
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  text-align: center;
}

.btn:hover {
  background-color: #7c4bd6;
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

/* ========================================
   ANIMATIONS
   ======================================== */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ========================================
   CUSTOM UTILITY CLASSES (не дублирующие Tailwind)
   ======================================== */

/* Специфичные градиенты для проекта */
.bg-multi-gradient {
  background:
    radial-gradient(circle at 20% 20%, rgba(114,84,228,0.07) 0%, transparent 60%),
    radial-gradient(circle at 80% 30%, rgba(54,180,220,0.07) 0%, transparent 60%),
    radial-gradient(circle at 60% 80%, rgba(220,70,150,0.07) 0%, transparent 60%),
    radial-gradient(circle at 10% 90%, rgba(120,210,100,0.07) 0%, transparent 60%);
}

/* GPU-ускоренные трансформации */
.transform-gpu {
  transform: translateZ(0);
}

/* Dark theme styles */
.dark\:bg-none {
  background: none;
}

.dark\:bg-gm-dark-gradient {
  background: radial-gradient(
    circle at top right,
    #1f1543 0%,
    #150f2d 30%,
    #0c0920 60%,
    #050413 100%
  );
}

/* Удалены пустые CSS правила для pattern backgrounds */

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
  .section-container {
    padding: 1rem;
    border-radius: 0.75rem;
  }
}

@media (max-width: 480px) {
  .content-section {
    padding: 0.5rem;
  }

  .section-container {
    padding: 0.75rem;
  }
}

#flag-section.open {
  max-height: 1000px; /* Increased for testing type filters */
}

/* ========================================
   SETTINGS SAVE BUTTON STYLES
   ======================================== */

#save-settings-btn.has-changes {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border-color: #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  animation: pulse-save 2s infinite;
}

@keyframes pulse-save {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  }
  50% {
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.5);
  }
}

#save-settings-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
