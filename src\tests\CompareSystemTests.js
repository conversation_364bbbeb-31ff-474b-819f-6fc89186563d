/**
 * ТЕСТЫ ДЛЯ ОПТИМИЗИРОВАННОЙ СИСТЕМЫ СРАВНЕНИЯ
 */

export class CompareSystemTests {
  constructor() {
    this.testResults = [];
    this.startTime = 0;
    this.endTime = 0;
  }

  /**
   * ЗАПУСК ВСЕХ ТЕСТОВ
   */
  async runAllTests() {
    console.log('🧪 Запуск тестов системы сравнения...');
    this.startTime = performance.now();

    await this.testPerformance();
    await this.testUIResponsiveness();
    await this.testMemoryUsage();
    await this.testLogOptimization();
    await this.testComparisonInterface();

    this.endTime = performance.now();
    this.generateReport();
  }

  /**
   * ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ
   */
  async testPerformance() {
    const testName = 'Performance Test';
    const startTime = performance.now();

    try {
      // Тестируем добавление 8 танков
      if (window.advancedCompareSystem) {
        for (let i = 0; i < 8; i++) {
          const testTank = {
            name: `Test Tank ${i}`,
            level: 10,
            type: 'heavyTank',
            nation: 'ussr'
          };
          window.advancedCompareSystem.addTank(testTank);
        }

        // Тестируем обновление UI
        window.advancedCompareSystem.updateFloatingWindow();
        
        const endTime = performance.now();
        const duration = endTime - startTime;

        this.testResults.push({
          name: testName,
          status: duration < 100 ? 'PASS' : 'FAIL',
          duration: `${duration.toFixed(2)}ms`,
          details: `Добавление 8 танков и обновление UI за ${duration.toFixed(2)}ms`
        });

        // Очищаем после теста
        window.advancedCompareSystem.clearAll();
      } else {
        this.testResults.push({
          name: testName,
          status: 'FAIL',
          duration: '0ms',
          details: 'Система сравнения не найдена'
        });
      }
    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        duration: '0ms',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ ОТЗЫВЧИВОСТИ UI
   */
  async testUIResponsiveness() {
    const testName = 'UI Responsiveness Test';
    
    try {
      const floatingWindow = document.getElementById('compare-floating-window');
      const compareResults = document.getElementById('compare-results');
      
      let responsiveElements = 0;
      let totalElements = 0;

      // Проверяем наличие ключевых элементов
      const elements = [floatingWindow, compareResults];
      
      elements.forEach(element => {
        totalElements++;
        if (element) {
          responsiveElements++;
        }
      });

      const responsiveness = (responsiveElements / totalElements) * 100;

      this.testResults.push({
        name: testName,
        status: responsiveness >= 100 ? 'PASS' : 'PARTIAL',
        duration: 'N/A',
        details: `${responsiveElements}/${totalElements} элементов найдено (${responsiveness.toFixed(1)}%)`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        duration: 'N/A',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ ИСПОЛЬЗОВАНИЯ ПАМЯТИ
   */
  async testMemoryUsage() {
    const testName = 'Memory Usage Test';
    
    try {
      const initialMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
      
      // Создаем и удаляем множество танков для проверки утечек памяти
      if (window.advancedCompareSystem) {
        for (let cycle = 0; cycle < 10; cycle++) {
          for (let i = 0; i < 8; i++) {
            const testTank = {
              name: `Memory Test Tank ${cycle}-${i}`,
              level: 10,
              type: 'heavyTank',
              nation: 'ussr'
            };
            window.advancedCompareSystem.addTank(testTank);
          }
          window.advancedCompareSystem.clearAll();
        }
      }

      // Принудительная сборка мусора (если доступна)
      if (window.gc) {
        window.gc();
      }

      const finalMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
      const memoryDiff = finalMemory - initialMemory;

      this.testResults.push({
        name: testName,
        status: memoryDiff < 1000000 ? 'PASS' : 'WARN', // Менее 1MB прироста
        duration: 'N/A',
        details: `Изменение памяти: ${(memoryDiff / 1024).toFixed(2)} KB`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        duration: 'N/A',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ ОПТИМИЗАЦИИ ЛОГИРОВАНИЯ
   */
  async testLogOptimization() {
    const testName = 'Log Optimization Test';
    
    try {
      // Подсчитываем количество console.log в production режиме
      const originalLog = console.log;
      let logCount = 0;
      
      console.log = (...args) => {
        logCount++;
        originalLog.apply(console, args);
      };

      // Выполняем операции, которые могли бы логировать
      if (window.advancedCompareSystem) {
        const testTank = {
          name: 'Log Test Tank',
          level: 10,
          type: 'heavyTank',
          nation: 'ussr'
        };
        window.advancedCompareSystem.addTank(testTank);
        window.advancedCompareSystem.removeTankByName('Log Test Tank');
      }

      // Восстанавливаем оригинальный console.log
      console.log = originalLog;

      this.testResults.push({
        name: testName,
        status: logCount === 0 ? 'PASS' : 'INFO',
        duration: 'N/A',
        details: `Обнаружено ${logCount} вызовов console.log`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        duration: 'N/A',
        details: error.message
      });
    }
  }

  /**
   * ТЕСТ ИНТЕРФЕЙСА СРАВНЕНИЯ
   */
  async testComparisonInterface() {
    const testName = 'Comparison Interface Test';
    
    try {
      // Проверяем CSS стили для отображения 8 танков
      const comparisonTable = document.querySelector('.comparison-table-header');
      let interfaceScore = 0;
      let maxScore = 4;

      // Проверка 1: Наличие таблицы сравнения
      if (comparisonTable) {
        interfaceScore++;
      }

      // Проверка 2: Правильная grid структура
      const computedStyle = window.getComputedStyle(comparisonTable);
      if (computedStyle.display === 'grid') {
        interfaceScore++;
      }

      // Проверка 3: Адаптивные колонки
      const gridColumns = computedStyle.gridTemplateColumns;
      if (gridColumns && gridColumns.includes('repeat(8')) {
        interfaceScore++;
      }

      // Проверка 4: Overflow для скролла
      if (computedStyle.overflowX === 'auto') {
        interfaceScore++;
      }

      const percentage = (interfaceScore / maxScore) * 100;

      this.testResults.push({
        name: testName,
        status: percentage >= 75 ? 'PASS' : 'PARTIAL',
        duration: 'N/A',
        details: `Интерфейс: ${interfaceScore}/${maxScore} критериев (${percentage.toFixed(1)}%)`
      });

    } catch (error) {
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        duration: 'N/A',
        details: error.message
      });
    }
  }

  /**
   * ГЕНЕРАЦИЯ ОТЧЕТА
   */
  generateReport() {
    const totalDuration = this.endTime - this.startTime;
    const passedTests = this.testResults.filter(test => test.status === 'PASS').length;
    const totalTests = this.testResults.length;

    console.log('\n🎯 ОТЧЕТ О ТЕСТИРОВАНИИ СИСТЕМЫ СРАВНЕНИЯ');
    console.log('='.repeat(50));
    console.log(`⏱️  Общее время выполнения: ${totalDuration.toFixed(2)}ms`);
    console.log(`✅ Пройдено тестов: ${passedTests}/${totalTests}`);
    console.log(`📊 Успешность: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log('\n📋 ДЕТАЛИ ТЕСТОВ:');
    
    this.testResults.forEach((test, index) => {
      const statusIcon = {
        'PASS': '✅',
        'PARTIAL': '⚠️',
        'WARN': '⚠️',
        'FAIL': '❌',
        'ERROR': '💥',
        'INFO': 'ℹ️'
      }[test.status] || '❓';

      console.log(`${index + 1}. ${statusIcon} ${test.name}`);
      console.log(`   Статус: ${test.status}`);
      console.log(`   Время: ${test.duration}`);
      console.log(`   Детали: ${test.details}`);
      console.log('');
    });

    console.log('='.repeat(50));
  }
}

// Экспорт для использования в консоли
window.CompareSystemTests = CompareSystemTests;

// Автоматический запуск тестов в dev режиме
if (import.meta.env?.MODE === 'development') {
  window.runCompareTests = () => {
    const tests = new CompareSystemTests();
    tests.runAllTests();
  };
  
  console.log('🧪 Тесты системы сравнения загружены! Запустите: runCompareTests()');
}
