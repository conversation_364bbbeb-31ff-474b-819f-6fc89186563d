// Компонент слотов оборудования для танков

import { 
  STANDARD_EQUIPMENT, 
  SPECIAL_EQUIPMENT, 
  IMPROVED_EQUIPMENT, 
  EXPERIMENTAL_EQUIPMENT,
  EQUIPMENT_CATEGORIES,
  EQUIPMENT_TYPES 
} from '../../data/equipment.js';

// Объединенные данные оборудования
const ALL_EQUIPMENT = {
  ...STANDARD_EQUIPMENT,
  ...SPECIAL_EQUIPMENT,
  ...IMPROVED_EQUIPMENT,
  ...EXPERIMENTAL_EQUIPMENT
};

/**
 * Инициализация слотов оборудования для танка
 */
export function initializeEquipmentSlots(tankData) {
  const equipmentContainer = document.getElementById('equipment-slots');
  if (!equipmentContainer) {
    console.warn('Equipment container not found');
    return;
  }

  // Очищаем контейнер
  equipmentContainer.innerHTML = '';

  // Создаем слоты оборудования
  const slotsHTML = createEquipmentSlotsHTML(tankData);
  equipmentContainer.innerHTML = slotsHTML;

  // Инициализируем обработчики событий
  setupEquipmentHandlers();
}

/**
 * Создание HTML для слотов оборудования
 */
function createEquipmentSlotsHTML(tankData) {
  return `
    <div class="equipment-section">
      <div class="equipment-header">
        <h3>Оборудование</h3>
        <button class="btn-reset-equipment" onclick="resetEquipment()">
          Сбросить
        </button>
      </div>
      
      <div class="equipment-slots-container">
        ${createSlot(1, tankData)}
        ${createSlot(2, tankData)}
        ${createSlot(3, tankData)}
      </div>
      
      <div class="equipment-categories">
        ${createCategoriesHTML()}
      </div>
      
      <div class="equipment-list" id="equipment-list">
        ${createEquipmentListHTML()}
      </div>
    </div>
  `;
}

/**
 * Создание отдельного слота оборудования
 */
function createSlot(slotNumber, tankData) {
  return `
    <div class="equipment-slot" data-slot="${slotNumber}">
      <div class="slot-header">
        <span class="slot-number">${slotNumber}</span>
        <button class="slot-clear" onclick="clearSlot(${slotNumber})">×</button>
      </div>
      <div class="slot-content" id="slot-${slotNumber}">
        <div class="slot-placeholder">
          <span class="slot-icon">+</span>
          <span class="slot-text">Выберите оборудование</span>
        </div>
      </div>
    </div>
  `;
}

/**
 * Создание HTML для категорий оборудования
 */
function createCategoriesHTML() {
  return Object.entries(EQUIPMENT_CATEGORIES).map(([key, category]) => `
    <button class="category-filter ${key === 'firepower' ? 'active' : ''}" 
            data-category="${key}"
            onclick="filterEquipmentByCategory('${key}')">
      <span class="category-icon">${category.icon}</span>
      <span class="category-name">${category.name}</span>
    </button>
  `).join('');
}

/**
 * Создание списка доступного оборудования
 */
function createEquipmentListHTML() {
  return Object.entries(ALL_EQUIPMENT).map(([key, equipment]) => `
    <div class="equipment-item ${equipment.category}" 
         data-equipment="${key}"
         onclick="selectEquipment('${key}')">
      <div class="equipment-icon">
        <img src="${equipment.icon}" alt="${equipment.name}" loading="lazy">
      </div>
      <div class="equipment-info">
        <div class="equipment-name">${equipment.name}</div>
        <div class="equipment-type ${equipment.type}">${EQUIPMENT_TYPES[equipment.type].name}</div>
        <div class="equipment-description">${equipment.description}</div>
        <div class="equipment-cost">
          ${equipment.cost === 'bonds' ? 'Облигации' : `${equipment.cost.toLocaleString()} кредитов`}
        </div>
      </div>
    </div>
  `).join('');
}

/**
 * Настройка обработчиков событий
 */
function setupEquipmentHandlers() {
  // Обработчики уже созданы через onclick в HTML
  console.log('Equipment handlers initialized');
}

/**
 * Фильтрация оборудования по категории
 */
window.filterEquipmentByCategory = function(category) {
  // Обновляем активную категорию
  document.querySelectorAll('.category-filter').forEach(btn => {
    btn.classList.remove('active');
  });
  document.querySelector(`[data-category="${category}"]`).classList.add('active');

  // Фильтруем оборудование
  document.querySelectorAll('.equipment-item').forEach(item => {
    if (category === 'all' || item.classList.contains(category)) {
      item.style.display = 'flex';
    } else {
      item.style.display = 'none';
    }
  });
};

/**
 * Выбор оборудования
 */
window.selectEquipment = function(equipmentKey) {
  const equipment = ALL_EQUIPMENT[equipmentKey];
  if (!equipment) return;

  // Находим первый свободный слот
  for (let i = 1; i <= 3; i++) {
    const slot = document.getElementById(`slot-${i}`);
    if (slot.querySelector('.slot-placeholder')) {
      installEquipment(i, equipmentKey, equipment);
      break;
    }
  }
};

/**
 * Установка оборудования в слот
 */
function installEquipment(slotNumber, equipmentKey, equipment) {
  const slot = document.getElementById(`slot-${slotNumber}`);
  
  slot.innerHTML = `
    <div class="installed-equipment" data-equipment="${equipmentKey}">
      <div class="equipment-icon">
        <img src="${equipment.icon}" alt="${equipment.name}">
      </div>
      <div class="equipment-name">${equipment.name}</div>
      <div class="equipment-type ${equipment.type}">${EQUIPMENT_TYPES[equipment.type].name}</div>
    </div>
  `;

  // Обновляем характеристики танка
  updateTankCharacteristics();
}

/**
 * Очистка слота
 */
window.clearSlot = function(slotNumber) {
  const slot = document.getElementById(`slot-${slotNumber}`);
  slot.innerHTML = `
    <div class="slot-placeholder">
      <span class="slot-icon">+</span>
      <span class="slot-text">Выберите оборудование</span>
    </div>
  `;

  // Обновляем характеристики танка
  updateTankCharacteristics();
};

/**
 * Сброс всего оборудования
 */
window.resetEquipment = function() {
  for (let i = 1; i <= 3; i++) {
    window.clearSlot(i);
  }
};

/**
 * Получение установленного оборудования
 */
export function getInstalledEquipment() {
  const equipment = {};
  
  for (let i = 1; i <= 3; i++) {
    const slot = document.getElementById(`slot-${i}`);
    const installedEquipment = slot.querySelector('.installed-equipment');
    
    if (installedEquipment) {
      const equipmentKey = installedEquipment.dataset.equipment;
      equipment[`slot${i}`] = ALL_EQUIPMENT[equipmentKey];
    }
  }
  
  return equipment;
}

/**
 * Обновление характеристик танка с учетом оборудования
 */
function updateTankCharacteristics() {
  const installedEquipment = getInstalledEquipment();
  
  // Импортируем функцию обновления характеристик
  import('../tank-details/CharacteristicsTable.js').then(module => {
    import('../../store/state.js').then(stateModule => {
      const state = stateModule.state;
      if (state.selectedTank && state.selectedTank.characteristics) {
        module.updateCharacteristicsWithEquipment(
          state.selectedTank.characteristics,
          installedEquipment
        );
      }
    });
  });
}

/**
 * Экспорт функций для использования в других модулях
 */
export {
  createEquipmentSlotsHTML,
  createSlot,
  setupEquipmentHandlers,
  installEquipment,
  ALL_EQUIPMENT
};
