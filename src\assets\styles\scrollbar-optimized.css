/* ОПТИМИЗИРОВАННЫЙ СКРОЛЛБАР - ФИНАЛЬНАЯ ВЕРСИЯ */

/* ОСНОВНОЙ LAYOUT */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  overflow-y: hidden;
}

/* СТАБИЛЬНЫЙ КОНТЕЙНЕР С ОПТИМИЗАЦИЕЙ ПРОИЗВОДИТЕЛЬНОСТИ */
.main-content-container {
  position: fixed;
  top: 0;
  left: 250px;
  right: 0;
  bottom: 0;
  width: calc(100vw - 250px);
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  z-index: 5;
  opacity: 1;
  visibility: visible;
  background: transparent;

  /* FIREFOX СКРОЛЛБАР */
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.8) transparent;

  /* ОПТИМИЗАЦИЯ ПРОИЗВОДИТЕЛЬНОСТИ БЕЗ WILL-CHANGE */
  transform: translateZ(0);
  scroll-behavior: auto;
  contain: layout style;

  /* ПРИНУДИТЕЛЬНОЕ СОДЕРЖИМОЕ */
  padding-bottom: 1px;
}

/* WEBKIT СКРОЛЛБАР */
.main-content-container::-webkit-scrollbar {
  width: 12px;
  background: transparent;
}

.main-content-container::-webkit-scrollbar-track {
  background: transparent;
  border: none;
  box-shadow: none;
}

.main-content-container::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.8);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
  min-height: 20px;
  transition: none;
}

.main-content-container::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 1);
}

.main-content-container::-webkit-scrollbar-thumb:active {
  background: rgba(99, 102, 241, 1);
}

.main-content-container::-webkit-scrollbar-corner {
  background: transparent;
  display: none;
}

/* АНИМАЦИЯ ПОЯВЛЕНИЯ СПИСКА ТАНКОВ */
#tank-list {
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  will-change: opacity, transform;
}

/* СОСТОЯНИЯ АНИМАЦИИ */
#tank-list.fade-in {
  opacity: 0;
  transform: translateY(20px);
}

#tank-list.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* МГНОВЕННОЕ ПОЯВЛЕНИЕ БЕЗ АНИМАЦИИ */
#tank-list.instant-show {
  opacity: 1;
  transform: translateY(0);
  transition: none;
  display: grid;
  visibility: visible;
}

/* ОТКЛЮЧЕНИЕ ПЕРЕХОДОВ ВО ВРЕМЯ РЕНДЕРИНГА */
#tank-list.rendering {
  transition: none;
  will-change: auto;
}

/* ВКЛЮЧЕНИЕ ПЕРЕХОДОВ ПОСЛЕ РЕНДЕРИНГА */
#tank-list.ready-for-animation {
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* СКРЫТОЕ СОСТОЯНИЕ */
#tank-list.hidden {
  opacity: 0;
  transform: translateY(-10px);
}

/* ПРИНУДИТЕЛЬНОЕ СКРЫТИЕ СПИСКА ТАНКОВ */
#tank-list.force-hidden {
  display: none;
  visibility: hidden;
  opacity: 0;
  z-index: -1;
}

/* ЗАЩИТА ОТ ПЕРЕКРЫТИЯ ХАРАКТЕРИСТИК */
body[data-tank-page="true"] #tank-list {
  display: none;
  visibility: hidden;
  opacity: 0;
}

/* АНИМАЦИЯ ДЛЯ КАРТОЧЕК ТАНКОВ */
.tank-card {
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
  will-change: opacity, transform;
}

/* АНИМАЦИЯ ПОЯВЛЕНИЯ КАРТОЧЕК С ЗАДЕРЖКОЙ */
#tank-list.fade-in .tank-card {
  opacity: 0;
  transform: translateY(10px);
}

#tank-list.fade-in.visible .tank-card {
  opacity: 1;
  transform: translateY(0);
  transition-delay: calc(var(--card-index, 0) * 0.02s);
}

/* ПЛАВНОЕ ПОЯВЛЕНИЕ ФИЛЬТРОВ */
.filter-container {
  transition: opacity 0.3s ease-out;
}

.filter-container.fade-in {
  opacity: 0;
}

.filter-container.fade-in.visible {
  opacity: 1;
}

/* ПРЕДОТВРАЩЕНИЕ МОРГАНИЯ ПРИ ПЕРЕКЛЮЧЕНИИ ВКЛАДОК */
.content-section {
  transition: none;
}

.content-section.vehicles-section {
  transition: none;
}

/* МОБИЛЬНАЯ АДАПТАЦИЯ */
@media (max-width: 768px) {
  .main-content-container {
    left: 0;
    width: 100vw;
    -webkit-overflow-scrolling: touch;
  }
  
  .main-content-container::-webkit-scrollbar {
    width: 8px;
  }

  .sidebar {
    transform: translateX(-100%);
    will-change: auto;
    transition: transform 0.3s ease;
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }
}

/* СТАБИЛЬНОСТЬ СКРОЛЛИНГА */
.main-content-container[style*="overflow-y: auto"],
.main-content-container[style*="overflow-y: hidden"] {
  overflow-y: auto;
}

.main-content-container[style*="overflow-x: auto"],
.main-content-container[style*="overflow-x: scroll"] {
  overflow-x: hidden;
}

/* ОПТИМИЗАЦИЯ ВО ВРЕМЯ ПРОКРУТКИ */
.main-content-container.scrolling::-webkit-scrollbar-thumb {
  transition: none;
}
