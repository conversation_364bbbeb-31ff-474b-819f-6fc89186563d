import { renderBuildsTable } from '../builds/BuildsTable.js';
import {
  getTankIconPath,
  getFlagPath,
  getBlurredFlagPath,
  getRoleIconPath,
  getTankTypeClass,
} from '../../utils/constants.js';

// Инициализация компонента деталей танка
export function initializeTankDetails() {
  initializeTabHandlers();
  initializeTooltips();
}

// Обновление UI деталей танка
export async function updateTankDetailsUI(tankObject) {
  if (!tankObject) {
    hideTankDetails();
    return;
  }

  const container = document.getElementById('tank-characteristics-container');
  if (!container) {
    return;
  }

  await renderTankCharacteristicsPage(container, tankObject);
  showTankDetails();
  setupTooltips();
}

// Рендеринг полной страницы характеристик танка
async function renderTankCharacteristicsPage(container, tank) {
  // АГРЕССИВНО убираем отступы для страниц танков
  const mainContainer = document.querySelector('.main-content-container');
  if (mainContainer) {
    mainContainer.classList.add('tank-details-page');
    mainContainer.style.padding = '0';
    mainContainer.style.paddingTop = '0';
    mainContainer.style.margin = '0';
    mainContainer.style.marginTop = '0';
  }

  // Также убираем отступы у vehicles-section
  const vehiclesSection = document.getElementById('vehicles-section');
  if (vehiclesSection) {
    vehiclesSection.style.padding = '0';
    vehiclesSection.style.paddingTop = '0';
    vehiclesSection.classList.add('tank-page');
  }

  const tankIconPath = getTankIconPath(tank.name);
  const flagPath = getFlagPath(tank.country);
  const roleName = tank.role?.name || 'Универсальный';
  const roleIconPath = getRoleIconPath(roleName);

  // Импортируем функции для римских цифр и иконки типа техники
  const { toRomanNumeral } = await import('../../utils/helpers.js');
  const { getTankTypeIconPath } = await import('../../utils/constants.js');
  const tankTypeIconPath = getTankTypeIconPath(tank.type);

  // Создаем HTML для страницы характеристик
  container.innerHTML = `
    <div class="tank-characteristics-page">
      <!-- Кнопка назад -->
      <div class="back-button-container">
        <button class="back-to-list-btn" onclick="window.goBackToTankList()">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
          Назад к списку
        </button>
      </div>

      <!-- Заголовок танка -->
      <div class="tank-header" data-tank-type="${getTankTypeClass(tank.type)}">
        <div class="tank-header-left flex items-center">
          <div class="tank-icon-container">
            <img src="${tankIconPath}" alt="${tank.name}" class="tank-icon-large"
                 onerror="this.src='/src/assets/images/tanks/maus.webp'; this.onerror=null;">
          </div>
          <div class="tank-info">
            <div class="tank-name-row">
              <div class="tank-flag-container">
                <img src="${flagPath}" alt="${tank.country}" class="tank-flag" onerror="this.style.display='none'">
                <img src="${tankTypeIconPath}" alt="${tank.type}" class="tank-type-icon-flag" onerror="this.style.display='none'">
              </div>
              <div class="tank-name-info">
                <div class="tank-name-with-badge">
                  <h1 class="tank-name">${tank.name}</h1>
                  <div class="tank-badges-row">
                    <span class="tank-type-badge ${getTankTypeClass(tank.type)}">${tank.type}</span>
                    <span class="tank-level-badge-inline">${toRomanNumeral(tank.level || 10)}</span>
                  </div>
                </div>
                <div class="tank-meta">
                  <div class="tank-role">
                    <img src="${roleIconPath}" alt="role" class="role-icon" onerror="this.style.display='none'">
                    <span class="role-text">${roleName}</span>
                  </div>
                  ${
                    tank.cost
                      ? `<div class="tank-price">
                    <img src="${tank.currencyIcon || '/src/assets/images/role/silver.png'}" alt="currency" class="currency-icon" onerror="this.style.display='none'">
                    <span class="price-value">${tank.cost.toLocaleString()}</span>
                  </div>`
                      : ''
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tank-header-right">
          <div class="equipment-slots">
            <!-- Группа 1: Слот 1 (отдельно) -->
            <div class="equipment-slot slot-1" id="equipment-slot-1" title="Слот 1">
              <div class="slot-background"></div>
            </div>

            <!-- Группа 2: Слоты 2-3-4 (в притык) -->
            <div class="equipment-slot slot-2" id="equipment-slot-2" title="Слот 2">
              <div class="slot-background"></div>
            </div>
            <div class="equipment-slot slot-3" id="equipment-slot-3" title="Слот 3">
              <div class="slot-background"></div>
            </div>
            <div class="equipment-slot slot-4" id="equipment-slot-4" title="Слот 4">
              <div class="slot-background"></div>
            </div>

            <!-- Группа 3: Слот 5 (отдельно) -->
            <div class="equipment-slot slot-5" id="equipment-slot-5" title="Слот 5">
              <div class="slot-background"></div>
            </div>

            <!-- Группа 4: Слоты 6-7-8 (в притык) -->
            <div class="equipment-slot slot-6" id="equipment-slot-6" title="Слот 6">
              <div class="slot-background"></div>
            </div>
            <div class="equipment-slot slot-7" id="equipment-slot-7" title="Слот 7">
              <div class="slot-background"></div>
            </div>
            <div class="equipment-slot slot-8" id="equipment-slot-8" title="Слот 8">
              <div class="slot-background"></div>
            </div>

            <!-- Группа 5: Слот 9 (отдельно) -->
            <div class="equipment-slot slot-9" id="equipment-slot-9" title="Слот 9">
              <div class="slot-background"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- НОВЫЙ LAYOUT: Характеристики сверху -->
      <div class="characteristics-main-layout">
        <div class="characteristics-sections">
          <!-- Вооружение (красная секция) -->
          <div class="characteristics-section armament">
            <div class="section-header">
              <h2>🔥 Вооружение</h2>
            </div>
            <div class="characteristics-grid">
              ${renderCharacteristicItem('Урон в минуту', tank.characteristics?.dpm || tank.characteristics?.damagePerMinute || 'Н/Д', 'dpm')}
              ${renderCharacteristicItem('Урон', tank.characteristics?.damage || 'Н/Д', 'damage')}
              ${renderCharacteristicItem('Бронепробитие (мм)', tank.characteristics?.penetration || 'Н/Д', 'penetration')}
              ${renderCharacteristicItem('Время перезарядки (сек)', tank.characteristics?.reloadTime || 'Н/Д', 'reload')}
              ${renderCharacteristicItem('Скорострельность (выстр/мин)', tank.characteristics?.rateOfFire || 'Н/Д', 'rof')}
              ${renderCharacteristicItem('Время сведения (сек)', tank.characteristics?.aimTime || 'Н/Д', 'aim')}
              ${renderCharacteristicItem('Разброс (м)', tank.characteristics?.dispersion || 'Н/Д', 'dispersion')}
            </div>
          </div>

          <!-- Мобильность (бирюзовая секция) -->
          <div class="characteristics-section mobility">
            <div class="section-header">
              <h2>⚡ Мобильность</h2>
            </div>
            <div class="characteristics-grid">
              ${renderCharacteristicItem('Максимальная скорость (км/ч)', tank.characteristics?.speed || tank.characteristics?.maxSpeed || 'Н/Д', 'speed')}
              ${renderCharacteristicItem('Скорость заднего хода (км/ч)', tank.characteristics?.reverseSpeed || 'Н/Д', 'reverse')}
              ${renderCharacteristicItem('Мощность двигателя (л.с.)', tank.characteristics?.enginePower || 'Н/Д', 'engine')}
              ${renderCharacteristicItem('Удельная мощность (л.с./т)', tank.characteristics?.powerToWeight || 'Н/Д', 'power')}
              ${renderCharacteristicItem('Скорость поворота (°/сек)', tank.characteristics?.traverse || tank.characteristics?.traverseSpeed || 'Н/Д', 'traverse')}
            </div>
          </div>

          <!-- Прочие характеристики (зеленая секция) -->
          <div class="characteristics-section other">
            <div class="section-header">
              <h2>✅ Прочие</h2>
            </div>
            <div class="characteristics-grid">
              ${renderCharacteristicItem('Прочность', tank.characteristics?.hitPoints || 'Н/Д', 'hp')}
              ${renderCharacteristicItem('Обзор (м)', tank.characteristics?.viewRange || 'Н/Д', 'view')}
              ${renderCharacteristicItem('Дальность связи (м)', tank.characteristics?.radioRange || 'Н/Д', 'radio')}
              ${renderCharacteristicItem('Шанс возгорания (%)', tank.characteristics?.fireChance || 'Н/Д', 'fire')}
              ${renderCharacteristicItem('Броня корпуса (мм)', tank.characteristics?.hullArmor?.front || 'Н/Д', 'hull')}
              ${renderCharacteristicItem('Броня башни (мм)', tank.characteristics?.turretArmor?.front || 'Н/Д', 'turret')}
            </div>
          </div>
        </div>
      </div>

      <!-- Сборки и примечания снизу -->
      <div class="builds-and-notes-section">
        <div class="builds-and-notes-column">
          <!-- Таблица сборок -->
          <div class="builds-section">
        <div class="builds-header" style="display: none;">
          <h3 id="builds-message" style="display: none;"></h3>
        </div>
        <div class="builds-table-container">
          <table class="builds-table-daisy">
            <thead>
              <tr>
                <th class="th-build">
                  <div style="display: flex; align-items: center; gap: 8px;">
                    <svg
                      id="toggle-notes-icon"
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="#ff3b30"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      style="cursor: pointer; transition: all 0.3s ease;"
                      onclick="toggleNotesSection()"
                      title="Показать/скрыть примечания"
                    >
                      <path d="M12 9v4" />
                      <path d="M10.363 3.591l-8.106 13.534a1.914 1.914 0 0 0 1.636 2.871h16.214a1.914 1.914 0 0 0 1.636 -2.87l-8.106 -13.536a1.914 1.914 0 0 0 -3.274 0z" />
                      <path d="M12 16h.01" />
                    </svg>
                    <span>Сборка</span>
                  </div>
                </th>
                <th class="th-efficiency sortable" onclick="sortBuildsTable('efficiency', this)">
                  Эффективность
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-contribution sortable" onclick="sortBuildsTable('contribution', this)">
                  Вклад
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-damage sortable" onclick="sortBuildsTable('damage', this)">
                  Урон
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-assist sortable" onclick="sortBuildsTable('assist', this)">
                  Помощь
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-dpm sortable" onclick="sortBuildsTable('dpm', this)">
                  DPM
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-spread sortable" onclick="sortBuildsTable('spread', this)">
                  Разброс
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-dynspread sortable" onclick="sortBuildsTable('dynSpread', this)">
                  Дин. разброс
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-aimtime sortable" onclick="sortBuildsTable('aimTime', this)">
                  Сведение (с)
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-viewrange sortable" onclick="sortBuildsTable('viewRange', this)">
                  Обзор (м)
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-dispersion sortable" onclick="sortBuildsTable('dispersion', this)">
                  Дисперсия
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-cv sortable" onclick="sortBuildsTable('cv', this)">
                  CV (%)
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-accuracy sortable" onclick="sortBuildsTable('accuracy', this)">
                  Точность (%)
                  <span class="sort-arrow"></span>
                </th>
              </tr>
            </thead>
            <tbody id="builds-table-body" class="text-xs">
            </tbody>
          </table>
          </div>

          <!-- Секция примечаний -->
          <div class="notes-section">
            <div class="notes-header">
              <div class="notes-title">
                <svg xmlns="http://www.w3.org/2000/svg" class="notes-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                  <polyline points="14,2 14,8 20,8"/>
                  <line x1="16" y1="13" x2="8" y2="13"/>
                  <line x1="16" y1="17" x2="8" y2="17"/>
                  <polyline points="10,9 9,9 8,9"/>
                </svg>
                <h3>Примечания</h3>
              </div>
            </div>
            <div class="notes-content-compact">
              <div class="note-item-compact">
                <div class="note-text-compact"><strong>Постоянный бонус:</strong> Процент или фиксированное значение, добавляемое к базовым характеристикам танка.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-efficiency">Эффективность:</strong> Оценка сборки (0-100) по весам: Вклад (30%), Урон (25%), Помощь (20%), DPM (15%), Обзор (10%), CV (5%).</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-contribution">Вклад:</strong> Суммарный вклад в бой (урон + помощь в нанесении урона + блокированный урон + разведка).</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-damage">Урон:</strong> Средний урон за бой. Основной показатель эффективности в нанесении урона противнику.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-help">Помощь:</strong> Урон по засвеченным целям + блокированный урон + урон по оглушенным целям.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-dpm">DPM:</strong> Средний урон в минуту (Damage Per Minute) - расчетный показатель потенциального урона.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-spread">Разброс:</strong> Показатель неточности орудия между выстрелами - чем меньше значение, тем выше точность стрельбы.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-dynspread">Дин. разброс:</strong> Дополнительный разброс при движении танка - влияет на точность стрельбы в движении.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-aimtime">Сведение (с):</strong> Время полного сведения прицела после выстрела или поворота башни в секундах.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-viewrange">Обзор (м):</strong> Дальность обнаружения противника в метрах - влияет на способность засвечивать вражеские танки.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-dispersion">Дисперсия:</strong> Коэффициент разброса снарядов - влияет на кучность стрельбы и стабильность орудия.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-cv">CV (%):</strong> Коэффициент вариации в процентах - показатель стабильности результатов, чем меньше значение, тем стабильнее показатели.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-accuracy">Точность (%):</strong> Процентный показатель попаданий по цели - отражает общую эффективность стрельбы.</div>
              </div>
            </div>
          </div>
        </div> <!-- Закрываем builds-and-notes-column -->
      </div> <!-- Закрываем builds-and-notes-section -->
    </div>


  `;

  // ПРИНУДИТЕЛЬНО убираем отступы ПОСЛЕ создания HTML
  setTimeout(() => {
    const mainContainer = document.querySelector('.main-content-container');
    if (mainContainer) {
      mainContainer.style.padding = '0';
      mainContainer.style.paddingTop = '0';
      mainContainer.style.margin = '0';
      mainContainer.style.marginTop = '0';
    }

    const vehiclesSection = document.getElementById('vehicles-section');
    if (vehiclesSection) {
      vehiclesSection.style.padding = '0';
      vehiclesSection.style.paddingTop = '0';
    }

    const backButtonContainer = document.querySelector('.back-button-container');
    if (backButtonContainer) {
      backButtonContainer.style.marginTop = '0.5rem';
      backButtonContainer.style.paddingTop = '0';
    }

    console.log('🔧 TankDetails: Forced padding removal after HTML creation');
  }, 0);

  // ПРИНУДИТЕЛЬНАЯ обработка сборок
  const buildsTableBody = document.getElementById('builds-table-body');

  if (buildsTableBody && tank) {
    console.log(`🔧 TankDetails: Processing builds for ${tank.name}`);

    // ВСЕГДА сбрасываем состояние при смене танка
    if (!currentTankBuilds || currentTankBuilds.name !== tank.name) {
      currentTankBuilds = tank;
      currentSortColumn = null;
      currentSortDirection = 'desc';
      updateSortIndicators(null);
      console.log(`🔧 TankDetails: Reset builds state for ${tank.name}`);
    }

    // ПРИНУДИТЕЛЬНО вызываем renderBuildsTable для ВСЕХ танков
    console.log(`🔧 TankDetails: Calling renderBuildsTable for ${tank.name}`);
    renderBuildsTable(tank);
  } else {
    console.error('🚨 TankDetails: buildsTableBody not found or tank is null');
  }

  // Инициализация примечаний
  const notesSection = document.querySelector('.notes-section');
  const toggleIcon = document.getElementById('toggle-notes-icon');

  if (notesSection && toggleIcon) {
    notesSection.classList.remove('notes-hidden');
    toggleIcon.style.transform = 'rotate(0deg)';
    toggleIcon.style.stroke = '#ff3b30';
  }
}

// Вспомогательная функция для рендеринга элемента характеристики
function renderCharacteristicItem(label, value, type) {
  const percentage = getCharacteristicPercentage(value, type);
  const displayValue = formatCharacteristicValue(value, type);

  // Определяем, является ли значение низким (менее 30%)
  const isLowValue = percentage < 30 && percentage > 0;
  const lowValueClass = isLowValue ? ' low-value' : '';

  return `
    <div class="characteristic-item" data-type="${type}">
      <div class="characteristic-label">${label}</div>
      <div class="characteristic-value-container">
        <div class="characteristic-bar">
          <div class="characteristic-fill${lowValueClass}" style="width: ${percentage}%"></div>
        </div>
        <div class="characteristic-value">${displayValue}</div>
      </div>
    </div>
  `;
}

// Функция для получения процента заполнения полоски характеристики
function getCharacteristicPercentage(value, type) {
  // Если значение "Н/Д", возвращаем 0
  if (value === 'Н/Д' || value === null || value === undefined) {
    return 0;
  }

  const numericValue = parseFloat(value.toString().replace(/[^\d.]/g, ''));
  if (isNaN(numericValue)) return 0;

  // Нормализуем значения для разных типов характеристик
  switch (type) {
    // Вооружение
    case 'dpm':
      return Math.min((numericValue / 4000) * 100, 100);
    case 'damage':
      return Math.min((numericValue / 600) * 100, 100);
    case 'penetration':
      return Math.min((numericValue / 350) * 100, 100);
    case 'reload':
      return Math.min(((15 - numericValue) / 15) * 100, 100); // Обратная логика - меньше лучше
    case 'rof':
      return Math.min((numericValue / 12) * 100, 100);
    case 'aim':
      return Math.min(((3 - numericValue) / 3) * 100, 100); // Обратная логика
    case 'dispersion':
      return Math.min(((0.5 - numericValue) / 0.5) * 100, 100); // Обратная логика

    // Мобильность
    case 'speed':
      return Math.min((numericValue / 70) * 100, 100);
    case 'reverse':
      return Math.min((numericValue / 30) * 100, 100);
    case 'engine':
      return Math.min((numericValue / 1000) * 100, 100);
    case 'power':
      return Math.min((numericValue / 30) * 100, 100);
    case 'traverse':
      return Math.min((numericValue / 60) * 100, 100);

    // Прочие характеристики
    case 'hp':
      return Math.min((numericValue / 2500) * 100, 100);
    case 'view':
      return Math.min((numericValue / 500) * 100, 100);
    case 'radio':
      return Math.min((numericValue / 1000) * 100, 100);
    case 'fire':
      return Math.min(((20 - numericValue) / 20) * 100, 100); // Обратная логика
    case 'hull':
      return Math.min((numericValue / 300) * 100, 100);
    case 'turret':
      return Math.min((numericValue / 300) * 100, 100);

    default:
      return Math.min((numericValue / 100) * 100, 100);
  }
}

// Функция для форматирования значений характеристик
function formatCharacteristicValue(value, type) {
  // Если значение "Н/Д", возвращаем как есть
  if (value === 'Н/Д' || value === null || value === undefined) {
    return 'Н/Д';
  }

  const numericValue = parseFloat(value.toString().replace(/[^\d.]/g, ''));
  if (isNaN(numericValue)) return value;

  switch (type) {
    case 'dpm':
      return numericValue.toLocaleString();
    case 'damage':
      return Math.round(numericValue).toString();
    case 'penetration':
      return Math.round(numericValue) + ' мм';
    case 'reload':
      return numericValue.toFixed(1) + ' с';
    case 'rof':
      return Math.round(numericValue) + ' выстр/мин';
    case 'aim':
      return numericValue.toFixed(1) + ' с';
    case 'dispersion':
      return numericValue.toFixed(2) + ' м';
    case 'speed':
      return Math.round(numericValue) + ' км/ч';
    case 'reverse':
      return Math.round(numericValue) + ' км/ч';
    case 'engine':
      return Math.round(numericValue) + ' л.с.';
    case 'power':
      return numericValue.toFixed(1) + ' л.с./т';
    case 'traverse':
      return numericValue.toFixed(1) + ' °/с';
    case 'hp':
      return Math.round(numericValue).toLocaleString();
    case 'view':
      return Math.round(numericValue) + ' м';
    case 'radio':
      return numericValue.toFixed(1) + ' м';
    case 'fire':
      return Math.round(numericValue) + ' %';
    case 'hull':
      return Math.round(numericValue) + ' мм';
    case 'turret':
      return Math.round(numericValue) + ' мм';
    default:
      return value;
  }
}

// Скрыть детали танка
function hideTankDetails() {
  // Убраны вызовы функций скролла

  const container = document.getElementById('tank-characteristics-container');
  if (container) {
    container.classList.add('hidden');
    container.style.display = 'none';
    container.style.opacity = '0';
    container.style.visibility = 'hidden';
  }
}

// Показать детали танка
function showTankDetails() {
  const container = document.getElementById('tank-characteristics-container');
  if (container) {
    container.classList.remove('hidden');
    container.style.display = 'block';
    container.style.opacity = '1';
    container.style.visibility = 'visible';
  }

  // Сбрасываем прогресс-бары
  const progressBars = document.querySelectorAll('[id$="Fill"]');
  progressBars.forEach(bar => {
    bar.style.width = '0%';
    bar.style.display = 'none';
  });
}

// Обновление заголовка танка
function updateTankHeader(tankObject) {
  const elements = {
    icon: document.querySelector('.tank-icon-large'),
    flag: document.querySelector('.tank-flag'),
    name: document.querySelector('.tank-name'),
    roleText: document.querySelector('.tank-role .role-text'),
    roleIcon: document.querySelector('.tank-role .role-icon'),
    price: document.querySelector('.tank-price .price-value'),
    currencyIcon: document.querySelector('.tank-price .currency-icon'),
  };

  // Обновляем иконку танка
  if (elements.icon) {
    const iconPath = getTankIconPath(tankObject.name) || '/src/assets/images/tanks/maus.webp';
    elements.icon.src = iconPath;
    elements.icon.alt = tankObject.name || 'Иконка танка';
  }

  // Обновляем флаг страны
  if (elements.flag) {
    elements.flag.src = getBlurredFlagPath(tankObject.country);
    elements.flag.alt = tankObject.country || 'Страна';
  }

  // Обновляем название
  if (elements.name) {
    elements.name.textContent = tankObject.name || 'Название танка';
  }

  // Обновляем роль
  if (elements.roleText && elements.roleIcon) {
    const roleObject = tankObject.role;

    if (roleObject && roleObject.name && roleObject.icon) {
      elements.roleText.textContent = roleObject.name;
      elements.roleIcon.src = roleObject.icon;
      elements.roleIcon.alt = roleObject.name;
    } else {
      const fallbackRole = tankObject.role || 'Роль';
      elements.roleText.textContent = fallbackRole;
      elements.roleIcon.src = '/src/assets/images/role/unknown.png';
      elements.roleIcon.alt = fallbackRole;
    }
  }

  // Обновляем цену - скрываем если нет цены
  if (elements.price) {
    if (tankObject.cost) {
      elements.price.textContent = tankObject.cost.toLocaleString();
      elements.price.parentElement.style.display = 'flex';
    } else {
      elements.price.parentElement.style.display = 'none';
    }
  }

  // Обновляем иконку валюты
  if (elements.currencyIcon && tankObject.cost) {
    if (tankObject.currencyIcon) {
      elements.currencyIcon.src = tankObject.currencyIcon;
      elements.currencyIcon.alt = 'Бонусы';
    } else {
      elements.currencyIcon.src = '/src/assets/images/role/silver.png';
      elements.currencyIcon.alt = 'Серебро';
    }
  }

  // Обновляем изображение слота оборудования
  const equipmentSlot1 = document.getElementById('equipment-slot-1');
  if (equipmentSlot1 && tankObject.equipmentSlot1Image) {
    equipmentSlot1.style.backgroundImage = `url('${tankObject.equipmentSlot1Image}')`;
  }
}

// Инициализация обработчиков вкладок
function initializeTabHandlers() {
  const tabs = document.querySelectorAll('.tab-button');
  const tabContents = document.querySelectorAll('.tab-content');

  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const targetTab = tab.dataset.tab;

      // Обновляем активную вкладку
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');

      // Показываем соответствующий контент
      tabContents.forEach(content => {
        if (content.id === `${targetTab}-tab`) {
          content.classList.add('active');
          content.style.display = 'block';
        } else {
          content.classList.remove('active');
          content.style.display = 'none';
        }
      });
    });
  });
}

// Инициализация подсказок
function initializeTooltips() {
  // Создаем глобальный элемент подсказки
  let globalTooltip = document.getElementById('global-tooltip');
  if (!globalTooltip) {
    globalTooltip = document.createElement('div');
    globalTooltip.id = 'global-tooltip';
    globalTooltip.className = 'global-tooltip';
    document.body.appendChild(globalTooltip);
  }
}

// Настройка подсказок
function setupTooltips() {
  const globalTooltip = document.getElementById('global-tooltip');
  if (!globalTooltip) return;

  // Применяем стили в зависимости от темы
  applyTooltipTheme();

  // Удаляем старые обработчики
  const tooltipElements = document.querySelectorAll('.characteristic-tooltip');
  tooltipElements.forEach(element => {
    element.removeEventListener('mouseenter', handleTooltipMouseEnter);
    element.removeEventListener('mouseleave', handleTooltipMouseLeave);
    element.removeEventListener('mousemove', handleTooltipMouseMove);
  });

  // Добавляем новые обработчики
  tooltipElements.forEach(element => {
    element.addEventListener('mouseenter', handleTooltipMouseEnter);
    element.addEventListener('mouseleave', handleTooltipMouseLeave);
    element.addEventListener('mousemove', handleTooltipMouseMove);
  });
}

// Применение темы к подсказке
function applyTooltipTheme() {
  const globalTooltip = document.getElementById('global-tooltip');
  if (!globalTooltip) return;

  const isDarkTheme = document.documentElement.getAttribute('data-theme') === 'dark';

  if (isDarkTheme) {
    globalTooltip.style.backgroundColor = '#2d3748';
    globalTooltip.style.color = '#f7fafc';
    globalTooltip.style.border = '1px solid #4a5568';
  } else {
    globalTooltip.style.backgroundColor = '#f8fafc';
    globalTooltip.style.color = '#111827';
    globalTooltip.style.border = '1px solid #e6e6e6';
  }
}

// Обработчики событий подсказок
function handleTooltipMouseEnter(e) {
  const tooltipText = this.querySelector('.tooltip-text');
  if (!tooltipText) return;

  const globalTooltip = document.getElementById('global-tooltip');
  if (!globalTooltip) return;

  globalTooltip.innerHTML = tooltipText.innerHTML;
  positionTooltip(e);
  globalTooltip.style.display = 'block';
}

function handleTooltipMouseLeave() {
  const globalTooltip = document.getElementById('global-tooltip');
  if (globalTooltip) {
    globalTooltip.style.display = 'none';
  }
}

function handleTooltipMouseMove(e) {
  positionTooltip(e);
}

// Позиционирование подсказки
function positionTooltip(e) {
  const globalTooltip = document.getElementById('global-tooltip');
  if (!globalTooltip) return;

  const offsetX = 25;
  const offsetY = 10;

  let left = e.clientX + offsetX;
  let top = e.clientY - offsetY;

  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  const tooltipWidth = globalTooltip.offsetWidth;
  const tooltipHeight = globalTooltip.offsetHeight;

  // Проверка границ
  if (left + tooltipWidth > viewportWidth - 10) {
    left = e.clientX - tooltipWidth - offsetX;
  }

  if (top + tooltipHeight > viewportHeight - 10) {
    top = e.clientY - tooltipHeight - offsetY;
  }

  globalTooltip.style.left = `${left}px`;
  globalTooltip.style.top = `${top}px`;
}

// Наблюдатель за изменением темы
const themeObserver = new MutationObserver(mutations => {
  mutations.forEach(mutation => {
    if (mutation.attributeName === 'data-theme') {
      applyTooltipTheme();
    }
  });
});

// Запускаем наблюдатель
themeObserver.observe(document.documentElement, { attributes: true });

// Переменные для хранения состояния сортировки
let currentSortColumn = null;
let currentSortDirection = 'desc'; // По умолчанию сортировка по убыванию
let currentTankBuilds = null; // Сохраняем текущие сборки для пересортировки

// Функция сортировки таблицы сборок
window.sortBuildsTable = function (column, headerElement) {
  // КРИТИЧЕСКАЯ ПРОВЕРКА: Получаем актуальный танк из состояния
  import('../../store/state.js').then(stateModule => {
    const { state } = stateModule;

    // ПРИНУДИТЕЛЬНАЯ ПРОВЕРКА: Используем только текущий выбранный танк из state
    if (!state.selectedTank || !state.selectedTank.bestBuilds || state.selectedTank.bestBuilds.length === 0) {
      console.error('🚨 BuildsTable: No selected tank in state or no builds for sorting');
      // Очищаем таблицу если нет данных
      const tableBody = document.querySelector('#builds-table-body');
      if (tableBody) {
        tableBody.innerHTML = '';
      }
      return;
    }

    // КРИТИЧЕСКАЯ ПРОВЕРКА: Убеждаемся что currentTankBuilds соответствует state.selectedTank
    if (!currentTankBuilds || currentTankBuilds.name !== state.selectedTank.name) {
      console.log(`🧹 BuildsTable: Syncing currentTankBuilds with state: ${state.selectedTank.name}`);
      currentTankBuilds = state.selectedTank;
      // Сбрасываем сортировку при смене танка
      currentSortColumn = null;
      currentSortDirection = 'desc';
      updateSortIndicators(null);
    }

    // ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА: Убеждаемся что это правильный танк
    if (!currentTankBuilds || !currentTankBuilds.bestBuilds) {
      console.error('🚨 BuildsTable: Invalid tank data for sorting');
      return;
    }

    console.log(`🔧 BuildsTable: Sorting builds for tank: ${currentTankBuilds.name}`);

  // Определяем направление сортировки с тремя состояниями: desc -> asc -> none (сброс)
  if (currentSortColumn === column) {
    if (currentSortDirection === 'desc') {
      currentSortDirection = 'asc';
    } else if (currentSortDirection === 'asc') {
      // Третий клик - сброс сортировки
      currentSortDirection = null;
      currentSortColumn = null;
    }
  } else {
    currentSortDirection = 'desc';
    currentSortColumn = column;
  }

  // Копируем массив сборок для сортировки
  const sortedBuilds = [...currentTankBuilds.bestBuilds];

  // Если сортировка сброшена, используем исходный порядок
  if (currentSortDirection === null) {
    // Обновляем визуальные индикаторы (убираем стрелочки)
    updateSortIndicators(null);

    // Перерисовываем таблицу с исходными данными (без сортировки)
    renderBuildsTable(currentTankBuilds);
    return;
  }

  // Сортируем в зависимости от колонки
  sortedBuilds.sort((a, b) => {
    let aValue, bValue;

    switch (column) {
      case 'efficiency':
        aValue = parseFloat(a.efficiency) || 0;
        bValue = parseFloat(b.efficiency) || 0;
        break;
      case 'contribution':
        aValue = parseFloat(a.contribution) || 0;
        bValue = parseFloat(b.contribution) || 0;
        break;
      case 'damage':
        aValue = parseFloat(a.damage) || 0;
        bValue = parseFloat(b.damage) || 0;
        break;
      case 'assist':
        aValue = parseFloat(a.assist) || 0;
        bValue = parseFloat(b.assist) || 0;
        break;
      case 'dpm':
        aValue = parseFloat(a.dpm) || 0;
        bValue = parseFloat(b.dpm) || 0;
        break;
      case 'spread':
        aValue = parseFloat(a.spread) || 0;
        bValue = parseFloat(b.spread) || 0;
        break;
      case 'dynSpread':
        aValue = parseFloat(a.dynSpread) || 0;
        bValue = parseFloat(b.dynSpread) || 0;
        break;
      case 'aimTime':
        aValue = parseFloat(a.aimTime) || 0;
        bValue = parseFloat(b.aimTime) || 0;
        break;
      case 'viewRange':
        aValue = parseFloat(a.viewRange) || 0;
        bValue = parseFloat(b.viewRange) || 0;
        break;
      case 'dispersion':
        aValue = parseFloat(a.dispersion) || 0;
        bValue = parseFloat(b.dispersion) || 0;
        break;
      case 'cv':
        aValue = parseFloat(a.cv) || 0;
        bValue = parseFloat(b.cv) || 0;
        break;
      case 'accuracy':
        aValue = parseFloat(a.accuracy) || 0;
        bValue = parseFloat(b.accuracy) || 0;
        break;
      default:
        return 0;
    }

    // Для некоторых колонок меньшее значение лучше
    const inverseSortColumns = ['spread', 'dynSpread', 'aimTime', 'dispersion', 'cv'];
    const isInverseSort = inverseSortColumns.includes(column);

    if (currentSortDirection === 'asc') {
      return isInverseSort ? bValue - aValue : aValue - bValue;
    } else {
      return isInverseSort ? aValue - bValue : bValue - aValue;
    }
  });

  // Обновляем визуальные индикаторы сортировки
  updateSortIndicators(headerElement);

    // Перерисовываем таблицу с отсортированными данными
    const tempTank = { ...currentTankBuilds, bestBuilds: sortedBuilds };
    renderBuildsTable(tempTank);
  }); // Закрываем Promise.then
};

// Функция обновления индикаторов сортировки
function updateSortIndicators(activeHeader) {
  // Сбрасываем все индикаторы
  document.querySelectorAll('.builds-table-daisy th.sortable').forEach(th => {
    const arrow = th.querySelector('.sort-arrow');
    if (arrow) {
      arrow.textContent = '';
      arrow.style.opacity = '0';
    }
    th.classList.remove('sorted-asc', 'sorted-desc');
  });

  // Устанавливаем активный индикатор
  if (activeHeader) {
    const arrow = activeHeader.querySelector('.sort-arrow');
    if (arrow) {
      arrow.textContent = currentSortDirection === 'asc' ? '↑' : '↓';
      arrow.style.opacity = '1';
    }
    activeHeader.classList.add(`sorted-${currentSortDirection}`);
  }
}

// Убрана переменная toggleInProgress - не нужна для простой функции

// УЛУЧШЕННАЯ функция для переключения видимости секции примечаний
window.toggleNotesSection = function () {
  console.log('🔥 TOGGLE NOTES SECTION CALLED!');

  // НАДЕЖНЫЙ поиск элементов с множественными селекторами
  const notesSection = document.querySelector('.notes-section') ||
                      document.querySelector('#notes-section') ||
                      document.querySelector('[class*="notes-section"]');

  const toggleIcon = document.getElementById('toggle-notes-icon') ||
                    document.querySelector('#toggle-notes-icon') ||
                    document.querySelector('[id*="toggle-notes-icon"]');

  console.log('🔍 notesSection:', notesSection);
  console.log('🔍 toggleIcon:', toggleIcon);

  if (!notesSection) {
    console.error('❌ СЕКЦИЯ ПРИМЕЧАНИЙ НЕ НАЙДЕНА - пробуем найти в tank-characteristics-container');
    // Дополнительный поиск в контейнере характеристик
    const container = document.getElementById('tank-characteristics-container');
    if (container) {
      const notesInContainer = container.querySelector('.notes-section');
      if (notesInContainer) {
        console.log('✅ Найдена секция примечаний в контейнере');
        toggleNotesInContainer(notesInContainer, toggleIcon);
        return;
      }
    }
    console.error('❌ СЕКЦИЯ ПРИМЕЧАНИЙ НЕ НАЙДЕНА НИГДЕ');
    return;
  }

  toggleNotesInContainer(notesSection, toggleIcon);
};

// Вспомогательная функция для переключения примечаний
function toggleNotesInContainer(notesSection, toggleIcon) {
  // ПРОСТАЯ ЛОГИКА: просто переключаем класс
  const isHidden = notesSection.classList.contains('notes-hidden');

  if (isHidden) {
    // ПОКАЗЫВАЕМ
    console.log('🔄 ПОКАЗЫВАЕМ ПРИМЕЧАНИЯ');
    notesSection.classList.remove('notes-hidden');

    if (toggleIcon) {
      toggleIcon.style.transform = 'rotate(0deg)';
      toggleIcon.style.stroke = '#ff3b30';
    }
  } else {
    // СКРЫВАЕМ
    console.log('🔄 СКРЫВАЕМ ПРИМЕЧАНИЯ');
    notesSection.classList.add('notes-hidden');

    if (toggleIcon) {
      toggleIcon.style.transform = 'rotate(180deg)';
      toggleIcon.style.stroke = '#FFA726';
    }
  }

  console.log('✅ ПЕРЕКЛЮЧЕНИЕ ЗАВЕРШЕНО');
}

// Глобальная функция для возврата к списку танков
window.goBackToTankList = function() {
  console.log('🔙 Возврат к списку танков через кнопку "Назад к списку"');

  // Заменяем текущую запись в истории на vehicles
  history.replaceState(null, 'Vehicles', '/vehicles');

  // Импортируем необходимые модули
  Promise.all([
    import('../../store/state.js'),
    import('../../components/tank-list/index.js'),
    import('../../utils/ui.js'),
    import('../../core/TankManager.js')
  ]).then(([stateModule, tankListModule, uiModule, tankManagerModule]) => {
    const { state } = stateModule;
    const { updateFilterSelection } = tankListModule;

    // Сбрасываем состояние танка
    state.selectedTank = null;
    localStorage.removeItem('selectedTank');

    // ВОССТАНАВЛИВАЕМ ФИЛЬТРЫ КОТОРЫЕ БЫЛИ ДО ВЫБОРА ТАНКА
    const savedFilters = localStorage.getItem('filtersBeforeSelection');
    if (savedFilters) {
      try {
        const filters = JSON.parse(savedFilters);
        console.log('🔄 Восстанавливаем фильтры:', filters);
        console.log('🔍 userSelectedFilters:', filters.userSelectedFilters);
        console.log('🔍 countrySelectedManually:', filters.countrySelectedManually);
        console.log('🔍 categorySelectedManually:', filters.categorySelectedManually);
        console.log('🔍 Сохраненная страна:', filters.selectedCountry);
        console.log('🔍 Сохраненная категория:', filters.selectedCategory);

        // Проверяем, выбирал ли пользователь фильтры вручную
        if (filters.userSelectedFilters) {
          // Восстанавливаем фильтры ТОЛЬКО если пользователь их выбирал вручную
          state.selectedCountry = filters.selectedCountry || 'all';
          state.selectedCategory = filters.selectedCategory || 'all';
          state.searchQuery = filters.searchQuery || '';
          state.countrySelectedManually = filters.countrySelectedManually || false;
          state.categorySelectedManually = filters.categorySelectedManually || false;

          // Обновляем визуальное состояние фильтров
          const countryForUI = state.selectedCountry === 'all' ? 'all' :
            state.selectedCountry.toLowerCase().replace('ussr', 'ussr');
          const categoryForUI = state.selectedCategory === 'all' ? 'all' :
            Object.keys({
              'heavyTank': 'ТТ',
              'mediumTank': 'СТ',
              'lightTank': 'ЛТ',
              'at-spg': 'ПТ-САУ',
              'spg': 'САУ'
            }).find(key => key === state.selectedCategory) ?
            {'heavyTank': 'ТТ', 'mediumTank': 'СТ', 'lightTank': 'ЛТ', 'at-spg': 'ПТ-САУ', 'spg': 'САУ'}[state.selectedCategory] :
            state.selectedCategory;

          updateFilterSelection('country', countryForUI);
          updateFilterSelection('category', categoryForUI);

          // Поле поиска отключено в текущем дизайне
          // const searchInput = document.querySelector('#search-input');
          // if (searchInput) {
          //   searchInput.value = state.searchQuery;
          // }

          console.log('✅ Пользовательские фильтры восстановлены при кнопке "Назад к списку"');
        } else {
          // Если пользователь НЕ выбирал фильтры вручную - показываем все танки
          console.log('🔄 Пользователь не выбирал фильтры, показываем все танки');
          state.selectedCountry = 'all';
          state.selectedCategory = 'all';
          state.searchQuery = '';
          state.countrySelectedManually = false;
          state.categorySelectedManually = false;
          updateFilterSelection('country', 'all');
          updateFilterSelection('category', 'all');

          const searchInput = document.querySelector('#search-input');
          if (searchInput) {
            searchInput.value = '';
          }
        }

        // Очищаем сохраненные фильтры только при явном возврате к списку
        localStorage.removeItem('filtersBeforeSelection');
        console.log('🧹 Фильтры очищены при возврате к списку');

      } catch (error) {
        console.error('❌ Ошибка при восстановлении фильтров:', error);
        // Если ошибка - сбрасываем к "all"
        state.selectedCountry = 'all';
        state.selectedCategory = 'all';
        state.searchQuery = '';
        updateFilterSelection('country', 'all');
        updateFilterSelection('category', 'all');
      }
    } else {
      // Если нет сохраненных фильтров - сбрасываем к "all"
      console.log('🔄 Нет сохраненных фильтров, сбрасываем к "all"');
      state.selectedCountry = 'all';
      state.selectedCategory = 'all';
      state.searchQuery = '';
      state.countrySelectedManually = false;
      state.categorySelectedManually = false;
      updateFilterSelection('country', 'all');
      updateFilterSelection('category', 'all');

      const searchInput = document.querySelector('#search-input');
      if (searchInput) {
        searchInput.value = '';
      }
    }

    // Убираем атрибут страницы танка
    document.body.removeAttribute('data-tank-page');

    // НЕ СКРЫВАЕМ СЕКЦИИ - просто обеспечиваем что vehicles видна
    // ПРИНУДИТЕЛЬНО ПОКАЗЫВАЕМ СЕКЦИЮ VEHICLES
    const vehiclesSection = document.getElementById('vehicles-section');
    if (vehiclesSection) {
      vehiclesSection.style.display = 'grid';
      vehiclesSection.style.visibility = 'visible';
      vehiclesSection.style.opacity = '1';
      vehiclesSection.classList.remove('hidden');
      vehiclesSection.classList.add('section-visible');
    }

    // Скрываем характеристики танка
    const characteristicsContainer = document.getElementById('tank-characteristics-container');
    if (characteristicsContainer) {
      characteristicsContainer.classList.add('hidden');
      characteristicsContainer.style.display = 'none';
      characteristicsContainer.style.opacity = '0';
      characteristicsContainer.style.visibility = 'hidden';
    }

    // ИСПРАВЛЕНО: Показываем tank-list элемент с полным восстановлением grid layout
    const tankListElement = document.getElementById('tank-list');
    if (tankListElement) {
      // Ручное восстановление grid layout (без async/await для совместимости)
      tankListElement.classList.remove('hidden');
      tankListElement.style.display = 'grid';
      tankListElement.style.visibility = 'visible';
      tankListElement.style.opacity = '1';
      // Убираем принудительные стили - используем CSS
      tankListElement.style.gridTemplateColumns = '';
      tankListElement.style.gap = '';
      tankListElement.style.padding = '';
      tankListElement.style.width = '100%';
      tankListElement.style.maxWidth = '100%';
      tankListElement.style.boxSizing = 'border-box';

      // Дополнительная защита
      tankListElement.classList.add('performance-optimized');
      console.log('🔧 Grid layout восстановлен в TankDetails');
    }

    // Применяем фильтры и обновляем список танков
    tankManagerModule.tankManager.applyFiltersAndRenderTankList();

    // Дополнительная проверка grid layout через небольшую задержку
    setTimeout(() => {
      const tankListCheck = document.getElementById('tank-list');
      if (tankListCheck) {
        // Убеждаемся, что grid layout корректен
        if (tankListCheck.style.display !== 'grid') {
          tankListCheck.style.display = 'grid';
          // Убираем принудительные стили - используем CSS
          tankListCheck.style.gridTemplateColumns = '';
          tankListCheck.style.gap = '';
          tankListCheck.style.padding = '';
          console.log('🔧 Grid layout дополнительно восстановлен через setTimeout');
        }
      }

      // Синхронизируем бейджики сравнения после возврата к списку
      if (window.advancedCompareSystem) {
        window.advancedCompareSystem.syncAllBadges();
      }
    }, 100);

    console.log('✅ Возврат к списку завершен, фильтры восстановлены');
  }).catch(error => {
    console.error('❌ Ошибка при возврате к списку:', error);
  });
};

// Экспорт функций
export { updateTankHeader, showTankDetails, hideTankDetails, setupTooltips };
