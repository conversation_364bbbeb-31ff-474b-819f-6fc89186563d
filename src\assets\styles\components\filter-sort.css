/* ========================================
   СИСТЕМА ФИЛЬТРАЦИИ И СОРТИРОВКИ
   ======================================== */

/* ОСНОВНАЯ ПАНЕЛЬ */
.filter-sort-panel {
  background: linear-gradient(135deg, 
    rgba(30, 39, 59, 0.95) 0%,
    rgba(45, 55, 75, 0.9) 100%);
  border-radius: 16px;
  border: 1px solid rgba(114, 84, 228, 0.2);
  backdrop-filter: blur(20px);
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ЗАГОЛОВОК ПАНЕЛИ */
.filter-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.15), rgba(161, 123, 248, 0.1));
  border-bottom: 1px solid rgba(114, 84, 228, 0.2);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
}

.filter-icon {
  font-size: 1.25rem;
  filter: drop-shadow(0 0 10px rgba(114, 84, 228, 0.8));
}

.panel-controls {
  display: flex;
  gap: 0.75rem;
}

.filter-toggle-btn,
.filter-reset-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-toggle-btn:hover,
.filter-reset-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(114, 84, 228, 0.5);
  transform: translateY(-1px);
}

.toggle-icon,
.reset-icon {
  font-size: 0.9rem;
  transition: transform 0.3s ease;
}

.filter-toggle-btn:hover .toggle-icon {
  transform: scale(1.1);
}

/* СОДЕРЖИМОЕ ПАНЕЛИ */
.filter-panel-content {
  padding: 1.5rem;
  transition: all 0.3s ease;
  max-height: 1000px;
  overflow: hidden;
}

.filter-panel-content.collapsed {
  max-height: 0;
  padding: 0 1.5rem;
}

/* СЕКЦИИ ФИЛЬТРОВ */
.filter-sections {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.filter-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  padding: 1rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title::before {
  content: '';
  width: 3px;
  height: 1rem;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  border-radius: 2px;
}

/* ПОИСК */
.search-section {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05));
  border-color: rgba(59, 130, 246, 0.2);
}

.search-input-container {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 0.12);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(26, 26, 46, 0.95);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(20px);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 0.25rem;
}

.search-suggestion {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.search-suggestion:last-child {
  border-bottom: none;
}

.search-suggestion:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #ffffff;
}

.suggestion-icon {
  color: #3b82f6;
  font-size: 0.9rem;
}

.suggestion-text {
  color: #d1d5db;
  font-size: 0.85rem;
}

/* ОСНОВНЫЕ ФИЛЬТРЫ */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.filter-label {
  font-size: 0.85rem;
  font-weight: 500;
  color: #d1d5db;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.label-icon {
  font-size: 1rem;
}

/* СЛАЙДЕРЫ ДИАПАЗОНА */
.range-slider-container {
  position: relative;
}

.range-slider {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  margin: 0.5rem 0;
}

.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.range-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.4);
}

.range-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.range-values {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #9ca3af;
  margin-top: 0.25rem;
}

/* ЧЕКБОКСЫ */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.checkbox-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.checkbox-item input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-item input[type="checkbox"]:checked + .checkbox-custom {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  border-color: #7c3aed;
}

.checkbox-item input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.7rem;
  font-weight: bold;
}

.checkbox-label {
  font-size: 0.85rem;
  color: #d1d5db;
  user-select: none;
}

/* СЕЛЕКТ СТРАН */
.country-select {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 0.85rem;
  min-height: 120px;
}

.country-select option {
  background: rgba(26, 26, 46, 0.95);
  color: #ffffff;
  padding: 0.5rem;
}

.country-select option:checked {
  background: rgba(124, 58, 237, 0.3);
}

/* ФИЛЬТРЫ ХАРАКТЕРИСТИК */
.characteristics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.characteristic-filter {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.range-input-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.range-input {
  flex: 1;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: #ffffff;
  font-size: 0.8rem;
  text-align: center;
}

.range-input:focus {
  outline: none;
  border-color: rgba(114, 84, 228, 0.5);
  box-shadow: 0 0 0 2px rgba(114, 84, 228, 0.1);
}

.range-separator {
  color: #9ca3af;
  font-weight: bold;
}

.range-unit {
  font-size: 0.75rem;
  color: #9ca3af;
  min-width: 30px;
}

.range-slider-dual {
  position: relative;
  margin-top: 0.5rem;
}

.dual-range {
  position: absolute;
  width: 100%;
  height: 6px;
  background: transparent;
  outline: none;
  -webkit-appearance: none;
  pointer-events: none;
}

.dual-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  border-radius: 50%;
  cursor: pointer;
  pointer-events: all;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* СОРТИРОВКА */
.sort-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sort-control-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.sort-select {
  flex: 1;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 0.85rem;
}

.sort-select option {
  background: rgba(26, 26, 46, 0.95);
  color: #ffffff;
}

.sort-direction-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.sort-direction-btn:hover {
  background: rgba(114, 84, 228, 0.2);
  border-color: rgba(114, 84, 228, 0.5);
  transform: scale(1.05);
}

.direction-icon {
  font-size: 1.2rem;
  font-weight: bold;
}

.sort-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.sort-apply-btn,
.sort-clear-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.sort-apply-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.sort-apply-btn:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.sort-clear-btn {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.sort-clear-btn:hover {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.5);
}

/* ПРЕДУСТАНОВКИ */
.preset-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

.preset-btn {
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  color: #ffffff;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.preset-btn:hover {
  background: rgba(114, 84, 228, 0.15);
  border-color: rgba(114, 84, 228, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(114, 84, 228, 0.2);
}

/* АКТИВНЫЕ ФИЛЬТРЫ */
.active-filters {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 12px;
  display: none;
}

.active-filters-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #10b981;
  margin-bottom: 0.75rem;
}

.active-icon {
  font-size: 1rem;
}

.active-filters-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.active-filter-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 0.8rem;
  color: #ffffff;
}

.remove-filter-btn {
  width: 16px;
  height: 16px;
  background: rgba(239, 68, 68, 0.8);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 0.7rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-filter-btn:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

/* АДАПТИВНОСТЬ */
@media (max-width: 1200px) {
  .filter-grid {
    grid-template-columns: 1fr;
  }
  
  .characteristics-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
  
  .preset-buttons {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
}

@media (max-width: 768px) {
  .filter-sort-panel {
    margin: 0 -0.5rem 1.5rem -0.5rem;
    border-radius: 12px;
  }
  
  .filter-panel-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }
  
  .panel-controls {
    justify-content: center;
  }
  
  .filter-panel-content {
    padding: 1rem;
  }
  
  .filter-sections {
    gap: 1rem;
  }
  
  .characteristics-grid {
    grid-template-columns: 1fr;
  }
  
  .sort-control-group {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .sort-direction-btn {
    width: 100%;
    height: 40px;
  }
  
  .preset-buttons {
    grid-template-columns: 1fr;
  }
  
  .active-filters-list {
    flex-direction: column;
  }
  
  .active-filter-item {
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .filter-panel-header {
    padding: 0.5rem 0.75rem;
  }
  
  .panel-title {
    font-size: 1rem;
  }
  
  .filter-panel-content {
    padding: 0.75rem;
  }
  
  .filter-item {
    gap: 0.5rem;
  }
  
  .range-input-container {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .range-separator {
    display: none;
  }
}
